package com.jcloud.auth.controller;

import com.jcloud.common.config.CaptchaProperties;
import com.jcloud.common.dto.CaptchaRequest;
import com.jcloud.common.dto.CaptchaResponse;
import com.jcloud.common.result.Result;
import com.jcloud.common.util.CaptchaUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证码控制器
 * 提供验证码生成和验证接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "验证码管理", description = "验证码生成和验证相关接口")
@RestController
@RequestMapping("/auth/captcha")
@RequiredArgsConstructor
public class CaptchaController {
    
    private final CaptchaUtils captchaUtils;
    private final CaptchaProperties captchaProperties;
    
    @Operation(summary = "生成验证码", description = "生成图形验证码，返回验证码图片和标识")
    @GetMapping("/generate")
    public Result<CaptchaResponse> generateCaptcha() {
        // 检查是否启用验证码
        if (!captchaProperties.getEnabled()) {
            return Result.error("验证码功能未启用");
        }
        
        try {
            CaptchaResponse captchaResponse = captchaUtils.generateCaptcha();
            log.debug("生成验证码成功，ID: {}", captchaResponse.getCaptchaId());
            return Result.success("生成验证码成功", captchaResponse);
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return Result.error("生成验证码失败");
        }
    }
    
    @Operation(summary = "验证验证码", description = "验证用户输入的验证码是否正确")
    @PostMapping("/verify")
    public Result<Map<String, Object>> verifyCaptcha(@Valid @RequestBody CaptchaRequest request) {
        // 检查是否启用验证码
        if (!captchaProperties.getEnabled()) {
            Map<String, Object> data = new HashMap<>();
            data.put("valid", true);
            data.put("message", "验证码功能未启用，跳过验证");
            return Result.success("验证成功", data);
        }
        
        try {
            boolean isValid = captchaUtils.verifyCaptcha(request.getCaptchaId(), request.getCaptchaCode());
            
            Map<String, Object> data = new HashMap<>();
            data.put("valid", isValid);
            data.put("message", isValid ? "验证码验证成功" : "验证码验证失败");
            
            log.debug("验证码验证结果，ID: {}, 结果: {}", request.getCaptchaId(), isValid);
            return Result.success("验证完成", data);
            
        } catch (Exception e) {
            log.warn("验证码验证失败，ID: {}, 错误: {}", request.getCaptchaId(), e.getMessage());
            
            Map<String, Object> data = new HashMap<>();
            data.put("valid", false);
            data.put("message", e.getMessage());
            
            return Result.error(e.getMessage());
        }
    }
    
    @Operation(summary = "获取验证码配置", description = "获取当前验证码配置信息")
    @GetMapping("/config")
    public Result<Map<String, Object>> getCaptchaConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", captchaProperties.getEnabled());
        config.put("type", captchaProperties.getType());
        config.put("length", captchaProperties.getLength());
        config.put("expireTime", captchaProperties.getExpireTime());
        config.put("caseSensitive", captchaProperties.getCaseSensitive());
        
        // 图片配置
        Map<String, Object> imageConfig = new HashMap<>();
        imageConfig.put("width", captchaProperties.getImage().getWidth());
        imageConfig.put("height", captchaProperties.getImage().getHeight());
        config.put("image", imageConfig);
        
        return Result.success("获取验证码配置成功", config);
    }
    
    @Operation(summary = "使验证码失效", description = "手动使指定验证码失效")
    @DeleteMapping("/{captchaId}")
    public Result<Void> invalidateCaptcha(@PathVariable String captchaId) {
        try {
            captchaUtils.invalidateCaptcha(captchaId);
            log.debug("验证码已失效，ID: {}", captchaId);
            return Result.success("验证码已失效", null);
        } catch (Exception e) {
            log.error("使验证码失效失败，ID: {}", captchaId, e);
            return Result.error("操作失败");
        }
    }
}
