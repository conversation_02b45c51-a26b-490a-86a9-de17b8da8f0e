<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.FinancialStatsMapper">

    <!-- 财务统计数据结果映射 -->
    <resultMap id="FinancialStatsResultMap" type="com.jcloud.common.dto.FinancialStatsResponse">
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="stat_name" property="statName" jdbcType="VARCHAR"/>
        <result column="stat_value" property="statValue" jdbcType="DECIMAL"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 
        调用财务统计存储过程
        使用从库数据源查询财务统计数据
        异常处理：
        1. 存储过程不存在 - 抛出SQLException
        2. 参数格式错误 - 抛出DataAccessException
        3. 数据库连接异常 - 抛出DataAccessException
        4. 查询超时 - 抛出QueryTimeoutException
    -->
    <select id="getStatisticsReport" resultMap="FinancialStatsResultMap" timeout="30">
        {CALL GetStatisticsReport(
            #{startTime,jdbcType=VARCHAR,mode=IN}, 
            #{endTime,jdbcType=VARCHAR,mode=IN}, 
            #{includeAnchor,jdbcType=BOOLEAN,mode=IN}
        )}
    </select>

</mapper>