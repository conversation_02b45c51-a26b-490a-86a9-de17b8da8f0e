# jCloud 生产环境配置文件说明

## 概述

本配置文件 `application-prod.yml` 是基于 jCloud 项目实际技术栈重新设计的生产环境配置文件，完全符合项目的技术架构和性能要求。

## 技术栈版本

- **Java**: 21 (LTS)
- **Spring Boot**: 3.2.1
- **MyBatis-Flex**: 1.8.0
- **sa-token**: 1.37.0
- **MySQL**: 8.0.33
- **Redis**: 支持单机/集群/哨兵模式
- **Druid**: 1.2.20
- **Knife4j**: 4.5.0

## 主要配置模块

### 1. 服务器配置 (server)
- **端口**: 8081 (可通过环境变量 `SERVER_PORT` 配置)
- **Tomcat优化**: 线程池、连接数、压缩等性能优化
- **HTTP/2支持**: 提升网络性能
- **压缩配置**: 减少传输数据量

### 2. 数据源配置 (spring.datasource)
- **主从分离**: 支持读写分离架构
- **连接池优化**: Druid连接池生产环境参数调优
- **监控配置**: SQL监控、性能统计
- **安全配置**: 防火墙、SQL注入防护

### 3. Redis配置 (spring.data.redis)
- **多模式支持**: 单机/集群/哨兵模式
- **连接池优化**: Lettuce连接池参数调优
- **缓存配置**: 支持多级缓存策略

### 4. sa-token配置
- **安全策略**: 生产环境安全参数
- **Redis集成**: 使用Redis存储token
- **JWT支持**: 支持JWT token模式
- **并发控制**: 防止账号共享

### 5. MyBatis-Flex配置
- **性能优化**: 执行器、缓存、延迟加载配置
- **SQL监控**: 慢SQL检测和记录
- **逻辑删除**: 全局逻辑删除配置

### 6. 日志配置 (logging)
- **分级日志**: 不同组件的日志级别控制
- **文件滚动**: 日志文件大小和保留策略
- **格式优化**: 生产环境日志格式

### 7. 监控配置 (management)
- **健康检查**: 数据库、Redis、磁盘空间监控
- **指标收集**: Prometheus指标导出
- **端点安全**: 监控端点访问控制

### 8. 业务配置 (jcloud)
- **验证码**: 图形验证码生成和验证
- **文件上传**: 文件类型、大小限制
- **安全策略**: 密码策略、登录安全
- **缓存策略**: 业务缓存配置

### 9. API文档配置 (knife4j)
- **生产模式**: 生产环境API文档控制
- **安全配置**: 文档访问权限
- **SQL审计**: SQL执行审计和脱敏

## 环境变量说明

### 数据库相关
- `DB_MASTER_HOST`: 主库地址
- `DB_MASTER_PORT`: 主库端口
- `DB_MASTER_NAME`: 主库名称
- `DB_MASTER_USERNAME`: 主库用户名
- `DB_MASTER_PASSWORD`: 主库密码
- `DB_SLAVE_HOST`: 从库地址 (可选)
- `DB_SLAVE_PORT`: 从库端口 (可选)

### Redis相关
- `REDIS_HOST`: Redis地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码
- `REDIS_DATABASE`: Redis数据库编号

### 安全相关
- `SA_TOKEN_JWT_SECRET`: JWT密钥 (必须设置强密钥)
- `CORS_ALLOWED_ORIGINS`: 允许的跨域源

### 监控相关
- `DRUID_MONITOR_ENABLED`: 是否启用Druid监控
- `PROMETHEUS_ENABLED`: 是否启用Prometheus指标
- `API_DOC_ENABLED`: 是否启用API文档

### 性能相关
- `TOMCAT_MAX_THREADS`: Tomcat最大线程数
- `DB_MASTER_MAX_ACTIVE`: 主库最大连接数
- `REDIS_POOL_MAX_ACTIVE`: Redis连接池最大连接数

## 部署建议

### 1. 环境变量配置
建议使用环境变量或配置中心管理敏感配置，避免硬编码。

### 2. 资源监控
- 定期检查数据库连接池使用情况
- 监控Redis内存使用和连接数
- 关注JVM内存和GC情况

### 3. 安全加固
- 定期更换JWT密钥
- 限制API文档访问
- 启用SQL审计功能

### 4. 性能调优
- 根据实际负载调整连接池参数
- 优化缓存策略和过期时间
- 监控慢SQL并进行优化

## 注意事项

1. **密钥安全**: 生产环境必须使用强密钥，定期轮换
2. **监控配置**: 建议启用完整的监控和日志记录
3. **资源限制**: 根据服务器配置调整连接池和线程池大小
4. **备份策略**: 确保数据库和Redis的备份策略
5. **版本兼容**: 确保所有组件版本与配置文件兼容

## 更新记录

- **2025-01-07**: 基于实际技术栈重新设计生产环境配置
- 完全匹配项目使用的组件版本
- 添加了完整的性能优化配置
- 增强了安全配置和监控功能
