# jCloud 生产环境变量模板
# 复制此文件为 .env.prod 并填入实际值

# ================================
# 基础配置
# ================================
ENVIRONMENT=production
VERSION=1.0.0
SERVER_PORT=8081

# ================================
# 数据库配置 - 主库
# ================================
DB_MASTER_HOST=localhost
DB_MASTER_PORT=3306
DB_MASTER_NAME=jcloud
DB_MASTER_USERNAME=jcloud
DB_MASTER_PASSWORD=your_strong_password_here
DB_SSL=true

# 数据库连接池配置
DB_MASTER_INITIAL_SIZE=15
DB_MASTER_MIN_IDLE=15
DB_MASTER_MAX_ACTIVE=100
DB_MASTER_MAX_WAIT=60000

# ================================
# 数据库配置 - 从库 (可选)
# ================================
DB_SLAVE_HOST=localhost
DB_SLAVE_PORT=3306
DB_SLAVE_NAME=jcloud
DB_SLAVE_USERNAME=jcloud_readonly
DB_SLAVE_PASSWORD=your_readonly_password_here

# 从库连接池配置
DB_SLAVE_INITIAL_SIZE=10
DB_SLAVE_MIN_IDLE=10
DB_SLAVE_MAX_ACTIVE=80
DB_SLAVE_MAX_WAIT=30000

# ================================
# Redis配置
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DATABASE=0
REDIS_TIMEOUT=10s
REDIS_CONNECT_TIMEOUT=5s

# Redis连接池配置
REDIS_POOL_MAX_ACTIVE=100
REDIS_POOL_MAX_WAIT=5000ms
REDIS_POOL_MAX_IDLE=30
REDIS_POOL_MIN_IDLE=10

# Redis集群配置 (如果使用集群)
REDIS_CLUSTER_NODES=
REDIS_CLUSTER_MAX_REDIRECTS=3

# Redis哨兵配置 (如果使用哨兵)
REDIS_SENTINEL_MASTER=
REDIS_SENTINEL_NODES=
REDIS_SENTINEL_PASSWORD=

# ================================
# sa-token安全配置
# ================================
SA_TOKEN_NAME=Authorization
SA_TOKEN_TIMEOUT=7200
SA_TOKEN_ACTIVITY_TIMEOUT=1800
SA_TOKEN_CONCURRENT=false
SA_TOKEN_SHARE=false
SA_TOKEN_STYLE=uuid
SA_TOKEN_PREFIX=Bearer
# 重要：生产环境必须设置强密钥
SA_TOKEN_JWT_SECRET=your_very_strong_jwt_secret_key_here_min_32_chars

# ================================
# 跨域配置
# ================================
CORS_ALLOWED_ORIGINS=https://admin.jcloud.com,https://jcloud.com

# ================================
# Tomcat性能配置
# ================================
TOMCAT_MAX_THREADS=200
TOMCAT_MIN_SPARE_THREADS=20
TOMCAT_MAX_CONNECTIONS=8192
TOMCAT_ACCEPT_COUNT=200
TOMCAT_CONNECTION_TIMEOUT=20000
TOMCAT_MAX_HTTP_FORM_POST_SIZE=10MB
TOMCAT_MAX_SWALLOW_SIZE=10MB

# ================================
# 监控配置
# ================================
# Druid监控
DRUID_MONITOR_ENABLED=false
DRUID_USERNAME=admin
DRUID_PASSWORD=your_druid_password_here
DRUID_ALLOW_IPS=127.0.0.1,10.0.0.0/8,**********/12,***********/16
DRUID_DENY_IPS=

# Prometheus监控
PROMETHEUS_ENABLED=false

# API文档
API_DOC_ENABLED=false
API_DOC_PRODUCTION=true
API_DOC_CONTACT=<EMAIL>
API_DOC_URL=https://jcloud.com
API_DOC_TERMS=https://jcloud.com/terms
API_DOC_HOST=

# Actuator监控端点
ACTUATOR_ENDPOINTS=health,info,metrics
ACTUATOR_BASE_PATH=/actuator
ACTUATOR_CORS_ORIGINS=
ACTUATOR_HEALTH_SHOW_DETAILS=when-authorized
ACTUATOR_HEALTH_SHOW_COMPONENTS=when-authorized

# ================================
# 日志配置
# ================================
LOG_LEVEL_ROOT=INFO
LOG_LEVEL_JCLOUD=INFO
LOG_PATH=/app/logs
LOG_MAX_FILE_SIZE=100MB
LOG_MAX_HISTORY=30
LOG_TOTAL_SIZE_CAP=5GB

# ================================
# 业务配置
# ================================
# 验证码配置
CAPTCHA_ENABLED=true
CAPTCHA_TYPE=ALPHANUMERIC
CAPTCHA_LENGTH=4
CAPTCHA_EXPIRE=300
CAPTCHA_CASE_SENSITIVE=false
CAPTCHA_MAX_ATTEMPTS=5
CAPTCHA_LOCK_TIME=600
CAPTCHA_BRUTE_FORCE_PROTECTION=true

# 缓存配置
CACHE_TTL=3600

# 文件上传配置
FILE_UPLOAD_PATH=/app/uploads
FILE_MAX_SIZE=10MB
FILE_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar
FILE_ENABLE_TYPE_CHECK=true
FILE_TEMP_CLEANUP_HOURS=24

# 密码策略配置
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=32
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_DIGIT=true
PASSWORD_REQUIRE_SPECIAL=true
PASSWORD_SPECIAL_CHARS=!@#$%^&*()_+-=[]{}|;:,.<>?
PASSWORD_HISTORY_COUNT=5

# 登录安全配置
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCK_TIME=1800
LOGIN_CAPTCHA_AFTER_ATTEMPTS=3
LOGIN_ENABLE_IP_WHITELIST=false
LOGIN_IP_WHITELIST=

# 会话安全配置
SESSION_MAX_CONCURRENT=1
SESSION_TIMEOUT=7200
SESSION_PREVENT_FIXATION=true

# ================================
# SQL审计配置
# ================================
SQL_AUDIT_ENABLED=true
SQL_AUDIT_SLOW_THRESHOLD=2000
SQL_AUDIT_RETENTION_DAYS=90
SQL_AUDIT_FILE_PATH=/app/logs/sql-audit

# ================================
# 性能配置
# ================================
# 任务调度
TASK_CORE_SIZE=8
TASK_MAX_SIZE=20
TASK_QUEUE_CAPACITY=200
SCHEDULING_POOL_SIZE=5

# MyBatis配置
MYBATIS_STATEMENT_TIMEOUT=30
MYBATIS_FETCH_SIZE=100

# 健康检查
HEALTH_DISKSPACE_THRESHOLD=10GB

# HTTP配置
HTTP2_ENABLED=true

# ================================
# 构建信息 (CI/CD自动填充)
# ================================
BUILD_TIME=
GIT_COMMIT=
GIT_BRANCH=
