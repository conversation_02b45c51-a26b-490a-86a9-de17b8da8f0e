CREATE DEFINER=`vimbox`@`%` PROCEDURE `GetInviteUserStatistics`(IN p_invite_user_id INT, IN p_start_time INT, -- 开始时间（秒级时间戳）
  IN p_end_time INT)
BEGIN
  DECLARE actual_profit BIGINT;
  
  DECLARE
  EXIT HANDLER FOR SQLEXCEPTION  
    BEGIN
      ROLLBACK;
      RESIGNAL;
    END;
    
    
    -- 查询邀请用户在指定时间区间内的统计数据
    SELECT
      -- 总充值金额（已支付状态为2，按时间区间过滤）
      COALESCE(
        (
          SELECT
            SUM(vor.amount)
          FROM
            vim_order_recharge vor
            INNER JOIN vim_user u ON vor.uid = u.id
          WHERE
            vor.state = 2
            AND u.invite_user = p_invite_user_id
            AND vor.create_time >= p_start_time
            AND vor.create_time <= p_end_time
        ),
        0
      ) AS total_recharge,
      -- 总消费金额（支付订单 + 密钥订单，按时间区间过滤）
      COALESCE(
        (
          SELECT
            ABS(SUM(total_amount))
          FROM
            (
              SELECT
                SUM(vop.amount) AS total_amount
              FROM
                vim_order_pay vop
                INNER JOIN vim_user u ON vop.uid = u.id
              WHERE
                u.invite_user = p_invite_user_id
                AND vop.amount < 0
                AND vop.TIME >= p_start_time
                AND vop.TIME <= p_end_time UNION ALL
              SELECT
                SUM(vok.amount) AS total_amount
              FROM
                vim_order_key vok
                INNER JOIN vim_user u ON vok.uid = u.id
              WHERE
                u.invite_user = p_invite_user_id
                AND vok.TIME >= p_start_time
                AND vok.TIME <= p_end_time
            ) AS combined_consume
        ),
        0
      ) AS total_consume,
      -- 用户总数
      (SELECT COUNT(*) FROM vim_user WHERE invite_user = p_invite_user_id) AS user_count,
      -- 指定时间区间内新增用户数
      (SELECT COUNT(*) FROM vim_user WHERE invite_user = p_invite_user_id AND create_time >= p_start_time AND create_time <= p_end_time) AS period_new_user_count,
      -- 指定时间区间内新邀请的下级用户总数
      (SELECT COUNT(*) FROM vim_user WHERE invite_user = p_invite_user_id AND create_time >= p_start_time AND create_time <= p_end_time) AS period_new_invite_count,
      -- 总待发货金额（基于提货订单中的商品价值，按时间区间过滤）
      COALESCE(
        (
          SELECT
            SUM(vi.price_cost)
          FROM
            vim_order_box vob
            INNER JOIN vim_user u ON vob.uid = u.id
            INNER JOIN vim_item vi ON vi.id = vob.itemid
          WHERE
            u.invite_user = p_invite_user_id
            AND vob.state = 3
            AND vob.timestamp >= p_start_time
            AND vob.timestamp <= p_end_time
        ),
        0
      ) AS total_claim_amount,
      -- 总实际发货金额（实际发货成本，按时间区间过滤）
      COALESCE(
        (
          SELECT
            SUM(voc.cost)
          FROM
            vim_order_claim voc
            INNER JOIN vim_user u ON voc.uid = u.id
          WHERE
            u.invite_user = p_invite_user_id
            AND voc.cost IS NOT NULL
            AND voc.cost > 0
            AND voc.claim_time >= p_start_time
            AND voc.claim_time <= p_end_time
        ),
        0
      ) AS total_shipped_amount,
      -- 背包总价值
      COALESCE(
        (
          SELECT
            SUM(vi.price_cost)
          FROM
            vim_order_box vob
            INNER JOIN vim_user u ON vob.uid = u.id
            INNER JOIN vim_item vi ON vob.itemid = vi.id
          WHERE
            vob.state = 1 AND
            u.invite_user = p_invite_user_id
            AND vob.`timestamp` >= p_start_time
            AND vob.`timestamp` <= p_end_time
        ),
        0
      ) AS total_backpack_amount,
      -- 时间区间内下级用户总充值金额（已支付状态为2）
      COALESCE(
        (
          SELECT
            SUM(vor.amount)
          FROM
            vim_order_recharge vor
            INNER JOIN vim_user u ON vor.uid = u.id
          WHERE
            vor.state = 2
            AND u.invite_user = p_invite_user_id
            AND vor.create_time >= p_start_time
            AND vor.create_time <= p_end_time
        ),
        0
      ) AS period_total_recharge,
      -- 总流水
      COALESCE(
        (
          SELECT
            SUM(vor.amount)
          FROM
            vim_order_recharge vor
            INNER JOIN vim_user u ON vor.uid = u.id
          WHERE
            vor.state = 2
            AND u.invite_user = p_invite_user_id
            AND vor.create_time >= p_start_time
            AND vor.create_time <= p_end_time
        ),
        0
      ) AS total_turnover,
      -- 利润比（实际利润 / 总消费金额 × 100%）
      CASE 
        WHEN COALESCE(
          (
            SELECT
              ABS(SUM(total_amount))
            FROM
              (
                SELECT
                  SUM(vop.amount) AS total_amount
                FROM
                  vim_order_pay vop
                  INNER JOIN vim_user u ON vop.uid = u.id
                WHERE
                  u.invite_user = p_invite_user_id
                  AND vop.amount < 0
                  AND vop.TIME >= p_start_time
                  AND vop.TIME <= p_end_time UNION ALL
                SELECT
                  SUM(vok.amount) AS total_amount
                FROM
                  vim_order_key vok
                  INNER JOIN vim_user u ON vok.uid = u.id
                WHERE
                  u.invite_user = p_invite_user_id
                  AND vok.TIME >= p_start_time
                  AND vok.TIME <= p_end_time
              ) AS combined_consume
          ),
          0
        ) > 0 THEN
          (
            (
              COALESCE(
                (
                  SELECT
                    ABS(SUM(total_amount))
                  FROM
                    (
                      SELECT
                        SUM(vop.amount) AS total_amount
                      FROM
                        vim_order_pay vop
                        INNER JOIN vim_user u ON vop.uid = u.id
                      WHERE
                        u.invite_user = p_invite_user_id
                        AND vop.amount < 0
                        AND vop.TIME >= p_start_time
                        AND vop.TIME <= p_end_time UNION ALL
                      SELECT
                        SUM(vok.amount) AS total_amount
                      FROM
                        vim_order_key vok
                        INNER JOIN vim_user u ON vok.uid = u.id
                      WHERE
                        u.invite_user = p_invite_user_id
                        AND vok.TIME >= p_start_time
                        AND vok.TIME <= p_end_time
                    ) AS combined_consume
                ),
                0
              ) - COALESCE(
                (
                  SELECT
                    SUM(voc.cost)
                  FROM
                    vim_order_claim voc
                    INNER JOIN vim_user u ON voc.uid = u.id
                  WHERE
                    u.invite_user = p_invite_user_id
                    AND voc.cost IS NOT NULL
                    AND voc.cost > 0
                    AND voc.claim_time >= p_start_time
                    AND voc.claim_time <= p_end_time
                ),
                0
              ) - COALESCE(
                (
                  SELECT
                    SUM(vi.price_cost)
                  FROM
                    vim_order_box vob
                    INNER JOIN vim_user u ON vob.uid = u.id
                    INNER JOIN vim_item vi ON vi.id = vob.itemid
                  WHERE
                    u.invite_user = p_invite_user_id
                    AND vob.state = 3
                    AND vob.timestamp >= p_start_time
                    AND vob.timestamp <= p_end_time
                ),
                0
              ) - COALESCE(
                (
                  SELECT
                    SUM(vubv.price_show * vubv.count)
                  FROM
                    vim_user_backpack_view vubv
                    INNER JOIN vim_user u ON vubv.uid = u.id
                  WHERE
                    u.invite_user = p_invite_user_id
                    AND vubv.last_timestamp >= p_start_time
                    AND vubv.last_timestamp <= p_end_time
                ),
                0
              )
            ) / COALESCE(
              (
                SELECT
                  ABS(SUM(total_amount))
                FROM
                  (
                    SELECT
                      SUM(vop.amount) AS total_amount
                    FROM
                      vim_order_pay vop
                      INNER JOIN vim_user u ON vop.uid = u.id
                    WHERE
                      u.invite_user = p_invite_user_id
                      AND vop.amount < 0
                      AND vop.TIME >= p_start_time
                      AND vop.TIME <= p_end_time UNION ALL
                    SELECT
                      SUM(vok.amount) AS total_amount
                    FROM
                      vim_order_key vok
                      INNER JOIN vim_user u ON vok.uid = u.id
                    WHERE
                      u.invite_user = p_invite_user_id
                      AND vok.TIME >= p_start_time
                      AND vok.TIME <= p_end_time
                  ) AS combined_consume
              ),
              1
            ) * 100
          )
        ELSE 0
      END AS profit_ratio;
  END