CREATE DEFINER=`vimbox`@`%` PROCEDURE `GetStatisticsReport`(IN start_time VARCHAR(20), -- 支持时间戳或日期字符串
IN end_time VARCHAR(20),   -- 支持时间戳或日期字符串
IN include_anchor BOOLEAN)
BEGIN
    -- 声明变量
    DECLARE start_timestamp_ms BIGINT;
    DECLARE end_timestamp_ms BIGINT;
    DECLARE start_timestamp_s BIGINT;
    DECLARE end_timestamp_s BIGINT;

    -- 判断输入是否为纯数字（时间戳）
    DECLARE is_start_timestamp BOOLEAN;
    DECLARE is_end_timestamp BOOLEAN;

    -- 用户统计变量
    DECLARE user_visit_count INT DEFAULT 0;
    DECLARE user_recharge_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE user_recharge_count INT DEFAULT 0;
    DECLARE user_box_count INT DEFAULT 0;
    DECLARE user_prize_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE user_delivery_amount DECIMAL(10,2) DEFAULT 0.00;

    -- 主播统计变量
    DECLARE anchor_visit_count INT DEFAULT 0;
    DECLARE anchor_recharge_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE anchor_recharge_count INT DEFAULT 0;
    DECLARE anchor_box_count INT DEFAULT 0;
    DECLARE anchor_prize_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE anchor_delivery_amount DECIMAL(10,2) DEFAULT 0.00;

    -- 合计统计变量
    DECLARE total_visit_count INT DEFAULT 0;
    DECLARE total_recharge_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE total_recharge_count INT DEFAULT 0;
    DECLARE total_box_count INT DEFAULT 0;
    DECLARE total_prize_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE total_delivery_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE user_profit_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE anchor_profit_amount DECIMAL(10,2) DEFAULT 0.00;
    
    -- 总流水统计变量
    DECLARE user_total_flow DECIMAL(10,2) DEFAULT 0.00;
    DECLARE anchor_total_flow DECIMAL(10,2) DEFAULT 0.00;
    DECLARE total_flow DECIMAL(10,2) DEFAULT 0.00;
    
    -- 返奖率统计变量（调整数据类型以支持更大的返奖率值）
    DECLARE user_return_rate DECIMAL(8,2) DEFAULT 0.00;
    DECLARE anchor_return_rate DECIMAL(8,2) DEFAULT 0.00;
    DECLARE total_return_rate DECIMAL(8,2) DEFAULT 0.00;

    -- 其他业务统计变量
    DECLARE gift_key_count INT DEFAULT 0;
    DECLARE gift_prize_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE reported_delivery_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE actual_delivery_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE profit_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE user_actual_delivery_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE anchor_actual_delivery_amount DECIMAL(10,2) DEFAULT 0.00;
    
    -- 主播充值转移相关变量
    DECLARE anchor_transfer_amount DECIMAL(10,2) DEFAULT 0.00; -- 从主播转移到用户的充值金额
    DECLARE anchor_transfer_count INT DEFAULT 0; -- 从主播转移到用户的充值笔数
    DECLARE anchor_adjusted_recharge_amount DECIMAL(10,2) DEFAULT 0.00; -- 调整后的主播充值金额
    DECLARE anchor_adjusted_recharge_count INT DEFAULT 0; -- 调整后的主播充值笔数
    DECLARE user_adjusted_recharge_amount DECIMAL(10,2) DEFAULT 0.00; -- 调整后的用户充值金额
    DECLARE user_adjusted_recharge_count INT DEFAULT 0; -- 调整后的用户充值笔数

    -- 临时计算变量
    DECLARE roll_key_count INT DEFAULT 0;
    DECLARE cdk_key_count INT DEFAULT 0;
    DECLARE roll_prize_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE cdk_prize_amount DECIMAL(10,2) DEFAULT 0.00;

    -- 判断输入是否为时间戳（纯数字）
    SET is_start_timestamp = (start_time REGEXP '^[0-9]+$');
    SET is_end_timestamp = (end_time REGEXP '^[0-9]+$');

    -- 时间戳转换（毫秒和秒）
    IF is_start_timestamp THEN
        -- 如果是时间戳输入
        IF LENGTH(start_time) = 10 THEN
            -- 秒级时间戳
            SET start_timestamp_s = CAST(start_time AS UNSIGNED);
            SET start_timestamp_ms = start_timestamp_s * 1000;
        ELSEIF LENGTH(start_time) = 13 THEN
            -- 毫秒级时间戳
            SET start_timestamp_ms = CAST(start_time AS UNSIGNED);
            SET start_timestamp_s = FLOOR(start_timestamp_ms / 1000);
        ELSE
            -- 其他长度视为秒级
            SET start_timestamp_s = CAST(start_time AS UNSIGNED);
            SET start_timestamp_ms = start_timestamp_s * 1000;
        END IF;
    ELSE
        -- 如果是日期字符串输入
        SET start_timestamp_s = UNIX_TIMESTAMP(STR_TO_DATE(start_time, '%Y-%m-%d %H:%i:%s'));
        SET start_timestamp_ms = start_timestamp_s * 1000;
    END IF;

    IF is_end_timestamp THEN
        -- 如果是时间戳输入
        IF LENGTH(end_time) = 10 THEN
            -- 秒级时间戳
            SET end_timestamp_s = CAST(end_time AS UNSIGNED);
            SET end_timestamp_ms = end_timestamp_s * 1000;
        ELSEIF LENGTH(end_time) = 13 THEN
            -- 毫秒级时间戳
            SET end_timestamp_ms = CAST(end_time AS UNSIGNED);
            SET end_timestamp_s = FLOOR(end_timestamp_ms / 1000);
        ELSE
            -- 其他长度视为秒级
            SET end_timestamp_s = CAST(end_time AS UNSIGNED);
            SET end_timestamp_ms = end_timestamp_s * 1000;
        END IF;
    ELSE
        -- 如果是日期字符串输入
        SET end_timestamp_s = UNIX_TIMESTAMP(STR_TO_DATE(end_time, '%Y-%m-%d %H:%i:%s'));
        SET end_timestamp_ms = end_timestamp_s * 1000;
    END IF;

    -- ========== 用户相关统计 ==========

    -- 用户访问数（基于最后登录时间的用户数）
    -- vim_user.last_login_time 是 int 类型，秒级时间戳
    SELECT COUNT(*) INTO user_visit_count
    FROM vim_user vu
    WHERE vu.last_login_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND vu.identity = 1; -- 仅普通用户

    -- 用户充值金额和笔数（原始金额）
    -- vim_order_recharge.create_time 是 int 类型，秒级时间戳
    SELECT
    COALESCE(SUM(CASE WHEN vu.identity = 1 OR vu.id IS NULL THEN vor.amount ELSE 0 END), 0.00),
    COUNT(CASE WHEN vu.identity = 1 OR vu.id IS NULL THEN vor.id ELSE NULL END)
    INTO user_recharge_amount, user_recharge_count
    FROM vim_order_recharge vor
    LEFT JOIN vim_user vu ON vor.uid = vu.id
    WHERE vor.create_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND vor.state = 2; -- 已支付
    
    -- 用户开箱次数
    -- vim_order_box.timestamp 是 varchar 类型，需要转换为数值进行比较
    SELECT COUNT(CASE WHEN vu.identity = 1 THEN vob.oid ELSE NULL END) INTO user_box_count
    FROM vim_order_box vob
    LEFT JOIN vim_user vu ON vob.uid = vu.id
    WHERE vob.timestamp IS NOT NULL
    AND vob.timestamp != ''
    AND vob.timestamp REGEXP '^[0-9]+$' -- 确保是数字格式
    AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
    AND vob.type = 1; -- 开箱类型

    -- 用户获奖金额（仅已回收的订单，且确实中奖的记录）
    SELECT COALESCE(SUM(CASE WHEN vu.identity = 1 AND vob.price > 0 THEN vob.price ELSE 0 END), 0.00) INTO user_prize_amount
    FROM vim_order_box vob
    INNER JOIN vim_user vu ON vob.uid = vu.id
    WHERE vob.timestamp IS NOT NULL
    AND vob.timestamp != ''
    AND vob.timestamp REGEXP '^[0-9]+$' -- 确保是数字格式
    AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
    AND vob.type = 1 -- 开箱类型
    AND vob.state = 2 -- 仅已回收
    AND vob.price > 0; -- 确保有实际奖品价值

    -- 用户待发货金额
    -- vim_order_claim.creat_time 是 bigint 类型，秒级时间戳
    SELECT COALESCE(SUM(CASE WHEN vu.identity = 1 AND vi.price_cost IS NOT NULL AND vi.price_cost > 0 THEN vi.price_cost ELSE 0 END), 0.00) INTO user_delivery_amount
    FROM vim_order_claim voc
    LEFT JOIN vim_user vu ON voc.uid = vu.id
    LEFT JOIN vim_item vi on vi.id = voc.itemid
    WHERE voc.creat_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND voc.state IN (1); -- 所有提货状态：发货中、已发货、发货失败

    -- ========== 主播充值转移逻辑计算 ==========
    
    IF include_anchor THEN
        -- 计算需要从主播转移到用户的充值金额（全额转移）
        -- 查询每个主播的充值金额和发货金额，筛选出充值金额大于发货金额的主播
        -- 将这些主播的整个充值金额都转移到用户充值统计中
        SELECT 
            COALESCE(SUM(transfer_data.recharge_amount), 0.00),
            COALESCE(SUM(transfer_data.recharge_count), 0)
        INTO anchor_transfer_amount, anchor_transfer_count
        FROM (
            SELECT 
                anchor_recharge.uid,
                anchor_recharge.recharge_amount, -- 转移整个充值金额，不是差额
                anchor_recharge.recharge_count,  -- 转移整个充值笔数，不是差额
                COALESCE(anchor_delivery.delivery_amount, 0.00) AS delivery_amount
            FROM (
                -- 获取每个主播的充值金额和笔数
                SELECT 
                    vor.uid,
                    SUM(vor.amount) AS recharge_amount,
                    COUNT(vor.id) AS recharge_count
                FROM vim_order_recharge vor
                LEFT JOIN vim_user vu ON vor.uid = vu.id
                WHERE vor.create_time BETWEEN start_timestamp_s AND end_timestamp_s
                AND vor.state = 2 -- 已支付
                AND vu.identity IN (2, 3) -- 主播身份
                GROUP BY vor.uid
            ) anchor_recharge
            LEFT JOIN (
                -- 获取每个主播的发货金额（使用实际发货成本）
                SELECT 
                    voc.uid,
                    SUM(CASE WHEN voc.cost IS NOT NULL AND voc.cost > 0 THEN voc.cost ELSE 0 END) AS delivery_amount
                FROM vim_order_claim voc
                WHERE voc.creat_time BETWEEN start_timestamp_s AND end_timestamp_s
                AND voc.state IN (1, 2, 3) -- 所有提货状态
                GROUP BY voc.uid
            ) anchor_delivery ON anchor_recharge.uid = anchor_delivery.uid
            WHERE anchor_recharge.recharge_amount < COALESCE(anchor_delivery.delivery_amount, 0.00)
        ) transfer_data;
    END IF;

    -- 计算调整后的用户充值金额和笔数（加上从主播转移的部分）
    SET user_adjusted_recharge_amount = user_recharge_amount + anchor_transfer_amount;
    SET user_adjusted_recharge_count = user_recharge_count + anchor_transfer_count;

    -- ========== 主播相关统计（如果包含主播数据）==========

    IF include_anchor THEN
        -- 主播访问数（基于最后登录时间）
        SELECT COUNT(*) INTO anchor_visit_count
        FROM vim_user vu
        WHERE vu.last_login_time BETWEEN start_timestamp_s AND end_timestamp_s
        AND vu.identity IN (2, 3); -- 线上主播和线下主播

        -- 主播充值金额和笔数（原始金额）
        SELECT
            COALESCE(SUM(CASE WHEN vu.identity IN (2, 3) THEN vor.amount ELSE 0 END), 0.00),
            COUNT(CASE WHEN vu.identity IN (2, 3) THEN vor.id ELSE NULL END)
        INTO anchor_recharge_amount, anchor_recharge_count
        FROM vim_order_recharge vor
        LEFT JOIN vim_user vu ON vor.uid = vu.id
        WHERE vor.create_time BETWEEN start_timestamp_s AND end_timestamp_s
        AND vor.state = 2; -- 已支付
        
        -- 计算调整后的主播充值金额和笔数（减去转移部分）
        SET anchor_adjusted_recharge_amount = anchor_recharge_amount - anchor_transfer_amount;
        SET anchor_adjusted_recharge_count = anchor_recharge_count - anchor_transfer_count;

        -- 主播开箱次数
        SELECT COUNT(CASE WHEN vu.identity IN (2, 3) THEN vob.oid ELSE NULL END) INTO anchor_box_count
        FROM vim_order_box vob
        LEFT JOIN vim_user vu ON vob.uid = vu.id
        WHERE vob.timestamp IS NOT NULL
        AND vob.timestamp != ''
        AND vob.timestamp REGEXP '^[0-9]+$' -- 确保是数字格式
        AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
        AND vob.type = 1; -- 开箱类型

        -- 主播获奖金额（仅已回收的订单，且确实中奖的记录）
        SELECT COALESCE(SUM(CASE WHEN vu.identity IN (2, 3) AND vob.price > 0 THEN vob.price ELSE 0 END), 0.00) INTO anchor_prize_amount
        FROM vim_order_box vob
        LEFT JOIN vim_user vu ON vob.uid = vu.id
        WHERE vob.timestamp IS NOT NULL
        AND vob.timestamp != ''
        AND vob.timestamp REGEXP '^[0-9]+$' -- 确保是数字格式
        AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
        AND vob.type = 1 -- 开箱类型
        AND vob.state = 2 -- 仅已回收
        AND vob.price > 0; -- 确保有实际奖品价值

        -- 主播待发货金额（所有提货订单的预计发货成本）
        SELECT COALESCE(SUM(CASE WHEN vu.identity IN (2, 3) AND vi.price_cost IS NOT NULL AND vi.price_cost > 0 THEN vi.price_cost ELSE 0 END), 0.00) INTO anchor_delivery_amount
        FROM vim_order_claim voc
        LEFT JOIN vim_user vu ON voc.uid = vu.id
        LEFT JOIN vim_item vi ON voc.itemid = vi.id
        WHERE voc.creat_time BETWEEN start_timestamp_s AND end_timestamp_s
        AND voc.state IN (1); -- 所有提货状态：发货中、已发货、发货失败
    END IF;

    -- ========== 合计统计 ==========
    SET total_visit_count = user_visit_count + anchor_visit_count;
    SET total_recharge_amount = user_adjusted_recharge_amount + anchor_adjusted_recharge_amount;
    SET total_recharge_count = user_adjusted_recharge_count + anchor_adjusted_recharge_count;
    SET total_box_count = user_box_count + anchor_box_count;
    SET total_prize_amount = user_prize_amount + anchor_prize_amount;
    SET total_delivery_amount = user_delivery_amount + anchor_delivery_amount;
    
    -- 计算用户利润金额（用户充值金额 - 用户实际发货金额）
    -- 获取用户实际发货金额
    SELECT
        COALESCE(SUM(CASE WHEN vu.identity = 1 AND voc.cost IS NOT NULL AND voc.cost > 0 THEN voc.cost ELSE 0 END), 0.00)
    INTO user_actual_delivery_amount
    FROM vim_order_claim voc
    LEFT JOIN vim_user vu ON voc.uid = vu.id
    WHERE voc.claim_time IS NOT NULL
    AND voc.claim_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND voc.state = 2; -- 仅已发货成功
    
    SET user_profit_amount = user_recharge_amount - user_actual_delivery_amount;
    
    -- 计算主播利润金额（主播充值金额 - 主播实际发货金额）
    -- 获取主播实际发货金额
    SELECT
        COALESCE(SUM(CASE WHEN vu.identity IN (2, 3) AND voc.cost IS NOT NULL AND voc.cost > 0 THEN voc.cost ELSE 0 END), 0.00)
    INTO anchor_actual_delivery_amount
    FROM vim_order_claim voc
    LEFT JOIN vim_user vu ON voc.uid = vu.id
    WHERE voc.claim_time IS NOT NULL
    AND voc.claim_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND voc.state = 2; -- 仅已发货成功
    
    SET anchor_profit_amount = anchor_recharge_amount - anchor_actual_delivery_amount;

    -- ========== 其他业务统计 ==========

    -- 赠送钥匙数（Roll房发放 + CDK发放）
    -- Roll房发放的钥匙数（通过vim_order_box表type=7的记录统计）
    -- SELECT COUNT(vob.oid) INTO roll_key_count
    -- FROM vim_order_box vob
    -- WHERE vob.timestamp IS NOT NULL
    -- AND vob.timestamp != ''
    -- AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
    -- AND vob.type = 7; -- Roll房类型
       SELECT
         SUM(vok.amount) INTO roll_key_count
       FROM
         vim_order_key vok
       WHERE
         (vok.info LIKE 'ROLL%' OR vok.info LIKE 'CDK%' OR vok.info LIKE '欧皇%')
         AND vok.time BETWEEN start_timestamp_s AND end_timestamp_s;

    -- CDK发放的钥匙数（type=2为钥匙类型）
    -- vim_cdk.use_time 是 int 类型，秒级时间戳
    -- SELECT COUNT(vc.id) INTO cdk_key_count
    -- FROM vim_cdk vc
    -- WHERE vc.use_time IS NOT NULL
    -- AND vc.use_time BETWEEN start_timestamp_s AND end_timestamp_s
    -- AND vc.type = 2 -- 钥匙类型
    -- AND vc.state = 1; -- 已兑换

    SET gift_key_count = roll_key_count + cdk_key_count;

    -- 赠送奖品金额（Roll房发放 + CDK发放）
    -- Roll房发放的奖品金额
    -- vim_new_roll.end_time 是 bigint 类型，毫秒级时间戳
--     SELECT COALESCE(SUM(vic.price_show), 0.00) INTO roll_prize_amount
--     FROM vim_new_rollitem vnr
--     LEFT JOIN vim_new_roll vnroll ON vnr.roll_id = vnroll.id
--     LEFT JOIN vim_item vic ON vnr.item = vic.id
--     WHERE vnroll.end_time IS NOT NULL
--     AND vnroll.end_time BETWEEN start_timestamp_s AND end_timestamp_s
--     AND vnr.user_id IS NOT NULL -- 有中奖用户
--     AND vic.price_show IS NOT NULL; -- 确保有价格信息

    -- CDK发放的奖品金额（type=3为物品类型）
    SELECT COALESCE(SUM(vic.price_show), 0.00) INTO cdk_prize_amount
    FROM vim_cdk vc
    LEFT JOIN vim_item vic ON vc.value = vic.id
    WHERE vc.use_time IS NOT NULL
    AND vc.use_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND vc.type = 3 -- 物品类型
    AND vc.state = 1 -- 已兑换
    AND vic.price_show IS NOT NULL; -- 确保有价格信息

    SET gift_prize_amount = roll_prize_amount + cdk_prize_amount;

    -- 填报发货金额（所有提货订单的商品价值总和）
    -- 填报发货金额 = 用户提货金额 + 主播提货金额
    SET reported_delivery_amount = user_delivery_amount + anchor_delivery_amount;

    -- 实际发货金额（仅已发货成功的实际成本）
    -- 关键修复：使用claim_time（发货时间）而不是creat_time（提交时间）
    -- vim_order_claim.claim_time 是 int 类型，秒级时间戳
    SELECT
        COALESCE(SUM(CASE WHEN voc.cost IS NOT NULL AND voc.cost > 0 THEN voc.cost ELSE 0 END), 0.00)
    INTO actual_delivery_amount
    FROM vim_order_claim voc
    WHERE voc.claim_time IS NOT NULL
    AND voc.claim_time BETWEEN start_timestamp_s AND end_timestamp_s
    AND voc.state = 2; -- 仅已发货成功

    -- 利润金额计算（调整后的总充值金额 - 实际发货金额）
--     SET profit_amount = total_recharge_amount - actual_delivery_amount;
    SET profit_amount = user_recharge_amount - actual_delivery_amount;
    
    -- 总流水计算（总流水 = 开箱、升级、融合订单关联的商品价格总和）
    -- 用户总流水（开箱、升级、融合订单的商品价格）
    SELECT COALESCE(SUM(CASE WHEN vu.identity = 1 THEN vob.price ELSE 0 END), 0.00) INTO user_total_flow
    FROM vim_order_box vob
    LEFT JOIN vim_user vu ON vob.uid = vu.id
    WHERE vob.timestamp IS NOT NULL
    AND vob.timestamp != ''
    AND vob.timestamp REGEXP '^[0-9]+$'
    AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
    AND vob.type IN (1, 2, 3) -- 1=开箱, 2=升级, 3=融合
    AND vob.price > 0;
    
    -- 主播总流水（开箱、升级、融合订单的商品价格）
    IF include_anchor THEN
        SELECT COALESCE(SUM(CASE WHEN vu.identity IN (2, 3) THEN vob.price ELSE 0 END), 0.00) INTO anchor_total_flow
        FROM vim_order_box vob
        LEFT JOIN vim_user vu ON vob.uid = vu.id
        WHERE vob.timestamp IS NOT NULL
        AND vob.timestamp != ''
        AND vob.timestamp REGEXP '^[0-9]+$'
        AND CAST(vob.timestamp AS UNSIGNED) BETWEEN start_timestamp_ms AND end_timestamp_ms
        AND vob.type IN (1, 2, 3) -- 1=开箱, 2=升级, 3=融合
        AND vob.price > 0;
    END IF;
    
    -- 合计总流水
    SET total_flow = user_total_flow + anchor_total_flow;
    
    -- 返奖率计算（返奖率 = 获奖金额 / 总流水 * 100）
    -- 注意：获奖金额应该只包含通过充值开箱获得的奖品，不包含赠送奖品
    IF user_total_flow > 0 THEN
        SET user_return_rate = (user_prize_amount / user_total_flow) * 100;
    END IF;
    
    IF anchor_total_flow > 0 THEN
        SET anchor_return_rate = (anchor_prize_amount / anchor_total_flow) * 100;
    END IF;
    
    IF total_flow > 0 THEN
        SET total_return_rate = (total_prize_amount / total_flow) * 100;
    END IF;

    -- ========== 返回结构化结果集 ==========
    SELECT
        '用户相关统计' AS category,
        '用户访问数' AS stat_name,
        user_visit_count AS stat_value,
        '人' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户充值金额' AS stat_name,
        user_adjusted_recharge_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户充值笔数' AS stat_name,
        user_adjusted_recharge_count AS stat_value,
        '笔' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户开箱次数' AS stat_name,
        user_box_count AS stat_value,
        '次' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户获奖金额' AS stat_name,
        user_prize_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户待发货金额' AS stat_name,
        user_delivery_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户总流水' AS stat_name,
        user_total_flow AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '用户相关统计' AS category,
        '用户返奖率' AS stat_name,
        user_return_rate AS stat_value,
        '%' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播访问数' AS stat_name,
        anchor_visit_count AS stat_value,
        '人' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播充值金额' AS stat_name,
        anchor_adjusted_recharge_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播充值笔数' AS stat_name,
        anchor_adjusted_recharge_count AS stat_value,
        '笔' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播开箱次数' AS stat_name,
        anchor_box_count AS stat_value,
        '次' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播获奖金额' AS stat_name,
        anchor_prize_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播待发货金额' AS stat_name,
        anchor_delivery_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播总流水' AS stat_name,
        anchor_total_flow AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '主播相关统计' AS category,
        '主播返奖率' AS stat_name,
        anchor_return_rate AS stat_value,
        '%' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计访问数' AS stat_name,
        total_visit_count AS stat_value,
        '人' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计充值金额' AS stat_name,
        total_recharge_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计充值笔数' AS stat_name,
        total_recharge_count AS stat_value,
        '笔' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计开箱次数' AS stat_name,
        total_box_count AS stat_value,
        '次' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计获奖金额' AS stat_name,
        total_prize_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计待发货金额' AS stat_name,
        total_delivery_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '用户利润金额' AS stat_name,
        user_profit_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '主播利润金额' AS stat_name,
        anchor_profit_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计总流水' AS stat_name,
        total_flow AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '合计统计' AS category,
        '合计返奖率' AS stat_name,
        total_return_rate AS stat_value,
        '%' AS unit
    UNION ALL
    SELECT
        '其他业务统计' AS category,
        '赠送钥匙数' AS stat_name,
        gift_key_count AS stat_value,
        '个' AS unit
    UNION ALL
    SELECT
        '其他业务统计' AS category,
        '赠送奖品金额' AS stat_name,
        gift_prize_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '其他业务统计' AS category,
        '填报发货金额' AS stat_name,
        reported_delivery_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '其他业务统计' AS category,
        '实际发货金额' AS stat_name,
        actual_delivery_amount AS stat_value,
        '元' AS unit
    UNION ALL
    SELECT
        '其他业务统计' AS category,
        '实际利润金额' AS stat_name,
        profit_amount AS stat_value,
        '元' AS unit
    ORDER BY
        CASE category
            WHEN '用户相关统计' THEN 1
            WHEN '主播相关统计' THEN 2
            WHEN '合计统计' THEN 3
            WHEN '其他业务统计' THEN 4
        END,
        stat_name;

END