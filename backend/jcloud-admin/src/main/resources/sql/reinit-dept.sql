-- 重新初始化部门数据脚本
-- 用于修复部门数据不完整的问题

-- 清理现有部门数据（保留用户关联）
DELETE FROM `sys_dept` WHERE `tenant_id` = 1;

-- 重新插入完整的部门结构
INSERT INTO `sys_dept` (`id`, `tenant_id`, `parent_id`, `dept_code`, `dept_name`, `status`, `sort_order`, `remark`, `create_by`, `create_time`) VALUES
-- 根部门
(1, 1, 0, 'COMPANY', '总公司', 1, 1, '总公司', 0, NOW()),

-- 一级部门
(2, 1, 1, 'TECH', '技术部', 1, 11, '技术研发部门', 0, NOW()),
(3, 1, 1, 'MARKET', '市场部', 1, 12, '市场营销部门', 0, NOW()),
(4, 1, 1, 'HR', '人事部', 1, 13, '人力资源部门', 0, NOW()),
(5, 1, 1, 'FINANCE', '财务部', 1, 14, '财务管理部门', 0, NOW()),

-- 技术部下属部门
(6, 1, 2, 'TECH_DEV', '开发组', 1, 111, '软件开发组', 0, NOW()),
(7, 1, 2, 'TECH_TEST', '测试组', 1, 112, '软件测试组', 0, NOW()),
(8, 1, 2, 'TECH_OPS', '运维组', 1, 113, '系统运维组', 0, NOW()),

-- 开发组下属小组
(9, 1, 6, 'TECH_DEV_FE', '前端组', 1, 1111, '前端开发小组', 0, NOW()),
(10, 1, 6, 'TECH_DEV_BE', '后端组', 1, 1112, '后端开发小组', 0, NOW()),

-- 市场部下属部门
(11, 1, 3, 'MARKET_SALES', '销售组', 1, 121, '销售团队', 0, NOW()),
(12, 1, 3, 'MARKET_PR', '推广组', 1, 122, '市场推广组', 0, NOW()),

-- 人事部下属部门
(13, 1, 4, 'HR_RECRUIT', '招聘组', 1, 131, '人才招聘组', 0, NOW()),
(14, 1, 4, 'HR_TRAIN', '培训组', 1, 132, '员工培训组', 0, NOW());

-- 更新用户部门关联（确保用户有有效的部门）
UPDATE `sys_user_dept` SET `dept_id` = 2 WHERE `dept_id` NOT IN (SELECT `id` FROM `sys_dept` WHERE `tenant_id` = 1);

-- 重置自增ID
ALTER TABLE `sys_dept` AUTO_INCREMENT = 15;

-- 验证数据
SELECT 
    d.id,
    d.parent_id,
    d.dept_name,
    d.sort_order,
    CASE 
        WHEN d.parent_id = 0 THEN '根部门'
        WHEN p.parent_id = 0 THEN '一级部门'
        WHEN pp.parent_id = 0 THEN '二级部门'
        ELSE '三级部门'
    END as level
FROM `sys_dept` d
LEFT JOIN `sys_dept` p ON d.parent_id = p.id
LEFT JOIN `sys_dept` pp ON p.parent_id = pp.id
WHERE d.tenant_id = 1
ORDER BY d.sort_order;
