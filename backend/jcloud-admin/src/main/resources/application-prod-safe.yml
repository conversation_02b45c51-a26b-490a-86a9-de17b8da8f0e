# 备用安全配置 - 如果主配置仍有问题，可以使用此配置
spring:
  # 数据源配置 - 最小化配置，避免复杂参数
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: *********************************************************************************************************************************************************
        username: voltskins
        password: ShbAeEVw7RNh8arDzjN4eZhsh@
        # 最小化连接池配置
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 10000
        # 简化的连接检测配置
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 60000
        validation-query: SELECT 1
        validation-query-timeout: 3
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        # 基础配置
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,slf4j
        # 连接泄露检测
        remove-abandoned: true
        remove-abandoned-timeout: 300
        log-abandoned: true
      slave:
        url: **********************************************************************************************************************************************
        username: voltskins
        password: ShbAeEVw7RNh8arDzjN4eZhsh@
        # 最小化连接池配置
        initial-size: 3
        min-idle: 3
        max-active: 15
        max-wait: 8000
        # 简化的连接检测配置
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 60000
        validation-query: SELECT 1
        validation-query-timeout: 3
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        # 基础配置
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,slf4j
        # 连接泄露检测
        remove-abandoned: true
        remove-abandoned-timeout: 300
        log-abandoned: true

# MyBatis-Flex配置 - 简化版本
mybatis-flex:
  type-aliases-package: com.jcloud.**.entity
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-statement-timeout: 30
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    default-executor-type: simple

# 日志配置
logging:
  level:
    com.jcloud: info
    com.alibaba.druid: warn
    druid.sql: warn
