package com.jcloud.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * CORS跨域过滤器 - 基于Context7最佳实践
 * 作为WebMvcConfigurer的补充，确保所有请求都能正确处理CORS
 * 特别处理OPTIONS预检请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("jcloudCorsFilter") // 使用自定义名称避免与Spring Security冲突
@Order(1) // 确保在其他过滤器之前执行
public class CorsFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String origin = httpRequest.getHeader("Origin");
        
        // 检查是否为允许的域名
        if (isAllowedOrigin(origin)) {
            // 设置允许的域名
            httpResponse.setHeader("Access-Control-Allow-Origin", origin);
        }
        
        // 设置允许的方法
        httpResponse.setHeader("Access-Control-Allow-Methods", 
            "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH");
        
        // 设置允许的请求头
        httpResponse.setHeader("Access-Control-Allow-Headers", 
            "Authorization, Content-Type, Accept, Origin, " +
            "Access-Control-Request-Method, Access-Control-Request-Headers, " +
            "X-Requested-With, Tenant-Id, X-Forwarded-For, X-Forwarded-Proto, " +
            "Cache-Control, Pragma");
        
        // 设置允许暴露的响应头
        httpResponse.setHeader("Access-Control-Expose-Headers", 
            "Authorization, Content-Type, Content-Length, " +
            "X-Total-Count, X-Current-Page, X-Page-Size");
        
        // 允许携带凭证
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        
        // 设置预检请求缓存时间
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        
        // 处理OPTIONS预检请求
        if ("OPTIONS".equalsIgnoreCase(httpRequest.getMethod())) {
            httpResponse.setStatus(HttpServletResponse.SC_OK);
            return;
        }
        
        // 继续执行后续过滤器
        chain.doFilter(request, response);
    }
    
    /**
     * 检查是否为允许的域名
     */
    private boolean isAllowedOrigin(String origin) {
        if (origin == null) {
            return false;
        }
        // 允许的域名列表
        return origin.equals("http://localhost:5173") ||
               origin.equals("http://localhost:5174") ||
               origin.equals("http://localhost:3000") ||
               origin.equals("http://127.0.0.1:5173") ||
               origin.equals("http://127.0.0.1:5174") ||
               origin.equals("http://127.0.0.1:3000");
    }
}
