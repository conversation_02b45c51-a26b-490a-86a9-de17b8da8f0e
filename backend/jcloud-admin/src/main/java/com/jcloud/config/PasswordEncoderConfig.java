package com.jcloud.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码编码器配置 - 独立于Spring Security
 * 仅使用BCryptPasswordEncoder进行密码加密，不启用Spring Security Web功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class PasswordEncoderConfig {

    /**
     * BCrypt密码编码器Bean
     * 用于密码加密和验证，不依赖Spring Security Web配置
     */
    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
