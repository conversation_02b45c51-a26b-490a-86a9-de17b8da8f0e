package com.jcloud.admin.mapper;

import com.jcloud.common.dto.TenantUserDTO;
import com.jcloud.common.entity.SysTenantUser;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户用户关联Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysTenantUserMapper extends BaseMapper<SysTenantUser> {
    
    /**
     * 获取租户用户列表（包含用户详细信息）
     * 
     * @param tenantId 租户ID
     * @return 租户用户列表
     */
    @Select("""
        SELECT 
            tu.id,
            tu.tenant_id,
            tu.user_id,
            u.username,
            u.nickname,
            u.real_name,
            u.email,
            u.phone,
            tu.user_role,
            CASE tu.user_role 
                WHEN 1 THEN '管理员' 
                WHEN 2 THEN '普通用户' 
                ELSE '未知' 
            END as user_role_name,
            tu.assign_time,
            tu.status,
            CASE tu.status 
                WHEN 1 THEN '启用' 
                WHEN 0 THEN '禁用' 
                ELSE '未知' 
            END as status_name,
            tu.create_time
        FROM sys_tenant_user tu
        LEFT JOIN sys_user u ON tu.user_id = u.id
        WHERE tu.tenant_id = #{tenantId} 
            AND tu.deleted = 0 
            AND u.deleted = 0
        ORDER BY tu.create_time DESC
    """)
    List<TenantUserDTO> getTenantUsersWithDetails(@Param("tenantId") Long tenantId);
    
    /**
     * 获取可分配给租户的用户列表（排除已分配的用户）
     * 
     * @param tenantId 租户ID
     * @return 可分配用户列表
     */
    @Select("""
        SELECT 
            u.id,
            u.username,
            u.nickname,
            u.real_name,
            u.email,
            u.phone,
            u.status
        FROM sys_user u
        WHERE u.deleted = 0 
            AND u.status = 1
            AND u.id NOT IN (
                SELECT tu.user_id 
                FROM sys_tenant_user tu 
                WHERE tu.tenant_id = #{tenantId} 
                    AND tu.deleted = 0
            )
        ORDER BY u.create_time DESC
    """)
    List<TenantUserDTO> getAvailableUsersForTenant(@Param("tenantId") Long tenantId);
}
