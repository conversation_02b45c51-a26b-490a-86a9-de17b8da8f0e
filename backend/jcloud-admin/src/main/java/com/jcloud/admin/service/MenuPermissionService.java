package com.jcloud.admin.service;

import com.jcloud.common.entity.SysMenu;
import java.util.List;

/**
 * 菜单权限服务接口
 * 基于菜单表的统一权限管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MenuPermissionService {

    /**
     * 获取用户的所有权限编码
     * 
     * @param userId 用户ID
     * @return 权限编码列表
     */
    List<String> getUserPermissionCodes(Long userId);

    /**
     * 检查用户是否拥有指定权限
     * 
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否拥有权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否拥有任意一个权限
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有任意一个权限
     */
    boolean hasAnyPermission(Long userId, String... permissionCodes);

    /**
     * 检查用户是否拥有所有权限
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有所有权限
     */
    boolean hasAllPermissions(Long userId, String... permissionCodes);

    /**
     * 获取角色的所有权限编码
     * 
     * @param roleId 角色ID
     * @return 权限编码列表
     */
    List<String> getRolePermissionCodes(Long roleId);

    /**
     * 获取角色的所有权限菜单（按钮类型）
     * 
     * @param roleId 角色ID
     * @return 权限菜单列表
     */
    List<SysMenu> getRolePermissionMenus(Long roleId);

    /**
     * 为角色分配权限
     * 
     * @param roleId 角色ID
     * @param permissionCodes 权限编码列表
     * @return 是否成功
     */
    boolean assignPermissionsToRole(Long roleId, List<String> permissionCodes);

    /**
     * 获取所有可用的权限菜单（按钮类型）
     * 
     * @return 权限菜单列表
     */
    List<SysMenu> getAllPermissionMenus();

    /**
     * 根据权限编码获取权限菜单
     * 
     * @param permissionCode 权限编码
     * @return 权限菜单
     */
    SysMenu getPermissionMenuByCode(String permissionCode);

    /**
     * 根据父菜单ID获取权限菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 权限菜单列表
     */
    List<SysMenu> getPermissionMenusByParentId(Long parentId);

    /**
     * 构建权限菜单树
     * 
     * @return 权限菜单树
     */
    List<SysMenu> buildPermissionMenuTree();

    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserPermissionCache(Long userId);

    /**
     * 刷新角色权限缓存
     * 
     * @param roleId 角色ID
     */
    void refreshRolePermissionCache(Long roleId);

    /**
     * 清除所有权限缓存
     */
    void clearAllPermissionCache();
}
