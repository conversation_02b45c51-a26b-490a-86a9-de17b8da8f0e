package com.jcloud.admin.config;

import com.jcloud.common.config.DataSourceContextHolder;
import com.mybatisflex.core.paginate.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * MyBatis-Flex分页插件配置
 * 解决多数据源环境下分页查询的数据源切换问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@Slf4j
public class MyBatisFlexPaginationConfig {

    /**
     * 数据源感知分页拦截器
     * 确保分页查询的COUNT和数据查询使用相同的数据源
     *
     * 注意：暂时禁用此拦截器，因为与MyBatis-Flex内置分页机制冲突
     * 导致TooManyResultsException异常
     */
    // @Bean
    public DataSourceAwarePaginationInterceptor dataSourceAwarePaginationInterceptor() {
        return new DataSourceAwarePaginationInterceptor();
    }

    /**
     * 数据源感知的分页拦截器
     * 在分页查询过程中保持数据源的一致性
     */
    @Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
    })
    public static class DataSourceAwarePaginationInterceptor implements Interceptor {

        @Override
        public Object intercept(Invocation invocation) throws Throwable {
            Object[] args = invocation.getArgs();
            MappedStatement ms = (MappedStatement) args[0];
            Object parameter = args[1];
            
            // 检查是否是分页查询
            if (parameter instanceof Page) {
                String currentDataSource = DataSourceContextHolder.getDataSource();
                log.debug("分页查询检测到数据源: {}, SQL ID: {}", currentDataSource, ms.getId());
                
                // 如果当前没有设置数据源，但是是运营模块的查询，则设置为slave
                if ("master".equals(currentDataSource) && ms.getId().contains("Operations")) {
                    log.debug("运营模块分页查询，强制切换到slave数据源");
                    DataSourceContextHolder.setDataSource("slave");
                }
            }
            
            // 检查是否是COUNT查询（MyBatis-Flex分页插件生成的COUNT查询）
            if (ms.getId().contains("_COUNT")) {
                String currentDataSource = DataSourceContextHolder.getDataSource();
                log.debug("COUNT查询检测到数据源: {}, SQL ID: {}", currentDataSource, ms.getId());
                
                // 确保COUNT查询使用正确的数据源
                if (ms.getId().contains("Operations") && !"slave".equals(currentDataSource)) {
                    log.debug("运营模块COUNT查询，强制切换到slave数据源");
                    DataSourceContextHolder.setDataSource("slave");
                }
            }
            
            return invocation.proceed();
        }

        @Override
        public Object plugin(Object target) {
            return Plugin.wrap(target, this);
        }

        @Override
        public void setProperties(Properties properties) {
            // 可以在这里设置插件属性
        }
    }
}
