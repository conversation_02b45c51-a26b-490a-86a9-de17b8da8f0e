package com.jcloud.admin.service.impl;

import com.jcloud.common.mapper.SysRoleMenuMapper;
import com.jcloud.admin.service.SysRoleMenuService;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysRoleMenu;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色菜单关联服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {
    
    private final SysRoleMenuMapper roleMenuMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMenusToRole(Long roleId, List<Long> menuIds) {
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();
        
        try {
            // 1. 删除原有的角色菜单关联
            roleMenuMapper.deleteByRoleId(roleId, tenantId);
            
            // 2. 如果菜单ID列表不为空，则添加新的关联
            if (menuIds != null && !menuIds.isEmpty()) {
                List<SysRoleMenu> roleMenus = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();
                
                for (Long menuId : menuIds) {
                    SysRoleMenu roleMenu = new SysRoleMenu();
                    roleMenu.setTenantId(tenantId);
                    roleMenu.setRoleId(roleId);
                    roleMenu.setMenuId(menuId);
                    roleMenu.setCreateTime(now);
                    roleMenu.setCreateBy(currentUserId);
                    roleMenus.add(roleMenu);
                }
                
                // 批量插入
                int insertCount = roleMenuMapper.batchInsert(roleMenus);
                log.info("为角色分配菜单成功: roleId={}, menuIds={}, insertCount={}", roleId, menuIds, insertCount);
            } else {
                log.info("清空角色菜单: roleId={}", roleId);
            }
            
            return true;
        } catch (Exception e) {
            log.error("为角色分配菜单失败: roleId={}, menuIds={}", roleId, menuIds, e);
            throw e;
        }
    }
    
    @Override
    public List<SysMenu> getMenusByRoleId(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        return roleMenuMapper.selectMenusByRoleId(roleId, tenantId);
    }
    
    @Override
    public List<Long> getMenuIdsByRoleId(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        return roleMenuMapper.selectMenuIdsByRoleId(roleId, tenantId);
    }
    
    @Override
    public List<SysMenu> getMenusByUserId(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        return roleMenuMapper.selectMenusByUserId(userId, tenantId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByRoleId(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        try {
            int deleteCount = roleMenuMapper.deleteByRoleId(roleId, tenantId);
            log.info("删除角色菜单关联成功: roleId={}, deleteCount={}", roleId, deleteCount);
            return true;
        } catch (Exception e) {
            log.error("删除角色菜单关联失败: roleId={}", roleId, e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMenuId(Long menuId) {
        Long tenantId = SecurityUtils.getTenantId();
        try {
            int deleteCount = roleMenuMapper.deleteByMenuId(menuId, tenantId);
            log.info("删除菜单角色关联成功: menuId={}, deleteCount={}", menuId, deleteCount);
            return true;
        } catch (Exception e) {
            log.error("删除菜单角色关联失败: menuId={}", menuId, e);
            return false;
        }
    }
}
