package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 充值详情响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "充值详情响应")
public class RechargeDetailResponse {
    
    /**
     * 充值订单ID
     */
    @Schema(description = "充值订单ID")
    private String id;
    
    /**
     * 三方订单号
     */
    @Schema(description = "三方订单号")
    private String payid;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer uid;
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;
    
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private java.math.BigDecimal amount;
    
    /**
     * 获得代币数量
     */
    @Schema(description = "获得代币数量")
    private java.math.BigDecimal coin;
    
    /**
     * 订单状态（1-未支付，2-已支付，3-已支付但回调异常）
     */
    @Schema(description = "订单状态")
    private Integer state;
    
    /**
     * 订单状态描述
     */
    @Schema(description = "订单状态描述")
    private String stateDesc;
    
    /**
     * 创建时间（时间戳）
     */
    @Schema(description = "创建时间")
    private Integer createTime;
    
    /**
     * 更新时间（时间戳）
     */
    @Schema(description = "更新时间")
    private Integer updateTime;
    
    /**
     * 是否首充
     */
    @Schema(description = "是否首充")
    private Boolean isFirstRecharge;
    
    /**
     * 支付方式（根据payid推断）
     */
    @Schema(description = "支付方式")
    private String paymentMethod;
}
