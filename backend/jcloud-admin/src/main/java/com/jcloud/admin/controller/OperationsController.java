package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.constant.OperationsConstants;
import com.jcloud.admin.dto.request.AnchorQueryRequest;
import com.jcloud.admin.dto.request.ConsumeQueryRequest;
import com.jcloud.admin.dto.request.RechargeQueryRequest;
import com.jcloud.admin.dto.request.SubUserQueryRequest;
import com.jcloud.admin.dto.response.*;
import com.jcloud.admin.service.OperationsService;
import com.jcloud.common.annotation.OperLog;
import com.jcloud.common.dto.OperationsStatsResponse;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 运营数据管理控制器
 * 提供主播运营数据相关接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "运营数据管理", description = "主播运营数据相关接口")
@RestController
@RequestMapping("/operations")
@RequiredArgsConstructor
@Slf4j
public class OperationsController {

    private final OperationsService operationsService;

    @Operation(summary = "获取主播列表", description = "分页查询主播列表信息")
    @SaCheckPermission(OperationsConstants.Permission.ANCHOR_QUERY)
    @OperLog(title = "运营管理", businessType = 0)
    @PostMapping("/anchors")
    public Result<PageResult<AnchorListResponse>> getAnchorList(
            @Valid @RequestBody AnchorQueryRequest request) {

        log.info("开始查询主播列表，请求参数：{}", request);

        PageResult<AnchorListResponse> result = operationsService.getAnchorList(request);

        log.info("主播列表查询完成，共查询到 {} 条记录", result.getTotal());
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取主播详细统计", description = "获取指定主播的详细业务统计数据")
    @SaCheckPermission(OperationsConstants.Permission.ANCHOR_DETAIL)
    @OperLog(title = "运营管理", businessType = 0)
    @GetMapping("/anchors/{anchorId}/stats")
    public Result<AnchorStatsResponse> getAnchorStats(
            @Parameter(description = "主播ID", required = true)
            @PathVariable("anchorId") Integer anchorId,
            @Parameter(description = "开始时间（时间戳）")
            @RequestParam(value = "startTime", required = false) Integer startTime,
            @Parameter(description = "结束时间（时间戳）")
            @RequestParam(value = "endTime", required = false) Integer endTime) {

        log.info("开始查询主播统计数据，主播ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);

        AnchorStatsResponse result = operationsService.getAnchorStats(anchorId, startTime, endTime);

        log.info("主播统计数据查询完成，主播ID：{}", anchorId);
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取主播首充统计", description = "获取主播下级用户的首充人数和转化率统计")
    @SaCheckPermission(OperationsConstants.Permission.ANCHOR_FIRST_RECHARGE)
    @OperLog(title = "运营管理", businessType = 0)
    @GetMapping("/anchors/{anchorId}/first-recharge-stats")
    public Result<FirstRechargeStatsResponse> getFirstRechargeStats(
            @Parameter(description = "主播ID", required = true)
            @PathVariable("anchorId") Integer anchorId,
            @Parameter(description = "开始时间（时间戳）")
            @RequestParam(value = "startTime", required = false) Integer startTime,
            @Parameter(description = "结束时间（时间戳）")
            @RequestParam(value = "endTime", required = false) Integer endTime) {

        log.info("开始查询主播首充统计，主播ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);

        FirstRechargeStatsResponse result = operationsService.getFirstRechargeStats(anchorId, startTime, endTime);

        log.info("主播首充统计查询完成，主播ID：{}", anchorId);
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取主播下级用户列表", description = "分页查询主播的下级用户")
    @SaCheckPermission(OperationsConstants.Permission.ANCHOR_USERS)
    @OperLog(title = "运营管理", businessType = 0)
    @PostMapping("/anchors/{anchorId}/users")
    public Result<PageResult<SubUserResponse>> getSubUsers(
            @Parameter(description = "主播ID", required = true)
            @PathVariable("anchorId") Integer anchorId,
            @Valid @RequestBody SubUserQueryRequest request) {

        log.info("开始查询主播下级用户，主播ID：{}，请求参数：{}", anchorId, request);

        PageResult<SubUserResponse> result = operationsService.getSubUsers(anchorId, request);

        log.info("主播下级用户查询完成，主播ID：{}，共查询到 {} 条记录", anchorId, result.getTotal());
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取用户消费详情", description = "查询用户的消费记录详情")
    @SaCheckPermission(OperationsConstants.Permission.USER_CONSUME)
    @OperLog(title = "运营管理", businessType = 0)
    @PostMapping("/users/{userId}/consume")
    public Result<PageResult<ConsumeDetailResponse>> getUserConsumeDetails(
            @Parameter(description = "用户ID", required = true)
            @PathVariable("userId") Integer userId,
            @Valid @RequestBody ConsumeQueryRequest request) {

        // 参数验证
        if (userId == null || userId <= 0) {
            log.warn("用户消费详情查询参数无效，用户ID：{}", userId);
            return Result.error("用户ID不能为空且必须大于0");
        }

        log.info("开始查询用户消费详情，用户ID：{}，请求参数：{}", userId, request);

        PageResult<ConsumeDetailResponse> result = operationsService.getUserConsumeDetails(userId, request);

        log.info("用户消费详情查询完成，用户ID：{}，共查询到 {} 条记录", userId, result.getTotal());
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取用户充值详情", description = "查询用户的充值记录详情")
    @SaCheckPermission(OperationsConstants.Permission.USER_RECHARGE)
    @OperLog(title = "运营管理", businessType = 0)
    @PostMapping("/users/{userId}/recharge")
    public Result<PageResult<RechargeDetailResponse>> getUserRechargeDetails(
            @Parameter(description = "用户ID", required = true)
            @PathVariable("userId") Integer userId,
            @Valid @RequestBody RechargeQueryRequest request) {

        // 参数验证
        if (userId == null || userId <= 0) {
            log.warn("用户充值详情查询参数无效，用户ID：{}", userId);
            return Result.error("用户ID不能为空且必须大于0");
        }

        log.info("开始查询用户充值详情，用户ID：{}，请求参数：{}", userId, request);

        PageResult<RechargeDetailResponse> result = operationsService.getUserRechargeDetails(userId, request);

        log.info("用户充值详情查询完成，用户ID：{}，共查询到 {} 条记录", userId, result.getTotal());
        return Result.success("查询成功", result);
    }

    @Operation(summary = "获取运营统计数据", description = "获取总主播数、活跃主播数、总用户数等关键指标")
    @SaCheckPermission(OperationsConstants.Permission.ANCHOR_QUERY)
    @OperLog(title = "运营管理", businessType = 0)
    @GetMapping("/stats")
    public Result<OperationsStatsResponse> getOperationsStats(
            @Parameter(description = "开始时间（时间戳）")
            @RequestParam(value = "startTime", required = false) Integer startTime,
            @Parameter(description = "结束时间（时间戳）")
            @RequestParam(value = "endTime", required = false) Integer endTime) {

        log.info("开始获取运营统计数据，时间范围：{} - {}", startTime, endTime);

        OperationsStatsResponse result = operationsService.getOperationsStats(startTime, endTime);

        log.info("运营统计数据获取完成: 总主播数={}, 活跃主播数={}, 总用户数={}",
                result.getTotalAnchors(), result.getActiveAnchors(), result.getTotalUsers());
        return Result.success("获取成功", result);
    }
}
