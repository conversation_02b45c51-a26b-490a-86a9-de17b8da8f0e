package com.jcloud.admin.vo;

import com.jcloud.common.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户详情VO
 * 包含用户基本信息和关联的部门、角色信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户详情信息")
public class SysUserDetailVO extends SysUser {
    
    /**
     * 用户主部门ID
     */
    @Schema(description = "用户主部门ID")
    private Long deptId;
    
    /**
     * 用户主部门名称
     */
    @Schema(description = "用户主部门名称")
    private String deptName;
    
    /**
     * 用户所属的所有部门ID列表
     */
    @Schema(description = "用户所属的所有部门ID列表")
    private List<Long> deptIds;
    
    /**
     * 用户所属的所有部门名称列表
     */
    @Schema(description = "用户所属的所有部门名称列表")
    private List<String> deptNames;
    
    /**
     * 用户角色ID列表
     */
    @Schema(description = "用户角色ID列表")
    private List<Long> roleIds;
    
    /**
     * 用户角色名称列表
     */
    @Schema(description = "用户角色名称列表")
    private List<String> roleNames;
    
    /**
     * 用户权限编码列表
     */
    @Schema(description = "用户权限编码列表")
    private List<String> permissions;
}
