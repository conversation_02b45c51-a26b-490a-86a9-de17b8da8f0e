package com.jcloud.admin.config;

import cn.dev33.satoken.stp.StpInterface;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.entity.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * sa-token权限验证接口实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {
    
    private final SysUserService userService;
    
    /**
     * 返回指定账号id所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        try {
            Long userId = Long.valueOf(loginId.toString());
            log.info("🚀 StpInterface.getPermissionList() 被调用: userId={}, loginType={}", userId, loginType);

            // 获取用户信息
            SysUser user = userService.getById(userId);
            if (user == null) {
                log.warn("❌ 用户不存在: userId={}", userId);
                return new ArrayList<>();
            }
            
            // 获取用户权限
            Set<String> permissions = userService.getUserPermissions(userId);
            List<String> permissionList = new ArrayList<>(permissions);

            log.info("🎯 StpInterface 最终返回权限列表: userId={}, permissions={}", userId, permissionList);
            return permissionList;
            
        } catch (Exception e) {
            log.error("获取用户权限失败: loginId={}, error={}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 返回指定账号id所拥有的角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        try {
            Long userId = Long.valueOf(loginId.toString());
            log.debug("获取用户角色列表: userId={}", userId);
            
            // 获取用户信息
            SysUser user = userService.getById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return new ArrayList<>();
            }
            
            // 获取用户角色
            Set<String> roles = userService.getUserRoles(userId);
            List<String> roleList = new ArrayList<>(roles);
            
            log.debug("用户角色列表: userId={}, roles={}", userId, roleList);
            return roleList;
            
        } catch (Exception e) {
            log.error("获取用户角色失败: loginId={}, error={}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
