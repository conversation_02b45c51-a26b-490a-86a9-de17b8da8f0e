package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.MenuPermissionService;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

// 注意：简化验证，移除 javax.validation 依赖
import java.util.List;

/**
 * 菜单权限管理控制器
 * 基于菜单表的统一权限管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
// 简化验证
@Tag(name = "菜单权限管理", description = "基于菜单表的统一权限管理接口")
public class MenuPermissionController {

    private final MenuPermissionService menuPermissionService;

    @GetMapping("/permissions")
    @Operation(summary = "获取所有权限菜单", description = "获取所有按钮类型的权限菜单列表")
    @SaCheckPermission("system:menu:view")
    public Result<List<SysMenu>> getPermissionMenus() {
        log.debug("获取所有权限菜单");
        List<SysMenu> permissionMenus = menuPermissionService.getAllPermissionMenus();
        return Result.success(permissionMenus);
    }

    @GetMapping("/permission-tree")
    @Operation(summary = "获取权限菜单树", description = "获取按钮类型权限菜单的树形结构")
    @SaCheckPermission("system:menu:view")
    public Result<List<SysMenu>> getPermissionMenuTree() {
        log.debug("获取权限菜单树");
        List<SysMenu> permissionMenuTree = menuPermissionService.buildPermissionMenuTree();
        return Result.success(permissionMenuTree);
    }

    @GetMapping("/permissions/parent/{parentId}")
    @Operation(summary = "根据父菜单获取权限菜单", description = "根据父菜单ID获取子权限菜单列表")
    @SaCheckPermission("system:menu:view")
    public Result<List<SysMenu>> getPermissionMenusByParent(
            @Parameter(description = "父菜单ID") @PathVariable Long parentId) {
        log.debug("根据父菜单获取权限菜单: parentId={}", parentId);
        List<SysMenu> permissionMenus = menuPermissionService.getPermissionMenusByParentId(parentId);
        return Result.success(permissionMenus);
    }

    @GetMapping("/permission/{permissionCode}")
    @Operation(summary = "根据权限编码获取权限菜单", description = "根据权限编码获取对应的权限菜单")
    @SaCheckPermission("system:menu:view")
    public Result<SysMenu> getPermissionMenuByCode(
            @Parameter(description = "权限编码") @PathVariable String permissionCode) {
        log.debug("根据权限编码获取权限菜单: permissionCode={}", permissionCode);
        SysMenu permissionMenu = menuPermissionService.getPermissionMenuByCode(permissionCode);
        return Result.success(permissionMenu);
    }

    @GetMapping("/validate-permission-code/{permissionCode}")
    @Operation(summary = "验证权限编码唯一性", description = "验证权限编码在系统中是否唯一")
    @SaCheckPermission("system:menu:view")
    public Result<Boolean> validatePermissionCode(
            @Parameter(description = "权限编码") @PathVariable String permissionCode,
            @Parameter(description = "排除的菜单ID") @RequestParam(required = false) Long excludeId) {
        log.debug("验证权限编码唯一性: permissionCode={}, excludeId={}", permissionCode, excludeId);
        
        // 查询是否存在相同权限编码的菜单
        SysMenu existingMenu = menuPermissionService.getPermissionMenuByCode(permissionCode);
        boolean isUnique = existingMenu == null || 
                          (excludeId != null && existingMenu.getId().equals(excludeId));
        
        return Result.success(isUnique);
    }

    @PostMapping("/batch-set-permission-codes")
    @Operation(summary = "批量设置权限编码", description = "批量为菜单设置权限编码")
    @SaCheckPermission("system:menu:edit")
    public Result<Void> batchSetPermissionCodes(
            @Parameter(description = "权限编码设置项") @RequestBody
            List<PermissionCodeItem> items) {
        log.info("批量设置权限编码: itemCount={}", items.size());
        
        // 这里需要调用菜单服务的批量更新方法
        // 由于我们没有在 MenuPermissionService 中定义这个方法，
        // 这里可以调用 SysMenuService 的批量更新方法
        
        return Result.success();
    }

    @GetMapping("/generate-permission-code")
    @Operation(summary = "生成权限编码建议", description = "根据菜单名称和父级路径生成权限编码建议")
    @SaCheckPermission("system:menu:view")
    public Result<List<String>> generatePermissionCodeSuggestion(
            @Parameter(description = "菜单名称") @RequestParam String menuName,
            @Parameter(description = "父级路径") @RequestParam(required = false) String parentPath) {
        log.debug("生成权限编码建议: menuName={}, parentPath={}", menuName, parentPath);
        
        // 简单的权限编码生成逻辑
        List<String> suggestions = generatePermissionCodeSuggestions(menuName, parentPath);
        
        return Result.success(suggestions);
    }

    @GetMapping("/user/{userId}/permissions")
    @Operation(summary = "获取用户权限编码", description = "获取指定用户的所有权限编码")
    @SaCheckPermission("system:user:view")
    public Result<List<String>> getUserPermissionCodes(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.debug("获取用户权限编码: userId={}", userId);
        List<String> permissionCodes = menuPermissionService.getUserPermissionCodes(userId);
        return Result.success(permissionCodes);
    }

    @GetMapping("/role/{roleId}/permissions")
    @Operation(summary = "获取角色权限编码", description = "获取指定角色的所有权限编码")
    @SaCheckPermission("system:role:view")
    public Result<List<String>> getRolePermissionCodes(
            @Parameter(description = "角色ID") @PathVariable Long roleId) {
        log.debug("获取角色权限编码: roleId={}", roleId);
        List<String> permissionCodes = menuPermissionService.getRolePermissionCodes(roleId);
        return Result.success(permissionCodes);
    }

    @GetMapping("/role/{roleId}/permission-menus")
    @Operation(summary = "获取角色权限菜单", description = "获取指定角色的所有权限菜单")
    @SaCheckPermission("system:role:view")
    public Result<List<SysMenu>> getRolePermissionMenus(
            @Parameter(description = "角色ID") @PathVariable Long roleId) {
        log.debug("获取角色权限菜单: roleId={}", roleId);
        List<SysMenu> permissionMenus = menuPermissionService.getRolePermissionMenus(roleId);
        return Result.success(permissionMenus);
    }

    @PostMapping("/role/{roleId}/assign-permissions")
    @Operation(summary = "为角色分配权限", description = "为指定角色分配权限编码列表")
    @SaCheckPermission("system:role:assign-menu")
    public Result<Void> assignPermissionsToRole(
            @Parameter(description = "角色ID") @PathVariable Long roleId,
            @Parameter(description = "权限编码列表") @RequestBody List<String> permissionCodes) {
        log.info("为角色分配权限: roleId={}, permissionCount={}", roleId, permissionCodes.size());
        
        boolean success = menuPermissionService.assignPermissionsToRole(roleId, permissionCodes);
        if (success) {
            return Result.success();
        } else {
            return Result.error("权限分配失败");
        }
    }

    @DeleteMapping("/cache/user/{userId}")
    @Operation(summary = "刷新用户权限缓存", description = "刷新指定用户的权限缓存")
    @SaCheckPermission("system:cache:manage")
    public Result<Void> refreshUserPermissionCache(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        log.info("刷新用户权限缓存: userId={}", userId);
        menuPermissionService.refreshUserPermissionCache(userId);
        return Result.success();
    }

    @DeleteMapping("/cache/role/{roleId}")
    @Operation(summary = "刷新角色权限缓存", description = "刷新指定角色的权限缓存")
    @SaCheckPermission("system:cache:manage")
    public Result<Void> refreshRolePermissionCache(
            @Parameter(description = "角色ID") @PathVariable Long roleId) {
        log.info("刷新角色权限缓存: roleId={}", roleId);
        menuPermissionService.refreshRolePermissionCache(roleId);
        return Result.success();
    }

    @DeleteMapping("/cache/all")
    @Operation(summary = "清除所有权限缓存", description = "清除系统中所有的权限缓存")
    @SaCheckPermission("system:cache:manage")
    public Result<Void> clearAllPermissionCache() {
        log.info("清除所有权限缓存");
        menuPermissionService.clearAllPermissionCache();
        return Result.success();
    }

    /**
     * 权限编码设置项
     */
    public static class PermissionCodeItem {
        private Long id;
        private String permissionCode;

        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getPermissionCode() { return permissionCode; }
        public void setPermissionCode(String permissionCode) { this.permissionCode = permissionCode; }
    }

    /**
     * 生成权限编码建议
     */
    private List<String> generatePermissionCodeSuggestions(String menuName, String parentPath) {
        List<String> suggestions = new java.util.ArrayList<>();
        
        // 转换菜单名称为权限编码格式
        String actionCode = menuName.toLowerCase()
                .replaceAll("[^a-z0-9\\u4e00-\\u9fa5]", "")
                .replaceAll("[\u4e00-\u9fa5]", ""); // 移除中文字符
        
        if (actionCode.isEmpty()) {
            actionCode = "action";
        }
        
        // 根据父级路径生成建议
        if (parentPath != null && !parentPath.isEmpty()) {
            String[] pathParts = parentPath.split("/");
            if (pathParts.length > 1) {
                String module = pathParts[1]; // 假设第一部分是模块名
                suggestions.add(module + ":" + actionCode);
                
                if (pathParts.length > 2) {
                    String resource = pathParts[2];
                    suggestions.add(module + ":" + resource + ":" + actionCode);
                }
            }
        }
        
        // 添加常用的权限编码模式
        suggestions.add("system:" + actionCode);
        suggestions.add("system:menu:" + actionCode);
        
        return suggestions;
    }
}
