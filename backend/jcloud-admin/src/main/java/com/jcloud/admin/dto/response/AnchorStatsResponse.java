package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 主播统计数据响应DTO
 * 基于存储过程 GetInviteUserStatistics 返回的数据结构
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "主播统计数据响应")
public class AnchorStatsResponse {
    
    /**
     * 总充值金额
     */
    @Schema(description = "总充值金额")
    private java.math.BigDecimal totalRecharge;
    
    /**
     * 总消费金额（支付订单 + 密钥订单）
     */
    @Schema(description = "总消费金额")
    private java.math.BigDecimal totalConsume;
    
    /**
     * 用户总数
     */
    @Schema(description = "用户总数")
    private Integer userCount;
    
    /**
     * 指定时间区间内新增用户数
     */
    @Schema(description = "指定时间区间内新增用户数")
    private Integer periodNewUserCount;

    /**
     * 指定时间区间内新邀请的下级用户总数
     */
    @Schema(description = "指定时间区间内新邀请的下级用户总数")
    private Integer periodNewInviteCount;
    
    /**
     * 总待发货金额
     */
    @Schema(description = "总待发货金额")
    private java.math.BigDecimal totalClaimAmount;
    
    /**
     * 总实际发货金额
     */
    @Schema(description = "总实际发货金额")
    private java.math.BigDecimal totalShippedAmount;
    
    /**
     * 背包总价值
     */
    @Schema(description = "背包总价值")
    private java.math.BigDecimal totalBackpackAmount;
    
    /**
     * 时间区间内下级用户总充值金额
     */
    @Schema(description = "时间区间内下级用户总充值金额")
    private java.math.BigDecimal periodTotalRecharge;
    
    /**
     * 总流水
     */
    @Schema(description = "总流水")
    private java.math.BigDecimal totalTurnover;
    
    /**
     * 利润比（实际利润 / 总消费金额 × 100%）
     */
    @Schema(description = "利润比")
    private java.math.BigDecimal profitRatio;
    
    /**
     * 实际利润金额
     */
    @Schema(description = "实际利润金额")
    private java.math.BigDecimal actualProfit;
    
    /**
     * 查询开始时间（时间戳）
     */
    @Schema(description = "查询开始时间")
    private Integer startTime;
    
    /**
     * 查询结束时间（时间戳）
     */
    @Schema(description = "查询结束时间")
    private Integer endTime;
}
