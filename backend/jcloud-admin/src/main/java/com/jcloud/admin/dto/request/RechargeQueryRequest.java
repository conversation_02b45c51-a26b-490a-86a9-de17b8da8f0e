package com.jcloud.admin.dto.request;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充值查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充值查询请求")
public class RechargeQueryRequest extends PageQuery {
    
    /**
     * 充值订单号
     */
    @Schema(description = "充值订单号", example = "RCH202401010001")
    private String orderId;
    
    /**
     * 三方订单号
     */
    @Schema(description = "三方订单号", example = "WX202401010001")
    private String payId;
    
    /**
     * 充值开始时间（时间戳）
     */
    @Schema(description = "充值开始时间", example = "1640995200")
    private Integer startTime;
    
    /**
     * 充值结束时间（时间戳）
     */
    @Schema(description = "充值结束时间", example = "1672531199")
    private Integer endTime;
    
    /**
     * 最小充值金额
     */
    @Schema(description = "最小充值金额", example = "10.00")
    private java.math.BigDecimal minAmount;
    
    /**
     * 最大充值金额
     */
    @Schema(description = "最大充值金额", example = "10000.00")
    private java.math.BigDecimal maxAmount;
    
    /**
     * 订单状态（1-未支付，2-已支付，3-已支付但回调异常）
     */
    @Schema(description = "订单状态", example = "2")
    private Integer state;
    
    /**
     * 是否首充
     */
    @Schema(description = "是否首充", example = "true")
    private Boolean isFirstRecharge;
}
