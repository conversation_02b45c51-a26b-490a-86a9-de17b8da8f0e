package com.jcloud.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.vo.SysUserVO;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.util.DataMaskingUtils;
import com.jcloud.common.dto.UserCreateRequest;
import com.jcloud.common.dto.UserQueryRequest;
import com.jcloud.common.dto.UserUpdateRequest;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.entity.SysRole;
import com.jcloud.common.entity.SysUser;
import com.jcloud.admin.vo.SysUserDetailVO;
import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.common.entity.SysUserRole;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysUserMapper;
import com.jcloud.common.mapper.SysUserRoleMapper;
import com.jcloud.common.mapper.VimUserMapper;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.annotation.DataSource;
import com.jcloud.common.config.DataSourceContextHolder;
import com.jcloud.admin.service.MenuPermissionService;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.cache.CacheManager;
import jakarta.annotation.PostConstruct;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jcloud.common.util.TimeUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.Map;
import java.util.function.Function;


// 表定义将由MyBatis-Flex自动生成

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    private final SysUserRoleMapper userRoleMapper;
    private final MenuPermissionService menuPermissionService;
    private final BCryptPasswordEncoder passwordEncoder;
    private final DeptPermissionService deptPermissionService;
    private final VimUserMapper vimUserMapper;
    private final CacheManager cacheManager;

    /**
     * 异步执行器，用于处理同步任务
     */
    private final Executor asyncExecutor = Executors.newFixedThreadPool(2);

    public SysUserServiceImpl(SysUserRoleMapper userRoleMapper,
                             MenuPermissionService menuPermissionService,
                             BCryptPasswordEncoder passwordEncoder,
                             DeptPermissionService deptPermissionService,
                             VimUserMapper vimUserMapper,
                             CacheManager cacheManager) {
        this.userRoleMapper = userRoleMapper;
        this.menuPermissionService = menuPermissionService;
        this.passwordEncoder = passwordEncoder;
        this.deptPermissionService = deptPermissionService;
        this.vimUserMapper = vimUserMapper;
        this.cacheManager = cacheManager;
    }

    /**
     * 应用启动时清除可能存在的旧格式缓存
     */
    @PostConstruct
    public void clearOldFormatCache() {
        try {
            if (cacheManager.getCache("userCache") != null) {
                cacheManager.getCache("userCache").clear();
                log.info("已清除用户缓存，解决序列化格式兼容性问题");
            }
        } catch (Exception e) {
            log.warn("清除用户缓存失败，但不影响系统运行", e);
        }
    }
    
    @Override
    public PageResult<SysUser> pageUsers(UserQueryRequest queryRequest) {
        QueryWrapper queryWrapper = getQueryWrapper();

        // 构建查询条件
        if (StrUtil.isNotBlank(queryRequest.getUsername())) {
            queryWrapper.like("username", queryRequest.getUsername());
        }
        if (StrUtil.isNotBlank(queryRequest.getNickname())) {
            queryWrapper.like("nickname", queryRequest.getNickname());
        }
        if (StrUtil.isNotBlank(queryRequest.getRealName())) {
            queryWrapper.like("real_name", queryRequest.getRealName());
        }
        if (StrUtil.isNotBlank(queryRequest.getEmail())) {
            queryWrapper.like("email", queryRequest.getEmail());
        }
        if (StrUtil.isNotBlank(queryRequest.getPhone())) {
            queryWrapper.like("phone", queryRequest.getPhone());
        }
        if (queryRequest.getGender() != null) {
            queryWrapper.eq("gender", queryRequest.getGender());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getDeptId() != null) {
            // TODO: 通过用户部门关联表查询，暂时跳过
        }
        if (queryRequest.getIsAdmin() != null) {
            queryWrapper.eq("is_admin", queryRequest.getIsAdmin());
        }
        if (queryRequest.getCreateTimeStart() != null) {
            queryWrapper.ge("create_time", queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            queryWrapper.le("create_time", queryRequest.getCreateTimeEnd());
        }

        // 添加排序
        if (StrUtil.isNotBlank(queryRequest.getOrderBy())) {
            queryWrapper.orderBy(queryRequest.getOrderBy(), CommonConstants.ORDER_ASC.equalsIgnoreCase(queryRequest.getOrderDirection()));
        } else {
            queryWrapper.orderBy("create_time", false);
        }

        // MyBatis-Flex会自动应用数据权限过滤，无需手动调用
        Page<SysUser> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysUser> result = baseMapper.paginate(page, queryWrapper);

        return PageResult.of(result.getRecords(), result.getTotalRow(),
                           queryRequest.getPageNum(), queryRequest.getPageSize());
    }

    @Override
    public PageResult<SysUserVO> pageUsersForDisplay(UserQueryRequest queryRequest) {
        // 复用原有的查询逻辑
        PageResult<SysUser> userPageResult = pageUsers(queryRequest);

        // 转换为脱敏VO
        List<SysUserVO> maskedUsers = userPageResult.getRecords().stream()
                .map(this::convertToMaskedUserVO)
                .collect(Collectors.toList());

        return PageResult.of(maskedUsers, userPageResult.getTotal(),
                           userPageResult.getPageNum(), userPageResult.getPageSize());
    }

    /**
     * 将SysUser转换为脱敏的SysUserVO
     *
     * @param user 原始用户对象
     * @return 脱敏的用户VO
     */
    private SysUserVO convertToMaskedUserVO(SysUser user) {
        SysUserVO userVO = new SysUserVO();
        BeanUtil.copyProperties(user, userVO);

        // 手动处理脱敏
        if (StrUtil.isNotBlank(user.getPhone())) {
            userVO.setPhone(DataMaskingUtils.maskPhone(user.getPhone()));
        }

        if (StrUtil.isNotBlank(user.getEmail())) {
            userVO.setEmail(DataMaskingUtils.maskEmail(user.getEmail()));
        }

        return userVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(UserCreateRequest createRequest) {
        // 1. 验证用户名唯一性
        if (isUsernameExists(createRequest.getUsername(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "用户名已存在");
        }
        
        // 2. 验证邮箱唯一性
        if (StrUtil.isNotBlank(createRequest.getEmail()) && 
            isEmailExists(createRequest.getEmail(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "邮箱已存在");
        }
        
        // 3. 验证手机号唯一性
        if (StrUtil.isNotBlank(createRequest.getPhone()) && 
            isPhoneExists(createRequest.getPhone(), null)) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "手机号已存在");
        }
        
        // 4. 创建用户实体
        SysUser user = new SysUser();
        user.setUsername(createRequest.getUsername());
        user.setPassword(passwordEncoder.encode(createRequest.getPassword()));
        user.setNickname(createRequest.getNickname());
        user.setRealName(createRequest.getRealName());
        user.setEmail(createRequest.getEmail());
        user.setPhone(createRequest.getPhone());
        user.setGender(createRequest.getGender());
        user.setBirthday(createRequest.getBirthday());
        user.setStatus(createRequest.getStatus());
        user.setIsAdmin(CommonConstants.STATUS_DISABLED); // 默认非管理员
        user.setRemark(createRequest.getRemark());
        user.setPasswordUpdateTime(TimeUtil.now());
        
        // 5. 保存用户
        boolean success = save(user);
        if (!success) {
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR, "创建用户失败");
        }
        
        // 6. 分配角色
        if (createRequest.getRoleIds() != null && !createRequest.getRoleIds().isEmpty()) {
            assignRoles(user.getId(), createRequest.getRoleIds());
        }
        
        log.info("创建用户成功: {}", user.getUsername());
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserUpdateRequest updateRequest) {
        // 1. 验证用户是否存在
        SysUser existUser = getById(updateRequest.getId());
        if (existUser == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }

        // 2. 验证邮箱唯一性
        if (StrUtil.isNotBlank(updateRequest.getEmail()) &&
            isEmailExists(updateRequest.getEmail(), updateRequest.getId())) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "邮箱已存在");
        }
        
        // 3. 验证手机号唯一性
        if (StrUtil.isNotBlank(updateRequest.getPhone()) && 
            isPhoneExists(updateRequest.getPhone(), updateRequest.getId())) {
            throw BusinessException.of(ResultCode.DATA_ALREADY_EXISTS, "手机号已存在");
        }
        
        // 4. 更新用户信息
        SysUser user = new SysUser();
        user.setId(updateRequest.getId());
        user.setNickname(updateRequest.getNickname());
        user.setRealName(updateRequest.getRealName());
        user.setEmail(updateRequest.getEmail());
        user.setPhone(updateRequest.getPhone());
        user.setGender(updateRequest.getGender());
        user.setBirthday(updateRequest.getBirthday());
        user.setStatus(updateRequest.getStatus());
        user.setRemark(updateRequest.getRemark());
        boolean success = updateById(user);
        if (!success) {
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR, "更新用户失败");
        }
        
        // 5. 更新角色分配
        if (updateRequest.getRoleIds() != null) {
            assignRoles(updateRequest.getId(), updateRequest.getRoleIds());
        }
        
        log.info("更新用户成功: {}", existUser.getUsername());
        return true;
    }
    
    @Override
    // @Cacheable(value = "userCache", key = "'username:' + #username + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByUsername(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("查询用户: username={}, tenantId={}", username, tenantId);

        try {
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询用户失败: username={}, tenantId={}", username, tenantId, e);
            // 如果是类型转换错误，清理缓存后重试
            if (e.getCause() instanceof ClassCastException) {
                log.warn("检测到缓存类型转换错误，清理用户缓存后重试");
                clearUserCache(username, tenantId);
                return baseMapper.selectByUsername(username, tenantId);
            }
            throw e;
        }
    }

    /**
     * 清理指定用户的缓存
     */
    private void clearUserCache(String username, Long tenantId) {
        try {
            String cacheKey = "username:" + username + ":tenant:" + tenantId;
            if (cacheManager.getCache("userCache") != null) {
                cacheManager.getCache("userCache").evict(cacheKey);
                log.info("已清理用户缓存: {}", cacheKey);
            }
        } catch (Exception e) {
            log.warn("清理用户缓存失败", e);
        }
    }

    @Override
    public void clearUserCacheByUsername(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        clearUserCache(username, tenantId);
        log.info("已清理用户缓存: username={}, tenantId={}", username, tenantId);
    }

    @Override
    public SysUser getUserByUsernameFromDatabase(String username) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("直接从数据库查询用户: username={}, tenantId={}", username, tenantId);

        try {
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("数据库查询结果: {}", user != null ? "找到用户" : "未找到用户");
            if (user != null && user.getPassword() != null) {
                log.debug("用户密码字段正常: username={}, passwordLength={}", username, user.getPassword().length());
            } else if (user != null) {
                log.warn("用户密码字段为空: username={}", username);
            }
            return user;
        } catch (Exception e) {
            log.error("从数据库查询用户失败: username={}, tenantId={}", username, tenantId, e);
            throw e;
        }
    }

    // MyBatis-Flex自动处理数据权限，无需额外的权限检查方法
    
    @Override
    // @Cacheable(value = "userCache", key = "'email:' + #email + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByEmail(String email) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.selectByEmail(email, tenantId);
    }

    @Override
    @Cacheable(value = "userCache", key = "'phone:' + #phone + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public SysUser getUserByPhone(String phone) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.selectByPhone(phone, tenantId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        // 验证用户是否存在
        SysUser user = getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }
        
        String encodedPassword = passwordEncoder.encode(newPassword);
        Long tenantId = SecurityUtils.getTenantId();
        
        int result = baseMapper.resetPassword(userId, encodedPassword, tenantId);
        if (result > 0) {
            log.info("重置用户密码成功: {}", user.getUsername());
            return true;
        }
        
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // 验证用户是否存在
        SysUser user = getById(userId);
        if (user == null) {
            throw BusinessException.of(ResultCode.USER_NOT_FOUND, "用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw BusinessException.of(ResultCode.PASSWORD_ERROR, "原密码错误");
        }
        
        // 更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        Long tenantId = SecurityUtils.getTenantId();
        
        int result = baseMapper.resetPassword(userId, encodedPassword, tenantId);
        if (result > 0) {
            log.info("修改用户密码成功: {}", user.getUsername());
            return true;
        }
        
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(Long userId, Integer status) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setStatus(status);
        
        boolean success = updateById(user);
        if (success) {
            log.info("更新用户状态成功: userId={}, status={}", userId, status);
        }
        
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatusBatch(List<Long> userIds, Integer status) {
        Long tenantId = SecurityUtils.getTenantId();
        int result = baseMapper.updateStatusBatch(userIds, status, tenantId);
        
        if (result > 0) {
            log.info("批量更新用户状态成功: userIds={}, status={}", userIds, status);
            return true;
        }
        
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(Long userId, List<Long> roleIds) {
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();

        // 1. 删除原有角色关联
        userRoleMapper.deleteByUserId(userId, tenantId);

        // 2. 添加新的角色关联
        if (roleIds != null && !roleIds.isEmpty()) {
            for (Long roleId : roleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRole.setTenantId(tenantId);
                userRole.setCreateBy(currentUserId);
                userRoleMapper.insert(userRole);
            }
            log.info("分配用户角色成功: userId={}, roleIds={}", userId, roleIds);
        }
         // 如果roleIds为空，表示清空角色，也算成功
        return true;
    }
    
    @Override
    public List<Long> getUserRoleIds(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        return userRoleMapper.selectRolesByUserId(userId, tenantId)
                .stream()
                .map(SysRole::getId)
                .toList();
    }
    
    @Override
    public boolean isUsernameExists(String username, Long excludeUserId) {
        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByUsername(username, tenantId);
        
        if (user == null) {
            return false;
        }
        
        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }
    
    @Override
    public boolean isEmailExists(String email, Long excludeUserId) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByEmail(email, tenantId);
        
        if (user == null) {
            return false;
        }
        
        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }
    
    @Override
    public boolean isPhoneExists(String phone, Long excludeUserId) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        SysUser user = baseMapper.selectByPhone(phone, tenantId);
        
        if (user == null) {
            return false;
        }
        
        // 如果是更新操作，排除当前用户
        return !user.getId().equals(excludeUserId);
    }

    @Override
    public Set<String> getUserPermissions(Long userId) {
        log.info("SysUserService.getUserPermissions() 被调用: userId={}", userId);

        // 检查是否为超级管理员
        SysUser user = getById(userId);
        return getUserPermissions(user);
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#user.id + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserPermissions(SysUser user) {
        log.info("🚀 SysUserService.getUserPermissions(SysUser) 被调用: userId={}", user != null ? user.getId() : null);
        log.info(" 用户信息检查: user={}", user);

        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            // 超级管理员拥有所有权限
            Set<String> allPermissions = new HashSet<>();
            allPermissions.add("*:*:*");
            log.info(" 超级管理员权限返回: permissions={}", allPermissions);
            return allPermissions;
        }

        // 使用新的菜单权限服务获取用户权限
        List<String> permissions = menuPermissionService.getUserPermissionCodes(user.getId());
        Set<String> permissionSet = new HashSet<>(permissions);
        log.info(" 普通用户权限返回: permissions={}", permissionSet);
        return permissionSet;
    }

    @Override
    public Set<String> getUserRoles(Long userId) {
        // 检查是否为超级管理员
        SysUser user = getById(userId);
        return getUserRoles(user);
    }

    @Override
    @Cacheable(value = "userRoles", key = "#user.id + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Set<String> getUserRoles(SysUser user) {
        Long tenantId = SecurityUtils.getTenantId();

        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            // 超级管理员拥有admin角色
            Set<String> adminRoles = new HashSet<>();
            adminRoles.add(CommonConstants.SUPER_ADMIN_ROLE);
            return adminRoles;
        }

        // 获取用户角色
        List<String> roles = userRoleMapper.selectRoleCodesByUserId(user.getId(), tenantId);
        return new HashSet<>(roles);
    }

    @Override
    public SysUserDetailVO getUserDetailById(Long userId) {
        if (userId == null) {
            return null;
        }

        // 获取用户基本信息
        SysUser user = getById(userId);
        if (user == null) {
            return null;
        }

        // 创建用户详情VO
        SysUserDetailVO userDetail = new SysUserDetailVO();
        // 复制基本属性
        userDetail.setId(user.getId());
        userDetail.setUsername(user.getUsername());
        userDetail.setNickname(user.getNickname());
        userDetail.setRealName(user.getRealName());
        userDetail.setAvatar(user.getAvatar());
        // 对敏感信息进行脱敏处理
        userDetail.setEmail(StrUtil.isNotBlank(user.getEmail()) ? DataMaskingUtils.maskEmail(user.getEmail()) : user.getEmail());
        userDetail.setPhone(StrUtil.isNotBlank(user.getPhone()) ? DataMaskingUtils.maskPhone(user.getPhone()) : user.getPhone());
        userDetail.setGender(user.getGender());
        userDetail.setBirthday(user.getBirthday());
        userDetail.setStatus(user.getStatus());
        userDetail.setIsAdmin(user.getIsAdmin());
        userDetail.setLastLoginTime(user.getLastLoginTime());
        userDetail.setLastLoginIp(user.getLastLoginIp());
        userDetail.setPasswordUpdateTime(user.getPasswordUpdateTime());
        userDetail.setRemark(user.getRemark());
        userDetail.setCreateTime(user.getCreateTime());
        userDetail.setUpdateTime(user.getUpdateTime());
        userDetail.setCreateBy(user.getCreateBy());
        userDetail.setUpdateBy(user.getUpdateBy());
        userDetail.setDeleted(user.getDeleted());
        userDetail.setTenantId(user.getTenantId());
        userDetail.setVersion(user.getVersion());

        try {
            // 获取用户主部门信息
            Long primaryDeptId = deptPermissionService.getUserPrimaryDeptId(userId);
            if (primaryDeptId != null) {
                userDetail.setDeptId(primaryDeptId);
                // 这里可以进一步查询部门名称
                // SysDept dept = deptService.getById(primaryDeptId);
                // if (dept != null) {
                //     userDetail.setDeptName(dept.getDeptName());
                // }
            }

            // 获取用户所有部门
            List<Long> allDeptIds = deptPermissionService.getUserDeptIds(userId);
            userDetail.setDeptIds(allDeptIds);

            // 获取用户角色
            Long tenantId = SecurityUtils.getTenantId();
            List<String> roleCodes = userRoleMapper.selectRoleCodesByUserId(userId, tenantId);
            // 这里可以进一步查询角色ID和名称
            // userDetail.setRoleNames(roleNames);

            // 获取用户权限
            List<String> permissions = menuPermissionService.getUserPermissionCodes(userId);
            userDetail.setPermissions(permissions);

        } catch (Exception e) {
            log.error("获取用户详情时发生错误: userId={}", userId, e);
            // 即使获取部门角色信息失败，也返回基本用户信息
        }

        return userDetail;
    }

    @Override
    public SysUserDetailVO getUserDetailForEdit(Long userId) {
        // 复用现有的getUserDetailById方法获取完整信息
        SysUserDetailVO userDetail = getUserDetailById(userId);
        if (userDetail == null) {
            return null;
        }

        // 获取原始用户数据（不脱敏）
        SysUser user = getById(userId);
        if (user != null) {
            // 恢复原始的敏感信息（用于编辑）
            userDetail.setEmail(user.getEmail()); // 原始邮箱
            userDetail.setPhone(user.getPhone()); // 原始手机号
        }

        return userDetail;
    }

    // MyBatis-Flex数据权限方言会自动处理权限过滤，无需手动实现

    @Override
    public String syncAnchorUsers() {
        log.info("开始异步同步vim_user表中的主播用户到sys_user表");

        try {
            // 启动异步同步任务
            CompletableFuture.runAsync(() -> {
                try {
                    performSyncAnchorUsers();
                } catch (Exception e) {
                    log.error("异步同步任务执行失败", e);
                }
            }, asyncExecutor);

            return "用户同步任务已启动，正在后台执行。请稍后查看同步结果。";

        } catch (Exception e) {
            log.error("启动同步任务失败", e);
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR,
                    "启动同步任务失败: " + e.getMessage());
        }
    }

    /**
     * 执行实际的同步操作（优化版本）
     */
    private void performSyncAnchorUsers() {
        log.info("开始执行用户同步操作");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 批量查询从库中的主播用户
            List<VimUser> anchorUsers = queryAnchorUsersFromSlave();
            log.info("从vim_user表查询到 {} 个主播用户", anchorUsers.size());

            if (anchorUsers.isEmpty()) {
                log.info("未找到需要同步的主播用户");
                return;
            }

            // 获取默认租户ID
            Long defaultTenantId = getDefaultTenantId();

            // 2. 批量查询现有用户，减少数据库查询次数
            Map<String, SysUser> existingUsersByPhone = batchQueryExistingUsersByPhone(
                    anchorUsers.stream().map(VimUser::getPhone).collect(Collectors.toList()),
                    defaultTenantId);

            Map<String, SysUser> existingUsersByUsername = batchQueryExistingUsersByUsername(
                    anchorUsers.stream().map(VimUser::getUsername).collect(Collectors.toList()),
                    defaultTenantId);

            // 3. 过滤需要同步的用户
            List<VimUser> usersToSync = anchorUsers.stream()
                    .filter(user -> user.getPhone() != null && !user.getPhone().trim().isEmpty())
                    .filter(user -> !existingUsersByPhone.containsKey(user.getPhone()))
                    .filter(user -> !existingUsersByUsername.containsKey(user.getUsername()))
                    .collect(Collectors.toList());

            log.info("过滤后需要同步的用户数量: {}", usersToSync.size());

            if (usersToSync.isEmpty()) {
                log.info("所有主播用户已存在，无需同步");
                return;
            }

            // 4. 批量转换和保存用户
            List<SysUser> newUsers = usersToSync.stream()
                    .map(vimUser -> convertVimUserToSysUser(vimUser, defaultTenantId))
                    .collect(Collectors.toList());

            int successCount = batchSaveUsers(newUsers);
            int skipCount = anchorUsers.size() - usersToSync.size();
            int errorCount = usersToSync.size() - successCount;

            // 5. 记录同步结果
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String result = String.format(
                "用户同步完成！总计: %d 个主播用户，成功: %d 个，跳过: %d 个，失败: %d 个，耗时: %d ms",
                anchorUsers.size(), successCount, skipCount, errorCount, duration
            );

            log.info("用户同步结果: {}", result);

        } catch (Exception e) {
            log.error("同步主播用户时发生异常", e);
        }
    }

    /**
     * 获取默认租户ID
     */
    private Long getDefaultTenantId() {
        try {
            Long tenantId = SecurityUtils.getTenantId();
            return tenantId != null ? tenantId : 1L;
        } catch (Exception e) {
            log.warn("无法获取当前租户ID，使用默认租户ID: 1");
            return 1L;
        }
    }

    /**
     * 批量查询现有用户（根据手机号）
     */
    @DataSource("master")
    private Map<String, SysUser> batchQueryExistingUsersByPhone(List<String> phones, Long tenantId) {
        if (phones.isEmpty()) {
            return Map.of();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where("phone IN (?)", phones)
                    .and("tenant_id = ?", tenantId)
                    .and("deleted = ?", 0);

            List<SysUser> existingUsers = baseMapper.selectListByQuery(queryWrapper);
            return existingUsers.stream()
                    .collect(Collectors.toMap(SysUser::getPhone, Function.identity()));
        } catch (Exception e) {
            log.error("批量查询用户手机号失败", e);
            return Map.of();
        }
    }

    /**
     * 批量查询现有用户（根据用户名）
     */
    @DataSource("master")
    private Map<String, SysUser> batchQueryExistingUsersByUsername(List<String> usernames, Long tenantId) {
        if (usernames.isEmpty()) {
            return Map.of();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where("username IN (?)", usernames)
                    .and("tenant_id = ?", tenantId)
                    .and("deleted = ?", 0);

            List<SysUser> existingUsers = baseMapper.selectListByQuery(queryWrapper);
            return existingUsers.stream()
                    .collect(Collectors.toMap(SysUser::getUsername, Function.identity()));
        } catch (Exception e) {
            log.error("批量查询用户名失败", e);
            return Map.of();
        }
    }

    /**
     * 批量保存用户到主库
     */
    @Transactional(rollbackFor = Exception.class)
    @DataSource("master")
    private int batchSaveUsers(List<SysUser> users) {
        if (users.isEmpty()) {
            return 0;
        }

        try {
            log.info("开始批量保存 {} 个用户", users.size());

            // 分批处理，每批50个用户
            int batchSize = 50;
            int successCount = 0;

            for (int i = 0; i < users.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, users.size());
                List<SysUser> batch = users.subList(i, endIndex);

                try {
                    // 使用MyBatis-Flex的批量插入
                    int batchResult = baseMapper.insertBatch(batch);
                    successCount += batchResult;
                    log.debug("批量保存第 {} 批用户成功，数量: {}", (i / batchSize) + 1, batchResult);
                } catch (Exception e) {
                    log.error("批量保存第 {} 批用户失败", (i / batchSize) + 1, e);
                    // 如果批量失败，尝试逐个保存
                    for (SysUser user : batch) {
                        try {
                            boolean saved = save(user);
                            if (saved) {
                                successCount++;
                            }
                        } catch (Exception ex) {
                            log.error("保存单个用户失败: {}", user.getUsername(), ex);
                        }
                    }
                }
            }

            log.info("批量保存完成，成功保存 {} 个用户", successCount);
            return successCount;

        } catch (Exception e) {
            log.error("批量保存用户失败", e);
            throw e;
        }
    }

    /**
     * 从从库查询主播用户（非事务方法）
     *
     * @return 主播用户列表
     */
    @DataSource("slave")
    private List<VimUser> queryAnchorUsersFromSlave() {
        try {
            log.debug("切换到从库查询主播用户");
            return vimUserMapper.selectAnchorUsers();
        } catch (Exception e) {
            log.error("查询从库vim_user表失败", e);
            throw BusinessException.of(ResultCode.INTERNAL_SERVER_ERROR,
                    "查询从库vim_user表失败: " + e.getMessage());
        }
    }

    /**
     * 检查主库中是否存在指定手机号的用户（非事务方法）
     *
     * @param phone 手机号
     * @param tenantId 租户ID
     * @return 用户信息，不存在则返回null
     */
    @DataSource("master")
    private SysUser checkUserExistsByPhone(String phone, Long tenantId) {
        try {
            log.debug("使用主库查询用户: phone={}, tenantId={}", phone, tenantId);

            // 直接使用baseMapper查询，@DataSource注解会确保使用主库
            SysUser user = baseMapper.selectByPhone(phone, tenantId);
            log.debug("查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询主库用户失败: phone={}, tenantId={}", phone, tenantId, e);
            throw new RuntimeException("查询用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存用户到主库（单独事务）
     *
     * @param user 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @DataSource("master")
    private void saveUserToMaster(SysUser user) {
        try {
            log.debug("保存用户到主库: username={}", user.getUsername());
            boolean saved = save(user);
            if (!saved) {
                throw new RuntimeException("保存用户失败");
            }
        } catch (Exception e) {
            log.error("保存用户到主库失败: username={}", user.getUsername(), e);
            throw e;
        }
    }

    /**
     * 检查主库中是否存在指定用户名的用户
     *
     * @param username 用户名
     * @param tenantId 租户ID
     * @return 用户信息，不存在则返回null
     */
    @DataSource("master")
    private SysUser checkUserExistsByUsername(String username, Long tenantId) {
        try {
            log.debug("使用主库查询用户名: username={}, tenantId={}", username, tenantId);

            // 直接使用baseMapper查询，@DataSource注解会确保使用主库
            SysUser user = baseMapper.selectByUsername(username, tenantId);
            log.debug("用户名查询结果: {}", user != null ? "找到用户" : "未找到用户");
            return user;
        } catch (Exception e) {
            log.error("查询主库用户名失败: username={}, tenantId={}", username, tenantId, e);
            throw new RuntimeException("查询用户名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将VimUser转换为SysUser
     *
     * @param vimUser vim_user表的用户数据
     * @param tenantId 租户ID
     * @return 转换后的SysUser对象
     */
    private SysUser convertVimUserToSysUser(VimUser vimUser, Long tenantId) {
        SysUser sysUser = new SysUser();

        // 基本信息映射
        sysUser.setTenantId(tenantId);
        sysUser.setUsername(vimUser.getUsername());
        sysUser.setPassword(vimUser.getPassword()); // 直接使用原密码（已加密）
        sysUser.setNickname(vimUser.getNickname());
        sysUser.setRealName(vimUser.getNickname()); // 使用昵称作为真实姓名
        sysUser.setAvatar(vimUser.getUserimage());
        sysUser.setPhone(vimUser.getPhone());
        sysUser.setLastLoginIp(vimUser.getLastLoginIp());

        // 状态映射：vim_user.state (1=正常,2=禁用) -> sys_user.status (1=启用,0=禁用)
        sysUser.setStatus(vimUser.getState() == 1 ? 1 : 0);

        // 默认值设置
        sysUser.setIsAdmin(0); // 非管理员
        sysUser.setGender(0); // 未知性别
        sysUser.setRemark("从vim_user同步的主播用户 (identity=" + vimUser.getIdentity() + ")");

        // 时间转换：int时间戳 -> Long时间戳
        if (vimUser.getLastLoginTime() != null && vimUser.getLastLoginTime() > 0) {
            sysUser.setLastLoginTime(vimUser.getLastLoginTime().longValue());
        }

        if (vimUser.getCreateTime() != null && vimUser.getCreateTime() > 0) {
            Long createTime = vimUser.getCreateTime().longValue();
            sysUser.setCreateTime(createTime);
            sysUser.setUpdateTime(createTime);
        }

        return sysUser;
    }
}