package com.jcloud.admin.config;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.dao.SaTokenDaoRedisJackson;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Sa-Token修复配置
 * 最小侵入性地解决LocalDateTime序列化问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class SaTokenFixConfig {

    /**
     * 配置支持JSR310的SaTokenDao
     * 这是最简单、最稳定的解决方案
     */
    @Bean
    @Primary
    public SaTokenDao saTokenDao() {
        log.info("配置sa-token JSR310支持");
        
        try {
            // 创建SaTokenDaoRedisJackson实例
            SaTokenDaoRedisJackson dao = new SaTokenDaoRedisJackson();
            
            // 通过反射获取并配置ObjectMapper
            java.lang.reflect.Field field = SaTokenDaoRedisJackson.class.getDeclaredField("objectMapper");
            field.setAccessible(true);
            
            ObjectMapper objectMapper = (ObjectMapper) field.get(dao);
            if (objectMapper == null) {
                objectMapper = new ObjectMapper();
            }
            
            // 注册JSR310模块
            objectMapper.registerModule(new JavaTimeModule());
            
            // 设置回去
            field.set(dao, objectMapper);
            
            log.info("sa-token JSR310支持配置成功");
            return dao;
            
        } catch (Exception e) {
            log.error("配置sa-token JSR310支持失败，使用默认配置", e);
            return new SaTokenDaoRedisJackson();
        }
    }
}
