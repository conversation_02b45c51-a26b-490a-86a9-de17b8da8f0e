package com.jcloud.admin.config;

import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.dialect.OperateType;
import com.mybatisflex.core.dialect.impl.CommonsDialectImpl;
import com.mybatisflex.core.query.CPI;
import com.mybatisflex.core.query.QueryTable;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.table.TableInfo;
import com.mybatisflex.core.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * jCloud数据权限方言实现
 * 基于MyBatis-Flex的IDialect接口实现统一的数据权限控制
 * 支持租户隔离、数据权限范围、部门权限等多层次权限控制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class JCloudDataPermissionDialect extends CommonsDialectImpl {
    
    /**
     * 需要进行数据权限过滤的表名
     */
    private static final String[] DATA_PERMISSION_TABLES = {
        "sys_user", "sys_dept", "sys_role", "sys_menu", "sys_user_role", "sys_role_menu"
    };
    
    @Override
    public void prepareAuth(QueryWrapper queryWrapper, OperateType operateType) {
        // 超级管理员跳过数据权限过滤
        if (SecurityUtils.isSuperAdmin()) {
            log.debug("超级管理员跳过数据权限过滤");
            super.prepareAuth(queryWrapper, operateType);
            return;
        }
        
        List<QueryTable> queryTables = CPI.getQueryTables(queryWrapper);
        if (queryTables == null || queryTables.isEmpty()) {
            super.prepareAuth(queryWrapper, operateType);
            return;
        }
        
        for (QueryTable queryTable : queryTables) {
            String tableName = queryTable.getName();
            
            // 检查是否需要进行数据权限过滤
            if (needDataPermissionFilter(tableName)) {
                applyDataPermissionFilter(queryWrapper, tableName, queryTable);
            }
        }
        
        super.prepareAuth(queryWrapper, operateType);
    }
    
    @Override
    public void prepareAuth(String schema, String tableName, StringBuilder sql, OperateType operateType) {
        // 超级管理员跳过数据权限过滤
        if (SecurityUtils.isSuperAdmin()) {
            super.prepareAuth(schema, tableName, sql, operateType);
            return;
        }
        
        if (needDataPermissionFilter(tableName)) {
            applyDataPermissionFilterToSql(tableName, sql, operateType);
        }
        
        super.prepareAuth(schema, tableName, sql, operateType);
    }
    
    @Override
    public void prepareAuth(TableInfo tableInfo, StringBuilder sql, OperateType operateType) {
        // 超级管理员跳过数据权限过滤
        if (SecurityUtils.isSuperAdmin()) {
            super.prepareAuth(tableInfo, sql, operateType);
            return;
        }
        
        String tableName = tableInfo.getTableName();
        if (needDataPermissionFilter(tableName)) {
            applyDataPermissionFilterToSql(tableName, sql, operateType);
        }
        
        super.prepareAuth(tableInfo, sql, operateType);
    }
    
    /**
     * 检查表是否需要进行数据权限过滤
     */
    private boolean needDataPermissionFilter(String tableName) {
        if (StringUtil.isBlank(tableName)) {
            return false;
        }
        
        for (String table : DATA_PERMISSION_TABLES) {
            if (table.equals(tableName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 为QueryWrapper应用数据权限过滤条件
     */
    private void applyDataPermissionFilter(QueryWrapper queryWrapper, String tableName, QueryTable queryTable) {
        String dataScope = SecurityUtils.getDataScope();
        Long currentUserId = SecurityUtils.getUserId();
        Long currentDeptId = SecurityUtils.getDeptId();
        
        log.debug("应用数据权限过滤: table={}, dataScope={}, userId={}, deptId={}", 
                tableName, dataScope, currentUserId, currentDeptId);
        
        switch (dataScope) {
            case "ALL":
                // 全部数据权限，不添加额外过滤条件
                break;
            case "DEPT":
                applyDeptPermissionFilter(queryWrapper, tableName, currentDeptId, false);
                break;
            case "DEPT_AND_SUB":
                applyDeptPermissionFilter(queryWrapper, tableName, currentDeptId, true);
                break;
            case "SELF":
                applySelfPermissionFilter(queryWrapper, tableName, currentUserId);
                break;
            case "CUSTOM":
                applyCustomPermissionFilter(queryWrapper, tableName, currentUserId);
                break;
            default:
                // 默认使用最严格的权限
                applySelfPermissionFilter(queryWrapper, tableName, currentUserId);
                break;
        }
    }
    
    /**
     * 为SQL字符串应用数据权限过滤条件
     */
    private void applyDataPermissionFilterToSql(String tableName, StringBuilder sql, OperateType operateType) {
        String dataScope = SecurityUtils.getDataScope();
        Long currentUserId = SecurityUtils.getUserId();
        Long currentDeptId = SecurityUtils.getDeptId();
        
        switch (dataScope) {
            case "ALL":
                // 全部数据权限，不添加额外过滤条件
                break;
            case "DEPT":
                appendDeptPermissionToSql(sql, tableName, currentDeptId, false);
                break;
            case "DEPT_AND_SUB":
                appendDeptPermissionToSql(sql, tableName, currentDeptId, true);
                break;
            case "SELF":
                appendSelfPermissionToSql(sql, tableName, currentUserId);
                break;
            case "CUSTOM":
                appendCustomPermissionToSql(sql, tableName, currentUserId);
                break;
            default:
                appendSelfPermissionToSql(sql, tableName, currentUserId);
                break;
        }
    }
    
    /**
     * 应用部门权限过滤
     */
    private void applyDeptPermissionFilter(QueryWrapper queryWrapper, String tableName, Long currentDeptId, boolean includeSubDepts) {
        if (currentDeptId == null) {
            // 如果当前用户没有部门，则无法访问任何数据
            queryWrapper.and("1 = 0");
            return;
        }

        switch (tableName) {
            case "sys_user":
                // 用户表：通过用户部门关联表过滤
                applyUserDeptFilter(queryWrapper, currentDeptId, includeSubDepts);
                break;
            case "sys_dept":
                // 部门表：直接过滤部门ID
                applyDeptFilter(queryWrapper, currentDeptId, includeSubDepts);
                break;
            case "sys_role":
            case "sys_menu":
                // 角色和菜单表：通过创建部门过滤（如果有相关字段）
                // 这里可以根据实际业务需求调整
                break;
            default:
                // 其他表：通过创建部门过滤
                queryWrapper.and("create_dept_id = " + currentDeptId);
                break;
        }
    }

    /**
     * 应用用户部门过滤
     */
    private void applyUserDeptFilter(QueryWrapper queryWrapper, Long currentDeptId, boolean includeSubDepts) {
        if (includeSubDepts) {
            // 包含子部门：需要查询部门树
            queryWrapper.and("EXISTS (SELECT 1 FROM sys_user_dept ud " +
                           "INNER JOIN sys_dept d ON ud.dept_id = d.id " +
                           "WHERE ud.user_id = sys_user.id AND " +
                           "(d.id = " + currentDeptId + " OR d.parent_id = " + currentDeptId + " OR d.ancestors LIKE '%," + currentDeptId + ",%'))");
        } else {
            // 仅本部门
            queryWrapper.and("EXISTS (SELECT 1 FROM sys_user_dept ud " +
                           "WHERE ud.user_id = sys_user.id AND ud.dept_id = " + currentDeptId + ")");
        }
    }

    /**
     * 应用部门过滤
     */
    private void applyDeptFilter(QueryWrapper queryWrapper, Long currentDeptId, boolean includeSubDepts) {
        if (includeSubDepts) {
            // 包含子部门
            queryWrapper.and("(id = " + currentDeptId + " OR parent_id = " + currentDeptId + " OR ancestors LIKE '%," + currentDeptId + ",%')");
        } else {
            // 仅本部门
            queryWrapper.and("id = " + currentDeptId);
        }
    }
    
    /**
     * 应用个人权限过滤
     */
    private void applySelfPermissionFilter(QueryWrapper queryWrapper, String tableName, Long currentUserId) {
        if (currentUserId == null) {
            queryWrapper.and("1 = 0");
            return;
        }
        
        if ("sys_user".equals(tableName)) {
            queryWrapper.and("id = " + currentUserId);
        } else {
            // 其他表：通过创建人过滤
            queryWrapper.and("create_by = " + currentUserId);
        }
    }
    
    /**
     * 应用自定义权限过滤
     */
    private void applyCustomPermissionFilter(QueryWrapper queryWrapper, String tableName, Long currentUserId) {
        // TODO: 实现自定义权限逻辑
        // 暂时使用个人权限
        applySelfPermissionFilter(queryWrapper, tableName, currentUserId);
    }
    
    /**
     * 为SQL添加部门权限条件
     */
    private void appendDeptPermissionToSql(StringBuilder sql, String tableName, Long currentDeptId, boolean includeSubDepts) {
        if (currentDeptId == null) {
            sql.append(" AND 1 = 0");
            return;
        }
        
        if ("sys_user".equals(tableName)) {
            sql.append(" AND EXISTS (SELECT 1 FROM sys_user_dept ud WHERE ud.user_id = ").append(wrap("id"))
               .append(" AND ud.dept_id = ").append(currentDeptId).append(")");
        } else if ("sys_dept".equals(tableName)) {
            sql.append(" AND ").append(wrap("id")).append(" = ").append(currentDeptId);
        }
    }
    
    /**
     * 为SQL添加个人权限条件
     */
    private void appendSelfPermissionToSql(StringBuilder sql, String tableName, Long currentUserId) {
        if (currentUserId == null) {
            sql.append(" AND 1 = 0");
            return;
        }
        
        if ("sys_user".equals(tableName)) {
            sql.append(" AND ").append(wrap("id")).append(" = ").append(currentUserId);
        } else {
            sql.append(" AND ").append(wrap("create_by")).append(" = ").append(currentUserId);
        }
    }
    
    /**
     * 为SQL添加自定义权限条件
     */
    private void appendCustomPermissionToSql(StringBuilder sql, String tableName, Long currentUserId) {
        // TODO: 实现自定义权限逻辑
        appendSelfPermissionToSql(sql, tableName, currentUserId);
    }
}
