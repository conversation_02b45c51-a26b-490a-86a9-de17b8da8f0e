package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysLoginLogService;
import com.jcloud.admin.service.SysOperLogService;
import com.jcloud.common.dto.LoginLogQueryRequest;
import com.jcloud.common.dto.OperLogQueryRequest;
import com.jcloud.common.entity.SysLoginLog;
import com.jcloud.common.entity.SysOperLog;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统监控控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "系统监控", description = "系统监控相关接口")
@RestController
@RequestMapping("/system/monitor")
@RequiredArgsConstructor
public class SysMonitorController {
    
    private final SysOperLogService operLogService;
    private final SysLoginLogService loginLogService;
    
    // ==================== 操作日志管理 ====================
    
    @Operation(summary = "分页查询操作日志", description = "根据条件分页查询操作日志列表")
    @SaCheckPermission("system:operlog:list")
    @GetMapping("/operlog/page")
    public Result<PageResult<SysOperLog>> pageOperLogs(@Valid OperLogQueryRequest queryRequest) {
        PageResult<SysOperLog> pageResult = operLogService.pageOperLogs(queryRequest);
        return Result.success("查询操作日志列表成功", pageResult);
    }
    
    @Operation(summary = "删除操作日志", description = "根据日志ID删除操作日志")
    @SaCheckPermission("system:operlog:remove")
    @DeleteMapping("/operlog/{id}")
    public Result<Void> deleteOperLog(@Parameter(description = "日志ID") @PathVariable("id") Long id) {
        boolean success = operLogService.removeById(id);
        if (success) {
            return Result.<Void>success("删除操作日志成功", null);
        } else {
            return Result.error("删除操作日志失败");
        }
    }
    
    @Operation(summary = "批量删除操作日志", description = "根据日志ID列表批量删除操作日志")
    @SaCheckPermission("system:operlog:remove")
    @DeleteMapping("/operlog/batch")
    public Result<Void> deleteOperLogsBatch(@Parameter(description = "日志ID列表") @RequestBody List<Long> logIds) {
        boolean success = operLogService.deleteOperLogsBatch(logIds);
        if (success) {
            return Result.<Void>success("批量删除操作日志成功", null);
        } else {
            return Result.error("批量删除操作日志失败");
        }
    }
    
    @Operation(summary = "清空操作日志", description = "清空所有操作日志")
    @SaCheckPermission("system:operlog:remove")
    @DeleteMapping("/operlog/clear")
    public Result<Void> clearOperLogs() {
        boolean success = operLogService.clearAllOperLogs();
        if (success) {
            return Result.<Void>success("清空操作日志成功", null);
        } else {
            return Result.error("清空操作日志失败");
        }
    }
    
    @Operation(summary = "清理历史操作日志", description = "清理指定天数之前的操作日志")
    @SaCheckPermission("system:operlog:remove")
    @DeleteMapping("/operlog/clean")
    public Result<Integer> cleanOldOperLogs(@Parameter(description = "保留天数") @RequestParam("days") int days) {
        int count = operLogService.cleanOldOperLogs(days);
        return Result.success("清理历史操作日志成功", count);
    }
    
    // ==================== 登录日志管理 ====================
    
    @Operation(summary = "分页查询登录日志", description = "根据条件分页查询登录日志列表")
    @SaCheckPermission("system:loginlog:list")
    @GetMapping("/loginlog/page")
    public Result<PageResult<SysLoginLog>> pageLoginLogs(@Valid LoginLogQueryRequest queryRequest) {
        PageResult<SysLoginLog> pageResult = loginLogService.pageLoginLogs(queryRequest);
        return Result.success("查询登录日志列表成功", pageResult);
    }
    
    @Operation(summary = "删除登录日志", description = "根据日志ID删除登录日志")
    @SaCheckPermission("system:loginlog:remove")
    @DeleteMapping("/loginlog/{id}")
    public Result<Void> deleteLoginLog(@Parameter(description = "日志ID") @PathVariable("id") Long id) {
        boolean success = loginLogService.removeById(id);
        if (success) {
            return Result.<Void>success("删除登录日志成功", null);
        } else {
            return Result.error("删除登录日志失败");
        }
    }
    
    @Operation(summary = "批量删除登录日志", description = "根据日志ID列表批量删除登录日志")
    @SaCheckPermission("system:loginlog:remove")
    @DeleteMapping("/loginlog/batch")
    public Result<Void> deleteLoginLogsBatch(@Parameter(description = "日志ID列表") @RequestBody List<Long> logIds) {
        boolean success = loginLogService.deleteLoginLogsBatch(logIds);
        if (success) {
            return Result.<Void>success("批量删除登录日志成功", null);
        } else {
            return Result.error("批量删除登录日志失败");
        }
    }
    
    @Operation(summary = "清空登录日志", description = "清空所有登录日志")
    @SaCheckPermission("system:loginlog:remove")
    @DeleteMapping("/loginlog/clear")
    public Result<Void> clearLoginLogs() {
        boolean success = loginLogService.clearAllLoginLogs();
        if (success) {
            return Result.<Void>success("清空登录日志成功", null);
        } else {
            return Result.error("清空登录日志失败");
        }
    }
    
    @Operation(summary = "清理历史登录日志", description = "清理指定天数之前的登录日志")
    @SaCheckPermission("system:loginlog:remove")
    @DeleteMapping("/loginlog/clean")
    public Result<Integer> cleanOldLoginLogs(@Parameter(description = "保留天数") @RequestParam("days") int days) {
        int count = loginLogService.cleanOldLoginLogs(days);
        return Result.success("清理历史登录日志成功", count);
    }
    
    // ==================== 系统监控 ====================
    
    @Operation(summary = "获取系统信息", description = "获取系统基本信息")
    @SaCheckPermission("system:monitor:list")
    @GetMapping("/system/info")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // 运行时信息
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        systemInfo.put("jvmName", runtimeMXBean.getVmName());
        systemInfo.put("jvmVersion", runtimeMXBean.getVmVersion());
        systemInfo.put("jvmVendor", runtimeMXBean.getVmVendor());
        systemInfo.put("startTime", runtimeMXBean.getStartTime());
        systemInfo.put("uptime", runtimeMXBean.getUptime());
        
        // 操作系统信息
        OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
        systemInfo.put("osName", osMXBean.getName());
        systemInfo.put("osVersion", osMXBean.getVersion());
        systemInfo.put("osArch", osMXBean.getArch());
        systemInfo.put("availableProcessors", osMXBean.getAvailableProcessors());
        
        // 内存信息
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        systemInfo.put("heapMemoryUsed", memoryMXBean.getHeapMemoryUsage().getUsed());
        systemInfo.put("heapMemoryMax", memoryMXBean.getHeapMemoryUsage().getMax());
        systemInfo.put("nonHeapMemoryUsed", memoryMXBean.getNonHeapMemoryUsage().getUsed());
        systemInfo.put("nonHeapMemoryMax", memoryMXBean.getNonHeapMemoryUsage().getMax());
        
        return Result.success("获取系统信息成功", systemInfo);
    }
    
    @Operation(summary = "获取系统统计信息", description = "获取系统统计数据")
    @SaCheckPermission("system:monitor:list")
    @GetMapping("/system/stats")
    public Result<Map<String, Object>> getSystemStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 今日统计
        stats.put("todayOperCount", operLogService.countTodayOpers());
        stats.put("todayLoginCount", loginLogService.countTodayLogins());
        stats.put("errorOperCount", operLogService.countErrorOpers());
        stats.put("failedLoginCount", loginLogService.countFailedLogins());
        
        // 业务类型统计
        stats.put("operByBusinessType", operLogService.countByBusinessType());
        stats.put("loginByStatus", loginLogService.countByStatus());
        
        return Result.success("获取系统统计信息成功", stats);
    }
    
    @Operation(summary = "获取最近日志", description = "获取最近的操作和登录日志")
    @SaCheckPermission("system:monitor:list")
    @GetMapping("/recent/logs")
    public Result<Map<String, Object>> getRecentLogs() {
        Map<String, Object> logs = new HashMap<>();
        
        logs.put("recentOperLogs", operLogService.getRecentOperLogs(10));
        logs.put("recentLoginLogs", loginLogService.getRecentLoginLogs(10));
        
        return Result.success("获取最近日志成功", logs);
    }
}
