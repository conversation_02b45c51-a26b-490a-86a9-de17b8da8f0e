package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysOperLogService;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.OperLogQueryRequest;
import com.jcloud.common.entity.SysOperLog;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysOperLogMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jcloud.common.util.TimeUtil;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysOperLogServiceImpl extends BaseServiceImpl<SysOperLogMapper, SysOperLog> implements SysOperLogService {
    
    @Override
    public PageResult<SysOperLog> pageOperLogs(OperLogQueryRequest queryRequest) {
        QueryWrapper queryWrapper = getQueryWrapper();
        
        // 构建查询条件
        if (StrUtil.isNotBlank(queryRequest.getTitle())) {
            queryWrapper.like("title", queryRequest.getTitle());
        }
        if (queryRequest.getBusinessType() != null) {
            queryWrapper.eq("business_type", queryRequest.getBusinessType());
        }
        if (StrUtil.isNotBlank(queryRequest.getOperName())) {
            queryWrapper.like("oper_name", queryRequest.getOperName());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getOperTimeStart() != null) {
            queryWrapper.ge("oper_time", queryRequest.getOperTimeStart());
        }
        if (queryRequest.getOperTimeEnd() != null) {
            queryWrapper.le("oper_time", queryRequest.getOperTimeEnd());
        }
        
        // 排序
        queryWrapper.orderBy("oper_time", false);
        
        // 分页查询
        Page<SysOperLog> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysOperLog> pageResult = baseMapper.paginate(page, queryWrapper);
        
        return PageResult.of(pageResult.getRecords(), pageResult.getTotalRow(), 
                           queryRequest.getPageNum(), queryRequest.getPageSize());
    }
    
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void recordOperLog(SysOperLog operLog) {
        try {
            // 设置租户信息
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId != null) {
                operLog.setTenantId(tenantId);
            }

            // 设置创建信息
            operLog.setCreateTime(TimeUtil.now());
            Long userId = SecurityUtils.getUserId();
            if (userId != null) {
                operLog.setCreateBy(userId);
            }

            boolean success = save(operLog);
            if (success) {
                log.debug("记录操作日志成功：{} - {}", operLog.getTitle(), operLog.getOperName());
            } else {
                log.warn("记录操作日志失败：{} - {}", operLog.getTitle(), operLog.getOperName());
            }
        } catch (Exception e) {
            log.error("记录操作日志异常：{} - {}", operLog.getTitle(), operLog.getOperName(), e);
        }
    }
    
    @Override
    public List<SysOperLog> getOperLogsByOperName(String operName) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByOperName(operName, tenantId);
    }
    
    @Override
    public List<SysOperLog> getOperLogsByBusinessType(Integer businessType) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByBusinessType(businessType, tenantId);
    }
    
    @Override
    public List<SysOperLog> getOperLogsByStatus(Integer status) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByStatus(status, tenantId);
    }
    
    @Override
    public List<SysOperLog> getRecentOperLogs(int limit) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectRecentLogs(limit, tenantId);
    }
    
    @Override
    public List<Map<String, Object>> countByBusinessType() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countByBusinessType(tenantId);
    }
    
    @Override
    public int countTodayOpers() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countTodayOpers(tenantId);
    }
    
    @Override
    public int countErrorOpers() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countErrorOpers(tenantId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanOldOperLogs(int days) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        int count = baseMapper.cleanOldLogs(days, tenantId);
        log.info("清理{}天前的操作日志，共清理{}条", days, count);
        return count;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOperLogsBatch(List<Long> logIds) {
        if (logIds == null || logIds.isEmpty()) {
            return true;
        }

        boolean success = removeByIds(logIds);
        if (success) {
            log.info("批量删除操作日志成功，数量：{}", logIds.size());
        }
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAllOperLogs() {
        QueryWrapper queryWrapper = getQueryWrapper();
        boolean success = baseMapper.deleteByQuery(queryWrapper) > 0;
        if (success) {
            log.info("清空所有操作日志成功");
        }
        return success;
    }
}
