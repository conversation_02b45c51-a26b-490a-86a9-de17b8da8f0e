package com.jcloud.admin.service;

import com.jcloud.common.dto.OperLogQueryRequest;
import com.jcloud.common.entity.SysOperLog;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 操作日志服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysOperLogService extends BaseService<SysOperLog> {
    
    /**
     * 分页查询操作日志列表
     * @param queryRequest 查询条件
     * @return 操作日志分页列表
     */
    PageResult<SysOperLog> pageOperLogs(OperLogQueryRequest queryRequest);
    
    /**
     * 记录操作日志
     * @param operLog 操作日志
     * @return 是否成功
     */
    void recordOperLog(SysOperLog operLog);
    
    /**
     * 根据操作人员查询操作日志
     * 
     * @param operName 操作人员
     * @return 操作日志列表
     */
    List<SysOperLog> getOperLogsByOperName(String operName);
    
    /**
     * 根据业务类型查询操作日志
     * 
     * @param businessType 业务类型
     * @return 操作日志列表
     */
    List<SysOperLog> getOperLogsByBusinessType(Integer businessType);
    
    /**
     * 根据操作状态查询操作日志
     * 
     * @param status 操作状态
     * @return 操作日志列表
     */
    List<SysOperLog> getOperLogsByStatus(Integer status);
    
    /**
     * 获取最近的操作日志
     * 
     * @param limit 限制数量
     * @return 操作日志列表
     */
    List<SysOperLog> getRecentOperLogs(int limit);
    
    /**
     * 统计各业务类型的操作数量
     * 
     * @return 统计结果
     */
    List<Map<String, Object>> countByBusinessType();
    
    /**
     * 统计今日操作数量
     * 
     * @return 操作数量
     */
    int countTodayOpers();
    
    /**
     * 统计异常操作数量
     * 
     * @return 异常操作数量
     */
    int countErrorOpers();
    
    /**
     * 清理指定天数之前的操作日志
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanOldOperLogs(int days);
    
    /**
     * 批量删除操作日志
     * 
     * @param logIds 日志ID列表
     * @return 是否成功
     */
    boolean deleteOperLogsBatch(List<Long> logIds);
    
    /**
     * 清空所有操作日志
     * 
     * @return 是否成功
     */
    boolean clearAllOperLogs();
}
