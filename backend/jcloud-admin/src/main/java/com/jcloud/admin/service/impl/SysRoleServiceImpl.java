package com.jcloud.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysRoleService;
import com.jcloud.admin.service.SysRoleMenuService;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.RoleCreateRequest;
import com.jcloud.common.dto.RoleQueryRequest;
import com.jcloud.common.dto.RoleUpdateRequest;
import com.jcloud.common.entity.SysRole;

import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysRoleMapper;

import com.jcloud.common.mapper.SysUserRoleMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.LambdaGetter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    
    // 注意：权限分配功能已整合到菜单分配中，不再需要 SysRolePermissionMapper
    private final SysUserRoleMapper userRoleMapper;
    private final SysRoleMenuService roleMenuService;
    
    @Override
    public PageResult<SysRole> pageRoles(RoleQueryRequest queryRequest) {
        // 参数验证
        Assert.notNull(queryRequest, "查询参数不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            QueryWrapper queryWrapper = getQueryWrapper();
            
            // 构建查询条件
            // 优先使用keyword进行OR查询
            if (StrUtil.isNotBlank(queryRequest.getKeyword())) {
                String keyword = queryRequest.getKeyword().trim();
                queryWrapper.where(SysRole::getRoleCode).like(keyword).or(SysRole::getRoleName).like(keyword);
                log.debug("使用关键词搜索: {}", keyword);
            } else {
                // 如果没有keyword，则使用具体字段查询
                if (StrUtil.isNotBlank(queryRequest.getRoleCode())) {
                    queryWrapper.like(SysRole::getRoleCode, queryRequest.getRoleCode().trim());
                }
                if (StrUtil.isNotBlank(queryRequest.getRoleName())) {
                    queryWrapper.like(SysRole::getRoleName, queryRequest.getRoleName().trim());
                }
            }
            if (StrUtil.isNotBlank(queryRequest.getRoleType())) {
                queryWrapper.eq("role_type", queryRequest.getRoleType().trim());
            }
            if (StrUtil.isNotBlank(queryRequest.getDataScope())) {
                queryWrapper.eq("data_scope", queryRequest.getDataScope().trim());
            }
            if (queryRequest.getStatus() != null) {
                queryWrapper.eq("status", queryRequest.getStatus());
            }
            if (queryRequest.getCreateTimeStart() != null) {
                queryWrapper.ge("create_time", queryRequest.getCreateTimeStart());
            }
            if (queryRequest.getCreateTimeEnd() != null) {
                queryWrapper.le("create_time", queryRequest.getCreateTimeEnd());
            }
            
            // 排序
            queryWrapper.orderBy("sort_order", true)
                       .orderBy("create_time", false);
            
            // 分页查询
            Page<SysRole> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
            Page<SysRole> pageResult = baseMapper.paginate(page, queryWrapper);
            
            log.debug("分页查询角色列表成功，总数：{}，当前页：{}，每页大小：{}", 
                     pageResult.getTotalRow(), queryRequest.getPageNum(), queryRequest.getPageSize());
            
            return PageResult.of(pageResult.getRecords(), pageResult.getTotalRow(), 
                               queryRequest.getPageNum(), queryRequest.getPageSize());
        } catch (Exception e) {
            log.error("分页查询角色列表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询角色列表失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(RoleCreateRequest createRequest) {
        // 参数验证
        Assert.notNull(createRequest, "创建请求不能为空");
        Assert.hasText(createRequest.getRoleCode(), "角色编码不能为空");
        Assert.hasText(createRequest.getRoleName(), "角色名称不能为空");
        Assert.hasText(createRequest.getRoleType(), "角色类型不能为空");
        Assert.hasText(createRequest.getDataScope(), "数据权限范围不能为空");
        Assert.notNull(createRequest.getStatus(), "状态不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            // 验证角色编码格式
            validateRoleCode(createRequest.getRoleCode());
            
            // 验证角色类型和数据权限范围
            validateRoleType(createRequest.getRoleType());
            validateDataScope(createRequest.getDataScope());
            
            // 检查角色编码是否存在
            if (isRoleCodeExists(createRequest.getRoleCode().trim(), null)) {
                throw new BusinessException(ResultCode.CONFLICT, "角色编码已存在");
            }
            
            // 检查角色名称是否存在
            if (isRoleNameExists(createRequest.getRoleName().trim(), null)) {
                throw new BusinessException(ResultCode.CONFLICT, "角色名称已存在");
            }
            
            // 创建角色
            SysRole role = new SysRole();
            BeanUtils.copyProperties(createRequest, role);
            // 清理字符串字段
            role.setRoleCode(createRequest.getRoleCode().trim().toUpperCase());
            role.setRoleName(createRequest.getRoleName().trim());
            role.setRoleType(createRequest.getRoleType().trim());
            role.setDataScope(createRequest.getDataScope().trim());
            if (StrUtil.isNotBlank(createRequest.getRemark())) {
                role.setRemark(createRequest.getRemark().trim());
            }
            
            setBaseInfo(role, false);
            
            boolean success = save(role);
            if (!success) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建角色失败");
            }
            
            // 注意：权限分配功能已整合到菜单分配中
            
            log.info("创建角色成功，角色编码：{}，角色名称：{}", role.getRoleCode(), role.getRoleName());
            return true;
        } catch (BusinessException e) {
            log.error("创建角色失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建角色失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建角色失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(RoleUpdateRequest updateRequest) {
        // 参数验证
        Assert.notNull(updateRequest, "更新请求不能为空");
        Assert.notNull(updateRequest.getId(), "角色ID不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            SysRole existingRole = getById(updateRequest.getId());
            if (existingRole == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "角色不存在");
            }
            
            // 检查是否为系统角色（系统角色不允许修改某些字段）
            if ("SYSTEM".equals(existingRole.getRoleType())) {
                // 系统角色只允许修改备注和排序
                if (updateRequest.getRoleName() != null || 
                    updateRequest.getDataScope() != null || 
                    updateRequest.getStatus() != null) {
                    throw new BusinessException(ResultCode.BAD_REQUEST, "系统角色不允许修改核心属性");
                }
            }
            
            // 验证数据权限范围
            if (StrUtil.isNotBlank(updateRequest.getDataScope())) {
                validateDataScope(updateRequest.getDataScope());
            }
            
            // 检查角色名称是否存在（排除当前角色）
            if (StrUtil.isNotBlank(updateRequest.getRoleName()) && 
                isRoleNameExists(updateRequest.getRoleName().trim(), updateRequest.getId())) {
                throw new BusinessException(ResultCode.CONFLICT, "角色名称已存在");
            }
            
            // 更新角色信息
            SysRole role = new SysRole();
            BeanUtils.copyProperties(updateRequest, role);
            
            // 清理字符串字段
            if (StrUtil.isNotBlank(updateRequest.getRoleName())) {
                role.setRoleName(updateRequest.getRoleName().trim());
            }
            if (StrUtil.isNotBlank(updateRequest.getDataScope())) {
                role.setDataScope(updateRequest.getDataScope().trim());
            }
            if (StrUtil.isNotBlank(updateRequest.getRemark())) {
                role.setRemark(updateRequest.getRemark().trim());
            }
            
            setBaseInfo(role, true);
            
            boolean success = updateById(role);
            if (!success) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新角色失败");
            }
            
            // 注意：权限分配功能已整合到菜单分配中
            
            log.info("更新角色成功，角色ID：{}，角色名称：{}", role.getId(), 
                    role.getRoleName() != null ? role.getRoleName() : existingRole.getRoleName());
            return true;
        } catch (BusinessException e) {
            log.error("更新角色失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新角色失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新角色失败：" + e.getMessage());
        }
    }
    
    @Override
    public SysRole getRoleByCode(String roleCode) {
        // 参数验证
        Assert.hasText(roleCode, "角色编码不能为空");
        
        try {
            String trimmedCode = roleCode.trim().toUpperCase();
            Long tenantId = SecurityUtils.getTenantId();
            
            SysRole role;
            if (tenantId == null || SecurityUtils.isSuperAdmin()) {
                role = baseMapper.selectByRoleCodeWithoutTenant(trimmedCode);
            } else {
                role = baseMapper.selectByRoleCode(trimmedCode, tenantId);
            }
            
            log.debug("根据角色编码查询角色成功，角色编码：{}，结果：{}", trimmedCode, role != null ? "找到" : "未找到");
            return role;
        } catch (Exception e) {
            log.error("根据角色编码查询角色失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询角色失败：" + e.getMessage());
        }
    }
    
    @Override
    public boolean isRoleCodeExists(String roleCode, Long excludeRoleId) {
        // 参数验证
        Assert.hasText(roleCode, "角色编码不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
            }
            
            String trimmedCode = roleCode.trim().toUpperCase();
            boolean exists = baseMapper.existsByRoleCode(trimmedCode, excludeRoleId, tenantId);
            
            log.debug("检查角色编码是否存在，角色编码：{}，排除ID：{}，结果：{}", trimmedCode, excludeRoleId, exists);
            return exists;
        } catch (BusinessException e) {
            log.error("检查角色编码是否存在失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("检查角色编码是否存在失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "检查角色编码失败：" + e.getMessage());
        }
    }
    
    @Override
    public boolean isRoleNameExists(String roleName, Long excludeRoleId) {
        // 参数验证
        Assert.hasText(roleName, "角色名称不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
            }
            
            String trimmedName = roleName.trim();
            boolean exists = baseMapper.existsByRoleName(trimmedName, excludeRoleId, tenantId);
            
            log.debug("检查角色名称是否存在，角色名称：{}，排除ID：{}，结果：{}", trimmedName, excludeRoleId, exists);
            return exists;
        } catch (BusinessException e) {
            log.error("检查角色名称是否存在失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("检查角色名称是否存在失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "检查角色名称失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatus(Long roleId, Integer status) {
        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        validateRoleStatus(status);
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            SysRole role = getById(roleId);
            if (role == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "角色不存在");
            }
            
            // 检查是否为系统角色
            if ("SYSTEM".equals(role.getRoleType()) && !SecurityUtils.isSuperAdmin()) {
                throw new BusinessException(ResultCode.FORBIDDEN, "只有超级管理员才能修改系统角色状态");
            }
            
            // 如果要禁用角色，检查是否有用户正在使用
            if (CommonConstants.STATUS_DISABLED.equals(status)) {
                int userCount = countUsersByRoleId(roleId);
                if (userCount > 0) {
                    log.warn("角色{}下还有{}个用户，禁用后这些用户将失去该角色权限", role.getRoleName(), userCount);
                }
            }

            role.setStatus(status);
            setBaseInfo(role, true);

            boolean success = updateById(role);
            if (success) {
                log.info("更新角色状态成功，角色ID：{}，角色名称：{}，状态：{}", 
                        roleId, role.getRoleName(), status == 1 ? "启用" : "禁用");
            }
            return success;
        } catch (BusinessException e) {
            log.error("更新角色状态失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新角色状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatusBatch(List<Long> roleIds, Integer status) {
        if (roleIds == null || roleIds.isEmpty()) {
            return true;
        }

        for (Long roleId : roleIds) {
            updateRoleStatus(roleId, status);
        }

        log.info("批量更新角色状态成功，角色数量：{}，状态：{}", roleIds.size(), status);
        return true;
    }

    // 注意：权限分配功能已整合到菜单分配中，不再需要独立的权限分配方法

    // 注意：权限查询功能已整合到菜单权限服务中，不再需要独立的权限查询方法

    @Override
    public List<SysRole> getRolesByUserId(Long userId) {
        // 参数验证
        Assert.notNull(userId, "用户ID不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
            }
            
            List<SysRole> roles = baseMapper.selectRolesByUserId(userId, tenantId);
            log.debug("获取用户角色列表成功，用户ID：{}，角色数量：{}", userId, roles.size());
            return roles;
        } catch (BusinessException e) {
            log.error("获取用户角色列表失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取用户角色列表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取用户角色列表失败：" + e.getMessage());
        }
    }

    @Override
    public Set<String> getRoleCodesByUserId(Long userId) {
        // 参数验证
        Assert.notNull(userId, "用户ID不能为空");
        
        try {
            List<SysRole> roles = getRolesByUserId(userId);
            Set<String> roleCodes = roles.stream()
                    .map(SysRole::getRoleCode)
                    .collect(Collectors.toSet());
            
            log.debug("获取用户角色编码列表成功，用户ID：{}，角色编码数量：{}", userId, roleCodes.size());
            return roleCodes;
        } catch (Exception e) {
            log.error("获取用户角色编码列表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取用户角色编码列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<SysRole> getAllEnabledRoles() {
        // 验证租户权限
        validateTenantAccess();
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
            }
            
            List<SysRole> roles = baseMapper.selectEnabledRolesByTenantId(tenantId);
            log.debug("获取启用角色列表成功，角色数量：{}", roles.size());
            return roles;
        } catch (BusinessException e) {
            log.error("获取启用角色列表失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取启用角色列表失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取启用角色列表失败：" + e.getMessage());
        }
    }

    @Override
    public int countUsersByRoleId(Long roleId) {
        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
            }
            
            int count = baseMapper.countUsersByRoleId(roleId, tenantId);
            log.debug("统计角色用户数量成功，角色ID：{}，用户数量：{}", roleId, count);
            return count;
        } catch (BusinessException e) {
            log.error("统计角色用户数量失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("统计角色用户数量失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "统计角色用户数量失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        
        // 验证租户权限
        validateTenantAccess();
        
        try {
            SysRole role = getById(roleId);
            if (role == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "角色不存在");
            }

            // 检查是否为系统角色
            if ("SYSTEM".equals(role.getRoleType())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "系统角色不能删除");
            }
            
            // 只有超级管理员或角色创建者才能删除角色
            if (!SecurityUtils.isSuperAdmin() && !SecurityUtils.getUserId().equals(role.getCreateBy())) {
                throw new BusinessException(ResultCode.FORBIDDEN, "只有超级管理员或角色创建者才能删除角色");
            }

            // 检查是否有用户关联
            int userCount = countUsersByRoleId(roleId);
            if (userCount > 0) {
                throw new BusinessException(ResultCode.BAD_REQUEST,
                        String.format("该角色下还有%d个用户，无法删除", userCount));
            }

            // 注意：角色权限关联已整合到角色菜单关联中，删除角色时会自动清理

            // 删除角色
            boolean success = removeById(roleId);
            if (success) {
                log.info("删除角色成功，角色ID：{}，角色名称：{}", roleId, role.getRoleName());
            } else {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除角色失败");
            }
            return success;
        } catch (BusinessException e) {
            log.error("删除角色失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("删除角色失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRolesBatch(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return true;
        }

        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }

        log.info("批量删除角色成功，角色数量：{}", roleIds.size());
        return true;
    }
    
    /**
     * 验证租户访问权限
     */
    private void validateTenantAccess() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null && !SecurityUtils.isSuperAdmin()) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
    }
    
    /**
     * 验证角色编码格式
     */
    private void validateRoleCode(String roleCode) {
        if (StrUtil.isBlank(roleCode)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色编码不能为空");
        }
        
        String trimmedCode = roleCode.trim();
        if (trimmedCode.length() > 50) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色编码长度不能超过50个字符");
        }
        
        // 验证角色编码格式：只能包含大写字母、数字和下划线，且必须以大写字母开头
        if (!trimmedCode.matches("^[A-Z][A-Z0-9_]*$")) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色编码只能包含大写字母、数字和下划线，且必须以大写字母开头");
        }
    }
    
    /**
     * 验证角色类型
     */
    private void validateRoleType(String roleType) {
        if (StrUtil.isBlank(roleType)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色类型不能为空");
        }
        
        String trimmedType = roleType.trim();
        if (!"SYSTEM".equals(trimmedType) && !"CUSTOM".equals(trimmedType)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色类型只能是SYSTEM或CUSTOM");
        }
        
        // 只有超级管理员才能创建系统角色
        if ("SYSTEM".equals(trimmedType) && !SecurityUtils.isSuperAdmin()) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有超级管理员才能创建系统角色");
        }
    }
    
    /**
     * 验证数据权限范围
     */
    private void validateDataScope(String dataScope) {
        if (StrUtil.isBlank(dataScope)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "数据权限范围不能为空");
        }
        
        String trimmedScope = dataScope.trim();
        String[] validScopes = {"ALL", "DEPT", "DEPT_AND_SUB", "SELF", "CUSTOM"};
        boolean isValid = false;
        for (String validScope : validScopes) {
            if (validScope.equals(trimmedScope)) {
                isValid = true;
                break;
            }
        }
        
        if (!isValid) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "数据权限范围值不正确");
        }
        
        // 只有管理员才能设置ALL权限范围
        if ("ALL".equals(trimmedScope) && !SecurityUtils.isAdmin()) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有管理员才能设置全部数据权限");
        }
    }
    
    /**
     * 验证角色状态
     */
    private void validateRoleStatus(Integer status) {
        if (status == null) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色状态不能为空");
        }
        
        if (status != CommonConstants.STATUS_ENABLED && status != CommonConstants.STATUS_DISABLED) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色状态值不正确");
        }
    }
    
    // 注意：权限验证功能已整合到菜单权限服务中

    // ==================== 菜单分配相关方法 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMenus(Long roleId, List<Long> menuIds) {
        log.info("开始为角色分配菜单: roleId={}, menuIds={}", roleId, menuIds);

        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        if (roleId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色ID必须大于0");
        }

        // 验证角色是否存在
        SysRole role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "角色不存在");
        }

        // 验证菜单ID列表
        if (menuIds != null && !menuIds.isEmpty()) {
            validateMenuIds(menuIds);
        }

        try {
            // 调用角色菜单服务进行分配
            boolean success = roleMenuService.assignMenusToRole(roleId, menuIds);

            if (success) {
                log.info("为角色分配菜单成功: roleId={}, menuCount={}", roleId,
                    menuIds != null ? menuIds.size() : 0);
            } else {
                log.error("为角色分配菜单失败: roleId={}, menuIds={}", roleId, menuIds);
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "菜单分配失败");
            }

            return success;
        } catch (Exception e) {
            log.error("为角色分配菜单异常: roleId={}, menuIds={}", roleId, menuIds, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "菜单分配异常: " + e.getMessage());
        }
    }

    @Override
    public List<SysMenu> getRoleMenus(Long roleId) {
        log.debug("获取角色菜单列表: roleId={}", roleId);

        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        if (roleId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色ID必须大于0");
        }

        try {
            List<SysMenu> menus = roleMenuService.getMenusByRoleId(roleId);
            log.debug("获取角色菜单列表成功: roleId={}, menuCount={}", roleId, menus.size());
            return menus;
        } catch (Exception e) {
            log.error("获取角色菜单列表异常: roleId={}", roleId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取角色菜单失败: " + e.getMessage());
        }
    }

    @Override
    public List<Long> getRoleMenuIds(Long roleId) {
        log.debug("获取角色菜单ID列表: roleId={}", roleId);

        // 参数验证
        Assert.notNull(roleId, "角色ID不能为空");
        if (roleId <= 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色ID必须大于0");
        }

        try {
            List<Long> menuIds = roleMenuService.getMenuIdsByRoleId(roleId);
            log.debug("获取角色菜单ID列表成功: roleId={}, menuIdCount={}", roleId, menuIds.size());
            return menuIds;
        } catch (Exception e) {
            log.error("获取角色菜单ID列表异常: roleId={}", roleId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取角色菜单ID失败: " + e.getMessage());
        }
    }

    /**
     * 验证菜单ID列表
     *
     * @param menuIds 菜单ID列表
     */
    private void validateMenuIds(List<Long> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return;
        }

        // 检查是否有null或无效的ID
        for (Long menuId : menuIds) {
            if (menuId == null || menuId <= 0) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "菜单ID不能为空或无效");
            }
        }

        // 去重检查
        long distinctCount = menuIds.stream().distinct().count();
        if (distinctCount != menuIds.size()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "菜单ID列表中存在重复项");
        }
    }
}
