package com.jcloud.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * jCloud管理后台启动类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.jcloud")
@EnableTransactionManagement
@MapperScan({"com.jcloud.common.mapper", "com.jcloud.admin.mapper"})
public class JCloudAdminApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(JCloudAdminApplication.class, args);

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
//        System.out.println(encoder.encode("gI5!iS0#bO"));
        System.out.println("=================================");
        System.out.println("jCloud管理后台启动成功！");
        System.out.println("API文档地址: http://localhost:8081/api/doc.html");
        System.out.println("=================================");
    }
}
