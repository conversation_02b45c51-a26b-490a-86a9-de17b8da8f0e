package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 首充统计响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "首充统计响应")
public class FirstRechargeStatsResponse {
    
    /**
     * 首充用户总数
     */
    @Schema(description = "首充用户总数")
    private Integer firstRechargeUserCount;
    
    /**
     * 下级用户总数
     */
    @Schema(description = "下级用户总数")
    private Integer totalSubUserCount;
    
    /**
     * 首充转化率（首充用户数 / 总用户数 × 100%）
     */
    @Schema(description = "首充转化率")
    private java.math.BigDecimal firstRechargeConversionRate;
    
    /**
     * 首充总金额
     */
    @Schema(description = "首充总金额")
    private java.math.BigDecimal totalFirstRechargeAmount;
    
    /**
     * 平均首充金额
     */
    @Schema(description = "平均首充金额")
    private java.math.BigDecimal avgFirstRechargeAmount;
    
    /**
     * 时间区间内首充用户数
     */
    @Schema(description = "时间区间内首充用户数")
    private Integer periodFirstRechargeUserCount;
    
    /**
     * 时间区间内首充总金额
     */
    @Schema(description = "时间区间内首充总金额")
    private java.math.BigDecimal periodFirstRechargeAmount;
    
    /**
     * 时间区间内平均首充金额
     */
    @Schema(description = "时间区间内平均首充金额")
    private java.math.BigDecimal periodAvgFirstRechargeAmount;
    
    /**
     * 查询开始时间（时间戳）
     */
    @Schema(description = "查询开始时间")
    private Integer startTime;
    
    /**
     * 查询结束时间（时间戳）
     */
    @Schema(description = "查询结束时间")
    private Integer endTime;
}
