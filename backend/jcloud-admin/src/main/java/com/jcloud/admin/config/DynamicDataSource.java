package com.jcloud.admin.config;

import com.jcloud.common.config.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源路由器
 * 根据当前线程上下文中的数据源标识来路由到对应的数据源
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {
    
    /**
     * 确定当前查找键
     * 该方法返回的值将作为键来查找目标数据源
     * 
     * @return 数据源标识
     */
    @Override
    protected Object determineCurrentLookupKey() {
        String dataSource = DataSourceContextHolder.getDataSource();
        log.debug("当前使用数据源: {}", dataSource);
        return dataSource;
    }
}