package com.jcloud.admin.service;

import com.jcloud.common.dto.LoginRequest;
import com.jcloud.common.dto.LoginResponse;

import java.util.Map;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuthService {
    
    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);
    
    /**
     * 用户登出
     */
    void logout();
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    Map<String, Object> getCurrentUserInfo();
}