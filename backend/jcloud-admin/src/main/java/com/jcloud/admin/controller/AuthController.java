package com.jcloud.admin.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jcloud.admin.service.AuthService;
import com.jcloud.admin.service.SysMenuService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.annotation.LoginLog;
import com.jcloud.common.annotation.OperLog;
import com.jcloud.common.dto.LoginRequest;
import com.jcloud.common.dto.LoginResponse;
import com.jcloud.common.dto.MenuDTO;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.result.Result;
import com.jcloud.common.util.MenuUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "认证管理", description = "用户认证相关接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final SysUserService userService;
    private final SysMenuService menuService;

    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    @LoginLog("用户登录")
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        LoginResponse loginResponse = authService.login(loginRequest);
        return Result.success("登录成功", loginResponse);
    }

    @Operation(summary = "用户登出", description = "用户登出清除会话")
    @LoginLog("用户登出")
    @OperLog(title = "用户认证", businessType = 0)
    @PostMapping("/logout")
    public Result<Void> logout() {
        authService.logout();
        return Result.<Void>success("登出成功", null);
    }

    @Operation(summary = "刷新令牌", description = "刷新访问令牌")
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refresh() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }

        // 续签token
        StpUtil.renewTimeout(2592000); // 30天

        Map<String, Object> data = new HashMap<>();
        data.put("accessToken", StpUtil.getTokenValue());
        data.put("expiresIn", StpUtil.getTokenTimeout());

        return Result.success("刷新令牌成功", data);
    }

    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @OperLog(title = "用户认证", businessType = 0)
    @GetMapping("/userinfo")
    public Result<Map<String, Object>> getUserInfo() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }

        Map<String, Object> userInfo = authService.getCurrentUserInfo();
        return Result.success("获取用户信息成功", userInfo);
    }

    @Operation(summary = "检查登录状态", description = "检查当前用户是否已登录")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkLogin() {
        Map<String, Object> data = new HashMap<>();
        data.put("isLogin", StpUtil.isLogin());

        if (StpUtil.isLogin()) {
            data.put("userId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
            data.put("tokenTimeout", StpUtil.getTokenTimeout());
        }

        return Result.success("检查登录状态成功", data);
    }

    @Operation(summary = "获取用户权限", description = "获取当前登录用户的权限列表")
    @GetMapping("/permissions")
    public Result<List<String>> getUserPermissions() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        Set<String> permissions = userService.getUserPermissions(userId);

        return Result.success("获取用户权限成功", new ArrayList<>(permissions));
    }

    @Operation(summary = "获取用户角色", description = "获取当前登录用户的角色列表")
    @GetMapping("/roles")
    public Result<List<String>> getUserRoles() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        Set<String> roles = userService.getUserRoles(userId);

        return Result.success("获取用户角色成功", new ArrayList<>(roles));
    }

    @Operation(summary = "获取用户菜单", description = "获取当前登录用户的菜单列表")
    @GetMapping("/menus")
    public Result<List<MenuDTO>> getUserMenus() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            return Result.error("用户未登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        List<SysMenu> sysMenus = menuService.getMenusByUserId(userId);

        // 转换为前端需要的格式并构建树形结构
        List<MenuDTO> menuDTOs = MenuUtils.convertToTreeDTOList(sysMenus);

        return Result.success("获取用户菜单成功", menuDTOs);
    }
}
