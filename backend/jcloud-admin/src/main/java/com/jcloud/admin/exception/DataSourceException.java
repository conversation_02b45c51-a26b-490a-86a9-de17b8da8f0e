package com.jcloud.admin.exception;

/**
 * 数据源异常类
 * 用于处理数据源连接和操作相关的异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DataSourceException extends RuntimeException {

    private final String dataSourceName;
    private final String errorCode;

    public DataSourceException(String message) {
        super(message);
        this.dataSourceName = "unknown";
        this.errorCode = "DS_ERROR";
    }

    public DataSourceException(String message, Throwable cause) {
        super(message, cause);
        this.dataSourceName = "unknown";
        this.errorCode = "DS_ERROR";
    }

    public DataSourceException(String dataSourceName, String message) {
        super(String.format("[%s] %s", dataSourceName, message));
        this.dataSourceName = dataSourceName;
        this.errorCode = "DS_ERROR";
    }

    public DataSourceException(String dataSourceName, String message, Throwable cause) {
        super(String.format("[%s] %s", dataSourceName, message), cause);
        this.dataSourceName = dataSourceName;
        this.errorCode = "DS_ERROR";
    }

    public DataSourceException(String dataSourceName, String errorCode, String message) {
        super(String.format("[%s] %s", dataSourceName, message));
        this.dataSourceName = dataSourceName;
        this.errorCode = errorCode;
    }

    public DataSourceException(String dataSourceName, String errorCode, String message, Throwable cause) {
        super(String.format("[%s] %s", dataSourceName, message), cause);
        this.dataSourceName = dataSourceName;
        this.errorCode = errorCode;
    }

    public String getDataSourceName() {
        return dataSourceName;
    }

    public String getErrorCode() {
        return errorCode;
    }
}