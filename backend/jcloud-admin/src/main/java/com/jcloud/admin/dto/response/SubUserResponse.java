package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 下级用户响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "下级用户响应")
public class SubUserResponse {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer id;
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;
    
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String userimage;
    
    /**
     * 账号状态（1-正常，2-禁用）
     */
    @Schema(description = "账号状态")
    private Integer state;
    
    /**
     * 是否实名认证（0-未实名，1-已实名）
     */
    @Schema(description = "是否实名认证")
    private Integer isauth;
    
    /**
     * 当前货币余额
     */
    @Schema(description = "当前货币余额")
    private java.math.BigDecimal coin;
    
    /**
     * 当前钥匙数量
     */
    @Schema(description = "当前钥匙数量")
    private java.math.BigDecimal key;
    
    /**
     * 注册时间（时间戳）
     */
    @Schema(description = "注册时间")
    private Integer createTime;
    
    /**
     * 最后登录时间（时间戳）
     */
    @Schema(description = "最后登录时间")
    private Integer lastLoginTime;
    
    /**
     * 累计充值金额
     */
    @Schema(description = "累计充值金额")
    private java.math.BigDecimal totalRecharge;
    
    /**
     * 累计消费金额
     */
    @Schema(description = "累计消费金额")
    private java.math.BigDecimal totalConsume;
    
    /**
     * 首充时间（时间戳）
     */
    @Schema(description = "首充时间")
    private Integer firstRechargeTime;
    
    /**
     * 首充金额
     */
    @Schema(description = "首充金额")
    private java.math.BigDecimal firstRechargeAmount;
    
    /**
     * 是否已首充
     */
    @Schema(description = "是否已首充")
    private Boolean hasFirstRecharge;
    
    /**
     * 用户等级
     */
    @Schema(description = "用户等级")
    private Integer level;
    
    /**
     * 用户经验
     */
    @Schema(description = "用户经验")
    private java.math.BigDecimal exp;
    
    /**
     * 邀请用户ID
     */
    @Schema(description = "邀请用户ID")
    private Integer inviteUser;
}
