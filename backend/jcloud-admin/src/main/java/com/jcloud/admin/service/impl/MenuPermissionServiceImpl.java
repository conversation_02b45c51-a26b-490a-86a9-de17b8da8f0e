package com.jcloud.admin.service.impl;

import com.jcloud.admin.service.MenuPermissionService;
import com.jcloud.admin.service.SysRoleMenuService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.mapper.SysMenuMapper;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单权限服务实现类
 * 基于菜单表的统一权限管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class MenuPermissionServiceImpl implements MenuPermissionService {

    private final SysMenuMapper menuMapper;
    private final SysRoleMenuService roleMenuService;
    private final SysUserService userService;

    public MenuPermissionServiceImpl(SysMenuMapper menuMapper,
                                   SysRoleMenuService roleMenuService,
                                   @Lazy SysUserService userService) {
        this.menuMapper = menuMapper;
        this.roleMenuService = roleMenuService;
        this.userService = userService;
    }

    @Override
    // 暂时禁用缓存以调试权限问题
    // @Cacheable(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<String> getUserPermissionCodes(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.info("🔍 获取用户权限编码: userId={}, tenantId={}", userId, tenantId);

        // 检查是否为超级管理员
        SysUser user = userService.getById(userId);
        log.info("🔍 用户信息: user={}", user);

        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            log.info("🎯 超级管理员用户，返回通配符权限: userId={}", userId);
            return List.of("*:*:*");
        }

        List<String> permissionCodes = menuMapper.selectPermissionCodesByUserId(userId, tenantId);
        log.info("🔍 普通用户权限编码查询结果: userId={}, permissionCount={}, permissions={}",
                userId, permissionCodes.size(), permissionCodes);

        return permissionCodes;
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || permissionCode == null || permissionCode.trim().isEmpty()) {
            return false;
        }

        // 检查是否为超级管理员
        SysUser user = userService.getById(userId);
        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            log.debug("超级管理员用户，拥有所有权限: userId={}, permissionCode={}", userId, permissionCode);
            return true;
        }

        List<String> userPermissions = getUserPermissionCodes(userId);
        return userPermissions.contains(permissionCode);
    }

    @Override
    public boolean hasAnyPermission(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }

        // 检查是否为超级管理员
        SysUser user = userService.getById(userId);
        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            log.debug("超级管理员用户，拥有所有权限: userId={}", userId);
            return true;
        }

        List<String> userPermissions = getUserPermissionCodes(userId);
        return Arrays.stream(permissionCodes)
                .anyMatch(userPermissions::contains);
    }

    @Override
    public boolean hasAllPermissions(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }

        // 检查是否为超级管理员
        SysUser user = userService.getById(userId);
        if (user != null && user.getIsAdmin() != null && user.getIsAdmin() == 1) {
            log.debug("超级管理员用户，拥有所有权限: userId={}", userId);
            return true;
        }

        List<String> userPermissions = getUserPermissionCodes(userId);
        return Arrays.stream(permissionCodes)
                .allMatch(userPermissions::contains);
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<String> getRolePermissionCodes(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取角色权限编码: roleId={}, tenantId={}", roleId, tenantId);
        
        List<String> permissionCodes = menuMapper.selectPermissionCodesByRoleId(roleId, tenantId);
        log.debug("角色权限编码查询结果: roleId={}, permissionCount={}", roleId, permissionCodes.size());
        
        return permissionCodes;
    }

    @Override
    public List<SysMenu> getRolePermissionMenus(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取角色权限菜单: roleId={}, tenantId={}", roleId, tenantId);
        
        List<SysMenu> permissionMenus = menuMapper.selectPermissionMenusByRoleId(roleId, tenantId);
        log.debug("角色权限菜单查询结果: roleId={}, menuCount={}", roleId, permissionMenus.size());
        
        return permissionMenus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public boolean assignPermissionsToRole(Long roleId, List<String> permissionCodes) {
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();
        
        log.info("为角色分配权限: roleId={}, permissionCodes={}", roleId, permissionCodes);
        
        try {
            // 1. 删除角色现有的权限关联（只删除按钮类型的菜单关联）
            int deletedCount = menuMapper.deleteRolePermissionMenus(roleId, tenantId);
            log.debug("删除角色现有权限关联: roleId={}, deletedCount={}", roleId, deletedCount);
            
            // 2. 如果权限编码列表不为空，则添加新的关联
            if (permissionCodes != null && !permissionCodes.isEmpty()) {
                // 根据权限编码获取对应的菜单ID
                List<Long> menuIds = menuMapper.selectMenuIdsByPermissionCodes(permissionCodes, tenantId);
                
                if (!menuIds.isEmpty()) {
                    // 使用现有的角色菜单服务来分配菜单
                    boolean success = roleMenuService.assignMenusToRole(roleId, menuIds);
                    if (success) {
                        log.info("角色权限分配成功: roleId={}, assignedMenuCount={}", roleId, menuIds.size());
                        
                        // 清除相关缓存
                        refreshRolePermissionCache(roleId);
                        
                        return true;
                    }
                }
            }
            
            log.info("角色权限分配完成: roleId={}, permissionCount={}", roleId, 
                    permissionCodes != null ? permissionCodes.size() : 0);
            return true;
            
        } catch (Exception e) {
            log.error("角色权限分配失败: roleId={}, permissionCodes={}", roleId, permissionCodes, e);
            throw e;
        }
    }

    @Override
    @Cacheable(value = "allPermissionMenus", key = "T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<SysMenu> getAllPermissionMenus() {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取所有权限菜单: tenantId={}", tenantId);
        
        List<SysMenu> permissionMenus = menuMapper.selectAllPermissionMenus(tenantId);
        log.debug("权限菜单查询结果: tenantId={}, menuCount={}", tenantId, permissionMenus.size());
        
        return permissionMenus;
    }

    @Override
    public SysMenu getPermissionMenuByCode(String permissionCode) {
        if (permissionCode == null || permissionCode.trim().isEmpty()) {
            return null;
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        return menuMapper.selectPermissionMenuByCode(permissionCode, tenantId);
    }

    @Override
    public List<SysMenu> getPermissionMenusByParentId(Long parentId) {
        Long tenantId = SecurityUtils.getTenantId();
        return menuMapper.selectPermissionMenusByParentId(parentId, tenantId);
    }

    @Override
    public List<SysMenu> buildPermissionMenuTree() {
        List<SysMenu> allPermissionMenus = getAllPermissionMenus();
        return buildMenuTree(allPermissionMenus, 0L);
    }

    /**
     * 递归构建菜单树
     */
    private List<SysMenu> buildMenuTree(List<SysMenu> allMenus, Long parentId) {
        return allMenus.stream()
                .filter(menu -> Objects.equals(menu.getParentId(), parentId))
                .peek(menu -> {
                    List<SysMenu> children = buildMenuTree(allMenus, menu.getId());
                    menu.setChildren(children);
                })
                .sorted(Comparator.comparing(SysMenu::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(SysMenu::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public void refreshUserPermissionCache(Long userId) {
        log.debug("刷新用户权限缓存: userId={}", userId);
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public void refreshRolePermissionCache(Long roleId) {
        log.debug("刷新角色权限缓存: roleId={}", roleId);
    }

    @Override
    @CacheEvict(value = {"userPermissions", "rolePermissions", "allPermissionMenus"}, allEntries = true)
    public void clearAllPermissionCache() {
        log.info("清除所有权限缓存");
    }
}
