package com.jcloud.admin.service;

import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysRoleMenu;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 角色菜单关联服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysRoleMenuService extends IService<SysRoleMenu> {
    
    /**
     * 为角色分配菜单
     * 
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean assignMenusToRole(Long roleId, List<Long> menuIds);
    
    /**
     * 根据角色ID获取菜单列表
     * 
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> getMenusByRoleId(Long roleId);
    
    /**
     * 根据角色ID获取菜单ID列表
     * 
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> getMenuIdsByRoleId(Long roleId);
    
    /**
     * 根据用户ID获取菜单列表（通过角色关联）
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> getMenusByUserId(Long userId);
    
    /**
     * 根据角色ID删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteByRoleId(Long roleId);
    
    /**
     * 根据菜单ID删除角色菜单关联
     * 
     * @param menuId 菜单ID
     * @return 是否成功
     */
    boolean deleteByMenuId(Long menuId);
}
