package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysTenantService;
import com.jcloud.common.dto.TenantConfigDTO;
import com.jcloud.common.dto.TenantQueryRequest;
import com.jcloud.common.dto.TenantStatsDTO;
import com.jcloud.common.dto.TenantUserDTO;
import com.jcloud.common.entity.SysTenant;
import com.jcloud.common.result.Result;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/system/tenant")
@RequiredArgsConstructor
@Tag(name = "租户管理", description = "租户管理相关接口")
public class SysTenantController {

    private final SysTenantService tenantService;

    /**
     * 分页查询租户列表
     */
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/page")
    @Operation(summary = "分页查询租户列表", description = "根据条件分页查询租户列表")
    public Result<Page<SysTenant>> pageTenants(@Valid TenantQueryRequest queryRequest) {
        log.info("分页查询租户列表: {}", queryRequest);

        try {
            Page<SysTenant> page = tenantService.pageTenants(queryRequest);
            return Result.success("查询租户列表成功", page);
        } catch (Exception e) {
            log.error("查询租户列表失败", e);
            return Result.error("查询租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的租户
     */
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/enabled")
    @Operation(summary = "获取所有启用的租户", description = "获取所有状态为启用的租户列表")
    public Result<List<SysTenant>> getAllEnabledTenants() {
        log.info("获取所有启用的租户");

        try {
            List<SysTenant> tenants = tenantService.getAllEnabledTenants();
            return Result.success("获取启用租户列表成功", tenants);
        } catch (Exception e) {
            log.error("获取启用租户列表失败", e);
            return Result.error("获取启用租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取租户详情
     */
    @SaCheckPermission("system:tenant:view")
    @GetMapping("/{id}")
    @Operation(summary = "获取租户详情", description = "根据租户ID获取租户详细信息")
    public Result<SysTenant> getTenantById(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id) {
        log.info("获取租户详情: {}", id);

        try {
            SysTenant tenant = tenantService.getTenantById(id);
            if (tenant == null) {
                return Result.error("租户不存在");
            }
            return Result.success("获取租户详情成功", tenant);
        } catch (Exception e) {
            log.error("获取租户详情失败", e);
            return Result.error("获取租户详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建租户
     */
    @SaCheckPermission("system:tenant:add")
    @PostMapping
    @Operation(summary = "创建租户", description = "创建新的租户")
    public Result<SysTenant> createTenant(@Valid @RequestBody SysTenant tenant) {
        log.info("创建租户: {}", tenant);

        try {
            SysTenant createdTenant = tenantService.createTenant(tenant);
            return Result.success("创建租户成功", createdTenant);
        } catch (Exception e) {
            log.error("创建租户失败", e);
            return Result.error("创建租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户
     */
    @SaCheckPermission("system:tenant:edit")
    @PutMapping("/{id}")
    @Operation(summary = "更新租户", description = "更新租户信息")
    public Result<SysTenant> updateTenant(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id,
            @Valid @RequestBody SysTenant tenant) {
        log.info("更新租户: {}, 数据: {}", id, tenant);

        try {
            tenant.setId(id);
            SysTenant updatedTenant = tenantService.updateTenant(tenant);
            return Result.success("更新租户成功", updatedTenant);
        } catch (Exception e) {
            log.error("更新租户失败", e);
            return Result.error("更新租户失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户
     */
    @SaCheckPermission("system:tenant:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除租户", description = "删除指定的租户")
    public Result<Void> deleteTenant(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id) {
        log.info("删除租户: {}", id);

        try {
            boolean success = tenantService.deleteTenant(id);
            if (success) {
                return Result.success("删除租户成功", null);
            } else {
                return Result.error("删除租户失败");
            }
        } catch (Exception e) {
            log.error("删除租户失败", e);
            return Result.error("删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除租户
     */
    @SaCheckPermission("system:tenant:delete")
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除租户", description = "批量删除多个租户")
    public Result<Void> deleteTenants(@RequestBody List<Long> ids) {
        log.info("批量删除租户: {}", ids);

        try {
            boolean success = tenantService.deleteTenants(ids);
            if (success) {
                return Result.success("批量删除租户成功", null);
            } else {
                return Result.error("批量删除租户失败");
            }
        } catch (Exception e) {
            log.error("批量删除租户失败", e);
            return Result.error("批量删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户状态
     */
    @SaCheckPermission("system:tenant:toggle-status")
    @PutMapping("/{id}/status")
    @Operation(summary = "更新租户状态", description = "启用或禁用租户")
    public Result<Void> updateTenantStatus(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id,
            @Parameter(description = "状态：0-禁用，1-启用", required = true)
            @RequestParam("status") Integer status) {
        log.info("更新租户状态: {}, 状态: {}", id, status);

        try {
            boolean success = tenantService.updateTenantStatus(id, status);
            if (success) {
                String action = status == 1 ? "启用" : "禁用";
                return Result.success(action + "租户成功", null);
            } else {
                return Result.error("更新租户状态失败");
            }
        } catch (Exception e) {
            log.error("更新租户状态失败", e);
            return Result.error("更新租户状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户编码是否存在
     */
    @SaCheckPermission("system:tenant:view")
    @GetMapping("/check-code")
    @Operation(summary = "检查租户编码", description = "检查租户编码是否已存在")
    public Result<Boolean> checkTenantCodeExists(
            @Parameter(description = "租户编码", required = true)
            @RequestParam String tenantCode,
            @Parameter(description = "排除的租户ID（编辑时使用）")
            @RequestParam(required = false) Long excludeId) {
        log.info("检查租户编码: {}, 排除ID: {}", tenantCode, excludeId);

        try {
            boolean exists = tenantService.checkTenantCodeExists(tenantCode, excludeId);
            return Result.success("检查租户编码完成", exists);
        } catch (Exception e) {
            log.error("检查租户编码失败", e);
            return Result.error("检查租户编码失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户统计信息
     */
    @SaCheckPermission("system:tenant:stats")
    @GetMapping("/{id}/stats")
    @Operation(summary = "获取租户统计信息", description = "获取租户的详细统计信息")
    public Result<TenantStatsDTO> getTenantStats(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id) {
        log.info("获取租户统计信息: {}", id);

        try {
            TenantStatsDTO stats = tenantService.getTenantStats(id);
            return Result.success("获取租户统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取租户统计信息失败", e);
            return Result.error("获取租户统计信息失败: " + e.getMessage());
        }
    }


    /**
     * 获取租户用户列表
     */
    @SaCheckPermission("system:tenant:manage-users")
    @GetMapping("/{id}/users")
    @Operation(summary = "获取租户用户列表", description = "获取租户下的用户列表")
    public Result<List<TenantUserDTO>> getTenantUsers(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id) {
        log.info("获取租户用户列表: {}", id);

        try {
            List<TenantUserDTO> users = tenantService.getTenantUsers(id);
            return Result.success("获取租户用户列表成功", users);
        } catch (Exception e) {
            log.error("获取租户用户列表失败", e);
            return Result.error("获取租户用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取可分配给租户的用户列表
     */
    @SaCheckPermission("system:tenant:manage-users")
    @GetMapping("/{id}/available-users")
    @Operation(summary = "获取可分配用户列表", description = "获取可分配给租户的用户列表（排除已分配的用户）")
    public Result<List<TenantUserDTO>> getAvailableUsersForTenant(
            @Parameter(description = "租户ID", required = true)
            @PathVariable("id") Long id) {
        log.info("获取可分配用户列表: {}", id);

        try {
            List<TenantUserDTO> users = tenantService.getAvailableUsersForTenant(id);
            return Result.success("获取可分配用户列表成功", users);
        } catch (Exception e) {
            log.error("获取可分配用户列表失败", e);
            return Result.error("获取可分配用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户配置
     */
    @SaCheckPermission("system:tenant:manage-config")
    @GetMapping("/{id}/configs")
    @Operation(summary = "获取租户配置", description = "获取指定租户的配置列表")
    public Result<List<TenantConfigDTO>> getTenantConfigs(
            @Parameter(description = "租户ID") @PathVariable("id") Long id) {
        log.info("获取租户配置: {}", id);

        try {
            List<TenantConfigDTO> configs = tenantService.getTenantConfigs(id);
            return Result.success("获取租户配置成功", configs);
        } catch (Exception e) {
            log.error("获取租户配置失败", e);
            return Result.error("获取租户配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户配置
     */
    @SaCheckPermission("system:tenant:manage-config")
    @PutMapping("/{id}/configs")
    @Operation(summary = "更新租户配置", description = "更新指定租户的配置")
    public Result<Void> updateTenantConfigs(
            @Parameter(description = "租户ID") @PathVariable("id") Long id,
            @RequestBody List<TenantConfigDTO> configs) {
        log.info("更新租户配置: {}, 配置数量: {}", id, configs.size());

        try {
            boolean success = tenantService.updateTenantConfigs(id, configs);
            if (success) {
                return Result.success("更新租户配置成功", null);
            } else {
                return Result.error("更新租户配置失败");
            }
        } catch (Exception e) {
            log.error("更新租户配置失败", e);
            return Result.error("更新租户配置失败: " + e.getMessage());
        }
    }

    /**
     * 分配用户到租户
     */
    @SaCheckPermission("system:tenant:manage-users")
    @PostMapping("/assign-users")
    @Operation(summary = "分配用户到租户", description = "将用户分配到指定租户")
    public Result<Void> assignUsersToTenant(@RequestBody AssignUsersRequest request) {
        log.info("分配用户到租户: {}", request);

        try {
            boolean success = tenantService.assignUsersToTenant(request.getTenantId(), request.getUserIds());
            if (success) {
                return Result.success("分配用户到租户成功", null);
            } else {
                return Result.error("分配用户到租户失败");
            }
        } catch (Exception e) {
            log.error("分配用户到租户失败", e);
            return Result.error("分配用户到租户失败: " + e.getMessage());
        }
    }

    /**
     * 从租户移除用户
     */
    @SaCheckPermission("system:tenant:manage-users")
    @DeleteMapping("/remove-user")
    @Operation(summary = "从租户移除用户", description = "从租户中移除指定用户")
    public Result<Void> removeUserFromTenant(
            @Parameter(description = "租户ID", required = true)
            @RequestParam Long tenantId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId) {
        log.info("从租户移除用户: 租户ID={}, 用户ID={}", tenantId, userId);

        try {
            boolean success = tenantService.removeUserFromTenant(tenantId, userId);
            if (success) {
                return Result.success("从租户移除用户成功", null);
            } else {
                return Result.error("从租户移除用户失败");
            }
        } catch (Exception e) {
            log.error("从租户移除用户失败", e);
            return Result.error("从租户移除用户失败: " + e.getMessage());
        }
    }

    /**
     * 导出租户数据
     */
    @SaCheckPermission("system:tenant:export")
    @GetMapping("/export")
    @Operation(summary = "导出租户数据", description = "导出租户数据到Excel文件")
    public ResponseEntity<byte[]> exportTenants(
            @Parameter(description = "租户ID列表")
            @RequestParam(required = false) List<Long> tenantIds) {
        log.info("导出租户数据: {}", tenantIds);

        try {
            byte[] data = tenantService.exportTenants(tenantIds);

            return ResponseEntity.ok()
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Content-Disposition", "attachment; filename=tenants.xlsx")
                    .body(data);
        } catch (Exception e) {
            log.error("导出租户数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 用户分配请求DTO
     */
    @Setter
    @Getter
    public static class AssignUsersRequest {
        private Long tenantId;
        private List<Long> userIds;

        @Override
        public String toString() {
            return "AssignUsersRequest{" +
                    "tenantId=" + tenantId +
                    ", userIds=" + userIds +
                    '}';
        }
    }
}
