package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.FinancialStatsService;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.dto.GroupedFinancialStatsResponse;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 财务数据管理控制器
 * 提供财务统计数据查询相关的RESTful API接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "财务数据管理", description = "财务统计数据相关接口")
@RestController
@RequestMapping("/financial/stats")
@RequiredArgsConstructor
@Slf4j
public class FinancialStatsController {
    
    private final FinancialStatsService financialStatsService;
    
    @Operation(summary = "获取财务统计数据", description = "根据时间范围获取财务统计数据")
    @SaCheckPermission("financial:stats:query")
    @PostMapping("/query")
    public Result<GroupedFinancialStatsResponse> getFinancialStats(
            @Valid @RequestBody FinancialStatsRequest request) {
        
        log.info("开始查询财务统计数据，参数：{}", request);
        
        try {
            GroupedFinancialStatsResponse response = financialStatsService.getFinancialStats(request);
            log.info("财务统计数据查询成功");
            return Result.success("获取财务统计数据成功", response);
        } catch (Exception e) {
            log.error("查询财务统计数据失败", e);
            return Result.error("查询财务统计数据失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "获取今日财务数据", description = "获取今日财务统计数据")
    @SaCheckPermission("financial:stats:query")
    @GetMapping("/today")
    public Result<GroupedFinancialStatsResponse> getTodayStats(
            @Parameter(description = "是否包含主播数据", example = "true")
            @RequestParam(value = "includeAnchor", defaultValue = "true") Boolean includeAnchor) {
        
        log.info("开始查询今日财务统计数据，包含主播数据：{}", includeAnchor);
        
        try {
            GroupedFinancialStatsResponse response = financialStatsService.getTodayFinancialStats(includeAnchor);
            log.info("今日财务统计数据查询成功");
            return Result.success("获取今日财务数据成功", response);
        } catch (Exception e) {
            log.error("查询今日财务统计数据失败", e);
            return Result.error("查询今日财务数据失败：" + e.getMessage());
        }
    }
}