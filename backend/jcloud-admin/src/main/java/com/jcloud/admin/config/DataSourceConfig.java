package com.jcloud.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceBuilder;
import com.jcloud.common.config.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 多数据源配置类
 * 支持主库和从库连接，用于财务数据查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源配置
     * 用于业务数据的读写操作
     */
    @Bean("masterDataSource")
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    /**
     * 从数据源配置
     * 专用于财务数据查询，连接vimbox数据库
     */
    @Bean("slaveDataSource")
    @ConfigurationProperties("spring.datasource.druid.slave")
    public DataSource slaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    /**
     * 动态数据源配置
     * 根据上下文自动切换数据源
     */
    @Bean
    @Primary
    public DataSource dynamicDataSource() {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        
        // 设置目标数据源映射
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceContextHolder.MASTER, masterDataSource());
        targetDataSources.put(DataSourceContextHolder.SLAVE, slaveDataSource());
        
        dynamicDataSource.setTargetDataSources(targetDataSources);
        // 设置默认数据源
        dynamicDataSource.setDefaultTargetDataSource(masterDataSource());
        
        log.info("动态数据源配置完成，支持主从库切换");
        return dynamicDataSource;
    }

    /**
     * 配置通用数据源参数
     */
    private void configureDataSource(DruidDataSource dataSource, String dataSourceName) {
        try {
            // 基础连接池配置
            dataSource.setInitialSize(5);
            dataSource.setMinIdle(5);
            dataSource.setMaxActive(20);
            dataSource.setMaxWait(60000);
            
            // 连接检测配置
            dataSource.setTimeBetweenEvictionRunsMillis(60000);
            dataSource.setMinEvictableIdleTimeMillis(300000);
            dataSource.setValidationQuery("SELECT 1");
            dataSource.setTestWhileIdle(true);
            dataSource.setTestOnBorrow(false);
            dataSource.setTestOnReturn(false);
            
            // 连接泄露检测
            dataSource.setRemoveAbandoned(true);
            dataSource.setRemoveAbandonedTimeout(1800);
            dataSource.setLogAbandoned(true);
            
            // 启用监控统计，移除wall过滤器避免dbType问题
            dataSource.setFilters("stat,slf4j");
            
            // 初始化数据源
            dataSource.init();
            
            log.info("数据源 {} 配置成功", dataSourceName);
        } catch (SQLException e) {
            log.error("数据源 {} 配置失败: {}", dataSourceName, e.getMessage(), e);
            throw new RuntimeException("数据源配置失败", e);
        }
    }

    /**
     * 配置从库数据源参数，优化查询性能
     */
    private void configureSlaveDataSource(DruidDataSource dataSource) {
        try {
            // 从库连接池配置 - 优化查询性能
            dataSource.setInitialSize(3);
            dataSource.setMinIdle(3);
            dataSource.setMaxActive(15);
            dataSource.setMaxWait(30000);
            
            // 连接检测配置
            dataSource.setTimeBetweenEvictionRunsMillis(60000);
            dataSource.setMinEvictableIdleTimeMillis(300000);
            dataSource.setValidationQuery("SELECT 1");
            dataSource.setTestWhileIdle(true);
            dataSource.setTestOnBorrow(false);
            dataSource.setTestOnReturn(false);
            
            // 连接泄露检测
            dataSource.setRemoveAbandoned(true);
            dataSource.setRemoveAbandonedTimeout(1800);
            dataSource.setLogAbandoned(true);
            
            // 启用监控统计，移除wall过滤器避免dbType问题
            dataSource.setFilters("stat,slf4j");
            
            // 查询超时设置
            dataSource.setQueryTimeout(30);
            
            // 初始化数据源
            dataSource.init();
            
            log.info("从数据源配置成功，专用于财务数据查询");
        } catch (SQLException e) {
            log.error("从数据源配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("从数据源配置失败", e);
        }
    }
}