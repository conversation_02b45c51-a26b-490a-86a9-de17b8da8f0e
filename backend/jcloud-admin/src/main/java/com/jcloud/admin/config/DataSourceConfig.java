package com.jcloud.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceBuilder;
import com.jcloud.common.config.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 多数据源配置类
 * 支持主库和从库连接，用于财务数据查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源配置
     * 用于业务数据的读写操作
     * 完全使用yml配置，不进行硬编码覆盖
     */
    @Bean("masterDataSource")
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource() {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        log.info("主数据源配置完成 - URL: {}", dataSource.getUrl());
        return dataSource;
    }

    /**
     * 从数据源配置
     * 专用于财务数据查询，连接vimbox数据库
     * 完全使用yml配置，不进行硬编码覆盖
     */
    @Bean("slaveDataSource")
    @ConfigurationProperties("spring.datasource.druid.slave")
    public DataSource slaveDataSource() {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        log.info("从数据源配置完成 - URL: {}", dataSource.getUrl());
        return dataSource;
    }

    /**
     * 动态数据源配置
     * 根据上下文自动切换数据源
     */
    @Bean
    @Primary
    public DataSource dynamicDataSource() {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();

        // 设置目标数据源映射
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceContextHolder.MASTER, masterDataSource());
        targetDataSources.put(DataSourceContextHolder.SLAVE, slaveDataSource());

        dynamicDataSource.setTargetDataSources(targetDataSources);
        // 设置默认数据源
        dynamicDataSource.setDefaultTargetDataSource(masterDataSource());

        log.info("动态数据源配置完成，支持主从库切换");

        // 添加数据源状态监控
        logDataSourceStatus();
        return dynamicDataSource;
    }

    /**
     * 记录数据源状态信息并验证连接
     */
    private void logDataSourceStatus() {
        try {
            DruidDataSource master = (DruidDataSource) masterDataSource();
            DruidDataSource slave = (DruidDataSource) slaveDataSource();

            log.info("=== 数据源配置状态 ===");
            log.info("主库 - URL: {}", master.getUrl());
            log.info("主库 - 连接池配置: 初始={}, 最小={}, 最大={}",
                    master.getInitialSize(), master.getMinIdle(), master.getMaxActive());
            log.info("主库 - 超时配置: 获取连接={}ms, 空闲回收={}ms",
                master.getMaxWait(), master.getMinEvictableIdleTimeMillis());

            log.info("从库 - URL: {}", slave.getUrl());
            log.info("从库 - 连接池配置: 初始={}, 最小={}, 最大={}",
                    slave.getInitialSize(), slave.getMinIdle(), slave.getMaxActive());
            log.info("从库 - 超时配置: 获取连接={}ms, 空闲回收={}ms",
                slave.getMaxWait(), slave.getMinEvictableIdleTimeMillis());

            // 验证连接
            validateConnections(master, slave);
            log.info("======================");
        } catch (Exception e) {
            log.error("记录数据源状态失败: {}", e.getMessage());
        }
    }

    /**
     * 验证数据源连接（使用更安全的方式）
     */
    private void validateConnections(DruidDataSource master, DruidDataSource slave) {
        // 验证主库连接
        try (var connection = master.getConnection()) {
            if (connection.isValid(5)) {
                log.info("主库连接验证成功");
            } else {
                log.warn("主库连接无效");
            }
        } catch (Exception e) {
            log.error("主库连接验证失败: {}", e.getMessage());
        }

        // 验证从库连接
        try (var connection = slave.getConnection()) {
            if (connection.isValid(5)) {
                log.info("从库连接验证成功");
            } else {
                log.warn("从库连接无效");
            }
        } catch (Exception e) {
            log.error("从库连接验证失败: {}", e.getMessage());
        }
    }
}
