package com.jcloud.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * 数据源健康检查器
 * 定期检查数据库连接状态，提供连接池监控信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceHealthChecker {
    
    @Qualifier("masterDataSource")
    private final DataSource masterDataSource;
    
    @Qualifier("slaveDataSource")
    private final DataSource slaveDataSource;
    
    /**
     * 每5分钟检查一次数据源健康状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkDataSourceHealth() {
        checkDataSource("主库", masterDataSource);
        checkDataSource("从库", slaveDataSource);
    }
    
    /**
     * 检查单个数据源的健康状态
     */
    private void checkDataSource(String name, DataSource dataSource) {
        try {
            if (dataSource instanceof DruidDataSource) {
                DruidDataSource druidDataSource = (DruidDataSource) dataSource;
                
                // 记录连接池状态
                log.info("=== {} 连接池状态 ===", name);
                log.info("活跃连接数: {}/{}", druidDataSource.getActiveCount(), druidDataSource.getMaxActive());
                log.info("空闲连接数: {}", druidDataSource.getPoolingCount());
                log.info("等待连接数: {}", druidDataSource.getWaitThreadCount());
                log.info("创建连接数: {}", druidDataSource.getCreateCount());
                log.info("销毁连接数: {}", druidDataSource.getDestroyCount());
                log.info("连接错误数: {}", druidDataSource.getConnectErrorCount());
                
                // 测试连接
                testConnection(name, dataSource);
            }
        } catch (Exception e) {
            log.error("{} 健康检查失败: {}", name, e.getMessage());
        }
    }
    
    /**
     * 测试数据库连接（使用更安全的方式）
     */
    private void testConnection(String name, DataSource dataSource) {
        try (Connection connection = dataSource.getConnection()) {
            // 使用isValid方法而不是执行SQL查询，避免session为null的问题
            if (connection.isValid(5)) {
                log.info("{} 连接测试成功", name);
            } else {
                log.warn("{} 连接无效", name);
            }
        } catch (Exception e) {
            log.error("{} 连接测试失败: {}", name, e.getMessage());
        }
    }
    
    /**
     * 手动触发健康检查（用于调试）
     */
    public void manualHealthCheck() {
        log.info("手动触发数据源健康检查");
        checkDataSourceHealth();
    }
}
