package com.jcloud.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息VO（用于前端显示，包含脱敏处理）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户信息VO")
public class SysUserVO {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long id;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatar;
    
    /**
     * 邮箱（脱敏）
     */
    @Schema(description = "邮箱")
    private String email;
    
    /**
     * 手机号（脱敏）
     */
    @Schema(description = "手机号")
    private String phone;
    
    /**
     * 性别（0-未知，1-男，2-女）
     */
    @Schema(description = "性别")
    private Integer gender;
    
    /**
     * 生日
     */
    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 是否管理员（0-否，1-是）
     */
    @Schema(description = "是否管理员")
    private Integer isAdmin;
    
    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String lastLoginIp;
    
    /**
     * 密码更新时间
     */
    @Schema(description = "密码更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime passwordUpdateTime;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createBy;
    
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateBy;
    
    /**
     * 是否删除（0-否，1-是）
     */
    @Schema(description = "是否删除")
    private Integer deleted;
    
    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private Integer version;
    
    // 扩展字段
    
    /**
     * 用户主部门ID
     */
    @Schema(description = "用户主部门ID")
    private Long deptId;
    
    /**
     * 用户主部门名称
     */
    @Schema(description = "用户主部门名称")
    private String deptName;
    
    /**
     * 用户所属的所有部门ID列表
     */
    @Schema(description = "用户所属的所有部门ID列表")
    private List<Long> deptIds;
    
    /**
     * 用户所属的所有部门名称列表
     */
    @Schema(description = "用户所属的所有部门名称列表")
    private List<String> deptNames;
    
    /**
     * 用户角色ID列表
     */
    @Schema(description = "用户角色ID列表")
    private List<Long> roleIds;
    
    /**
     * 用户角色名称列表
     */
    @Schema(description = "用户角色名称列表")
    private List<String> roleNames;
    
    /**
     * 用户权限编码列表
     */
    @Schema(description = "用户权限编码列表")
    private List<String> permissions;
}
