package com.jcloud.admin.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 消费详情响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "消费详情响应")
public class ConsumeDetailResponse {
    
    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private String id;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer uid;
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;
    
    /**
     * 消费金额
     */
    @Schema(description = "消费金额")
    private java.math.BigDecimal amount;
    
    /**
     * 消费后用户余额
     */
    @Schema(description = "消费后用户余额")
    private java.math.BigDecimal balance;
    
    /**
     * 消费时间（时间戳）
     */
    @Schema(description = "消费时间")
    private Integer time;
    
    /**
     * 消费说明
     */
    @Schema(description = "消费说明")
    private String info;
    
    /**
     * 消费类型（根据info字段推断）
     */
    @Schema(description = "消费类型")
    private String consumeType;
    
    /**
     * 是否首充后的消费
     */
    @Schema(description = "是否首充后的消费")
    private Boolean isAfterFirstRecharge;
}
