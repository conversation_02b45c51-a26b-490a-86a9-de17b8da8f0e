package com.jcloud.admin.service;

import com.jcloud.common.dto.MenuCreateRequest;
import com.jcloud.common.dto.MenuQueryRequest;
import com.jcloud.common.dto.MenuUpdateRequest;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;

/**
 * 菜单服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMenuService extends BaseService<SysMenu> {
    
    /**
     * 分页查询菜单列表
     * @param queryRequest 查询条件
     * @return 菜单分页列表
     */
    PageResult<SysMenu> pageMenus(MenuQueryRequest queryRequest);
    
    /**
     * 创建菜单
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createMenu(MenuCreateRequest createRequest);
    
    /**
     * 更新菜单
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateMenu(MenuUpdateRequest updateRequest);
    
    /**
     * 根据父菜单ID查询子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<SysMenu> getMenusByParentId(Long parentId);
    
    /**
     * 根据用户ID获取菜单列表（用于前端菜单渲染）
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> getMenusByUserId(Long userId);
    
    /**
     * 根据菜单类型获取菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<SysMenu> getMenusByType(Integer menuType);
    
    /**
     * 根据权限标识查询菜单
     * 
     * @param permissionCode 权限标识
     * @return 菜单信息
     */
    SysMenu getMenuByPermissionCode(String permissionCode);
    
    /**
     * 检查菜单名称是否存在（同级菜单下）
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @param excludeMenuId 排除的菜单ID
     * @return 是否存在
     */
    boolean isMenuNameExists(String menuName, Long parentId, Long excludeMenuId);

    /**
     * 检查路由路径是否存在
     *
     * @param path 路由路径
     * @param excludeMenuId 排除的菜单ID
     * @return 是否存在
     */
    boolean isPathExists(String path, Long excludeMenuId);
    
    /**
     * 启用/禁用菜单
     * 
     * @param menuId 菜单ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateMenuStatus(Long menuId, Integer status);
    
    /**
     * 批量启用/禁用菜单
     * 
     * @param menuIds 菜单ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean updateMenuStatusBatch(List<Long> menuIds, Integer status);
    
    /**
     * 显示/隐藏菜单
     * 
     * @param menuId 菜单ID
     * @param visible 是否显示
     * @return 是否成功
     */
    boolean updateMenuVisible(Long menuId, Integer visible);
    
    /**
     * 获取所有启用的菜单
     * 
     * @return 菜单列表
     */
    List<SysMenu> getAllEnabledMenus();
    
    /**
     * 获取所有可见的菜单
     *
     * @return 菜单列表
     */
    List<SysMenu> getAllVisibleMenus();

    /**
     * 获取所有可见的导航菜单（不包含按钮类型）
     *
     * @return 导航菜单列表
     */
    List<SysMenu> getAllVisibleNavigationMenus();
    
    /**
     * 构建菜单树形结构
     * 
     * @return 菜单树
     */
    List<SysMenu> buildMenuTree();
    
    /**
     * 构建用户菜单树（用于前端渲染）
     * 
     * @param userId 用户ID
     * @return 用户菜单树
     */
    List<SysMenu> buildUserMenuTree(Long userId);
    
    /**
     * 统计子菜单数量
     * 
     * @param parentId 父菜单ID
     * @return 子菜单数量
     */
    int countChildMenus(Long parentId);
    
    /**
     * 删除菜单（检查是否有子菜单）
     * 
     * @param menuId 菜单ID
     * @return 是否成功
     */
    boolean deleteMenu(Long menuId);
    
    /**
     * 批量删除菜单
     * 
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean deleteMenusBatch(List<Long> menuIds);
    
    /**
     * 移动菜单（更改父菜单）
     * 
     * @param menuId 菜单ID
     * @param newParentId 新父菜单ID
     * @return 是否成功
     */
    boolean moveMenu(Long menuId, Long newParentId);
    
    /**
     * 初始化系统菜单
     * 
     * @return 是否成功
     */
    boolean initSystemMenus();
}
