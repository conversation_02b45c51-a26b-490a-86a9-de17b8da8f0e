package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysMenuService;
import com.jcloud.common.dto.MenuCreateRequest;
import com.jcloud.common.dto.MenuQueryRequest;
import com.jcloud.common.dto.MenuUpdateRequest;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import com.jcloud.common.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "菜单管理", description = "菜单管理相关接口")
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
public class SysMenuController {
    
    private final SysMenuService menuService;
    
    @Operation(summary = "分页查询菜单列表", description = "根据条件分页查询菜单列表")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/page")
    public Result<PageResult<SysMenu>> pageMenus(@Valid MenuQueryRequest queryRequest) {
        PageResult<SysMenu> pageResult = menuService.pageMenus(queryRequest);
        return Result.success("查询菜单列表成功", pageResult);
    }
    
    @Operation(summary = "获取菜单详情", description = "根据菜单ID获取菜单详细信息")
    @SaCheckPermission("system:menu:query")
    @GetMapping("/{id}")
    public Result<SysMenu> getMenuById(@Parameter(description = "菜单ID") @PathVariable("id") Long id) {
        SysMenu menu = menuService.getById(id);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success("获取菜单详情成功", menu);
    }
    
    @Operation(summary = "创建菜单", description = "创建新菜单")
    @SaCheckPermission("system:menu:add")
    @PostMapping
    public Result<Void> createMenu(@Valid @RequestBody MenuCreateRequest createRequest) {
        boolean success = menuService.createMenu(createRequest);
        if (success) {
            return Result.<Void>success("创建菜单成功", null);
        } else {
            return Result.error("创建菜单失败");
        }
    }
    
    @Operation(summary = "更新菜单", description = "更新菜单信息")
    @SaCheckPermission("system:menu:edit")
    @PutMapping
    public Result<Void> updateMenu(@Valid @RequestBody MenuUpdateRequest updateRequest) {
        boolean success = menuService.updateMenu(updateRequest);
        if (success) {
            return Result.<Void>success("更新菜单成功", null);
        } else {
            return Result.error("更新菜单失败");
        }
    }
    
    @Operation(summary = "删除菜单", description = "根据菜单ID删除菜单（逻辑删除）")
    @SaCheckPermission("system:menu:remove")
    @DeleteMapping("/{id}")
    public Result<Void> deleteMenu(@Parameter(description = "菜单ID") @PathVariable("id") Long id) {
        boolean success = menuService.deleteMenu(id);
        if (success) {
            return Result.<Void>success("删除菜单成功", null);
        } else {
            return Result.error("删除菜单失败");
        }
    }
    
    @Operation(summary = "批量删除菜单", description = "根据菜单ID列表批量删除菜单")
    @SaCheckPermission("system:menu:remove")
    @DeleteMapping("/batch")
    public Result<Void> deleteMenusBatch(@Parameter(description = "菜单ID列表") @RequestBody List<Long> menuIds) {
        boolean success = menuService.deleteMenusBatch(menuIds);
        if (success) {
            return Result.<Void>success("批量删除菜单成功", null);
        } else {
            return Result.error("批量删除菜单失败");
        }
    }
    
    @Operation(summary = "启用/禁用菜单", description = "更新菜单状态")
    @SaCheckPermission("system:menu:edit")
    @PutMapping("/{id}/status")
    public Result<Void> updateMenuStatus(
            @Parameter(description = "菜单ID") @PathVariable("id") Long id,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam("status") Integer status) {
        boolean success = menuService.updateMenuStatus(id, status);
        if (success) {
            return Result.<Void>success("更新菜单状态成功", null);
        } else {
            return Result.error("更新菜单状态失败");
        }
    }
    
    @Operation(summary = "批量启用/禁用菜单", description = "批量更新菜单状态")
    @SaCheckPermission("system:menu:edit")
    @PutMapping("/batch/status")
    public Result<Void> updateMenuStatusBatch(
            @Parameter(description = "菜单ID列表") @RequestParam List<Long> menuIds,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam Integer status) {
        boolean success = menuService.updateMenuStatusBatch(menuIds, status);
        if (success) {
            return Result.<Void>success("批量更新菜单状态成功", null);
        } else {
            return Result.error("批量更新菜单状态失败");
        }
    }
    
    @Operation(summary = "显示/隐藏菜单", description = "更新菜单显示状态")
    @SaCheckPermission("system:menu:edit")
    @PutMapping("/{id}/visible")
    public Result<Void> updateMenuVisible(
            @Parameter(description = "菜单ID") @PathVariable("id") Long id,
            @Parameter(description = "是否显示（0-隐藏，1-显示）") @RequestParam("visible") Integer visible) {
        boolean success = menuService.updateMenuVisible(id, visible);
        if (success) {
            return Result.<Void>success("更新菜单显示状态成功", null);
        } else {
            return Result.error("更新菜单显示状态失败");
        }
    }
    
    @Operation(summary = "获取菜单树形结构", description = "获取完整的菜单树形结构")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/tree")
    public Result<List<SysMenu>> getMenuTree() {
        List<SysMenu> menuTree = menuService.buildMenuTree();
        return Result.success("获取菜单树成功", menuTree);
    }
    
    @Operation(summary = "获取用户菜单树", description = "获取当前用户的菜单树（用于前端渲染）")
    @GetMapping("/user-tree")
    public Result<List<SysMenu>> getUserMenuTree() {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            return Result.error("用户未登录");
        }
        List<SysMenu> userMenuTree = menuService.buildUserMenuTree(userId);
        return Result.success("获取用户菜单树成功", userMenuTree);
    }
    
    @Operation(summary = "根据父菜单ID获取子菜单", description = "获取指定父菜单下的子菜单列表")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/children/{parentId}")
    public Result<List<SysMenu>> getMenusByParentId(@Parameter(description = "父菜单ID") @PathVariable("parentId") Long parentId) {
        List<SysMenu> menus = menuService.getMenusByParentId(parentId);
        return Result.success("获取子菜单列表成功", menus);
    }
    
    @Operation(summary = "根据类型获取菜单列表", description = "根据菜单类型获取菜单列表")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/type/{type}")
    public Result<List<SysMenu>> getMenusByType(@Parameter(description = "菜单类型") @PathVariable Integer type) {
        List<SysMenu> menus = menuService.getMenusByType(type);
        return Result.success("获取菜单列表成功", menus);
    }
    
    @Operation(summary = "获取所有启用的菜单", description = "获取所有启用状态的菜单列表")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/enabled")
    public Result<List<SysMenu>> getAllEnabledMenus() {
        List<SysMenu> menus = menuService.getAllEnabledMenus();
        return Result.success("获取启用菜单列表成功", menus);
    }
    
    @Operation(summary = "获取所有可见的菜单", description = "获取所有可见状态的菜单列表")
    @SaCheckPermission("system:menu:list")
    @GetMapping("/visible")
    public Result<List<SysMenu>> getAllVisibleMenus() {
        List<SysMenu> menus = menuService.getAllVisibleMenus();
        return Result.success("获取可见菜单列表成功", menus);
    }
    
    @Operation(summary = "移动菜单", description = "将菜单移动到新的父菜单下")
    @SaCheckPermission("system:menu:edit")
    @PutMapping("/{id}/move")
    public Result<Void> moveMenu(
            @Parameter(description = "菜单ID") @PathVariable("id") Long id,
            @Parameter(description = "新父菜单ID") @RequestParam("newParentId") Long newParentId) {
        boolean success = menuService.moveMenu(id, newParentId);
        if (success) {
            return Result.<Void>success("移动菜单成功", null);
        } else {
            return Result.error("移动菜单失败");
        }
    }
    
    @Operation(summary = "统计子菜单数量", description = "统计指定菜单下的子菜单数量")
    @SaCheckPermission("system:menu:query")
    @GetMapping("/{id}/child-count")
    public Result<Integer> countChildMenus(@Parameter(description = "菜单ID") @PathVariable("id") Long id) {
        int count = menuService.countChildMenus(id);
        return Result.success("获取子菜单数量成功", count);
    }
    
    @Operation(summary = "检查菜单名称是否存在", description = "检查同级菜单下菜单名称是否已存在")
    @SaCheckPermission("system:menu:query")
    @GetMapping("/check-name")
    public Result<Boolean> checkMenuName(
            @Parameter(description = "菜单名称") @RequestParam String menuName,
            @Parameter(description = "父菜单ID") @RequestParam Long parentId,
            @Parameter(description = "排除的菜单ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = menuService.isMenuNameExists(menuName, parentId, excludeId);
        return Result.success("检查菜单名称完成", exists);
    }
    
    @Operation(summary = "检查路由路径是否存在", description = "检查路由路径是否已存在")
    @SaCheckPermission("system:menu:query")
    @GetMapping("/check-path")
    public Result<Boolean> checkPath(
            @Parameter(description = "路由路径") @RequestParam String path,
            @Parameter(description = "排除的菜单ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = menuService.isPathExists(path, excludeId);
        return Result.success("检查路由路径完成", exists);
    }
    
    @Operation(summary = "初始化系统菜单", description = "初始化系统默认菜单")
    @SaCheckPermission("system:menu:add")
    @PostMapping("/init")
    public Result<Void> initSystemMenus() {
        boolean success = menuService.initSystemMenus();
        if (success) {
            return Result.<Void>success("初始化系统菜单成功", null);
        } else {
            return Result.error("初始化系统菜单失败");
        }
    }
}
