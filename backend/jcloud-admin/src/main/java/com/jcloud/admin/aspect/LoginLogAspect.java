package com.jcloud.admin.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import com.jcloud.admin.service.SysLoginLogService;
import com.jcloud.common.annotation.LoginLog;
import com.jcloud.common.entity.SysLoginLog;
import com.jcloud.common.util.AddressUtils;
import com.jcloud.common.util.ServletUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 登录日志记录处理切面
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class LoginLogAspect {
    
    private final SysLoginLogService loginLogService;
    
    /**
     * 处理完请求后执行（登录成功）
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(loginLog)", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, LoginLog loginLog, Object result) {
        if (loginLog.recordSuccess()) {
            handleLoginLog(joinPoint, loginLog, null, "登录成功");
        }
    }
    
    /**
     * 拦截异常操作（登录失败）
     * 
     * @param joinPoint 切点
     * @param e 异常
     */
    @AfterThrowing(value = "@annotation(loginLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, LoginLog loginLog, Exception e) {
        if (loginLog.recordFailure()) {
            handleLoginLog(joinPoint, loginLog, e, "登录失败");
        }
    }
    
    protected void handleLoginLog(final JoinPoint joinPoint, LoginLog loginLog, final Exception e, String msg) {
        try {
            // 获取用户名
            String userName = getUserNameFromArgs(joinPoint.getArgs());
            
            // *========数据库日志=========*
            SysLoginLog sysLoginLog = new SysLoginLog();
            sysLoginLog.setUserName(userName);
            
            // 请求的地址
            String ip = ServletUtils.getClientIP();
            sysLoginLog.setIpaddr(ip);
            sysLoginLog.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
            
            // 获取客户端信息
            UserAgent userAgent = ServletUtils.getUserAgent();
            sysLoginLog.setBrowser(userAgent.getBrowser().getName());
            sysLoginLog.setOs(userAgent.getOs().getName());
            
            // 设置登录状态
            if (e != null) {
                sysLoginLog.setStatus(1);
                sysLoginLog.setMsg(StrUtil.sub(e.getMessage(), 0, 255));
            } else {
                sysLoginLog.setStatus(0);
                sysLoginLog.setMsg(msg);
            }
            
            sysLoginLog.setLoginTime(LocalDateTime.now());
            
            // 保存数据库
            saveLoginLog(sysLoginLog);
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("登录日志记录异常:{}", exp.getMessage(), exp);
        }
    }
    
    /**
     * 从方法参数中获取用户名
     */
    private String getUserNameFromArgs(Object[] args) {
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg != null) {
                    // 如果是字符串类型，可能是用户名
                    if (arg instanceof String) {
                        return (String) arg;
                    }
                    // 如果是对象，尝试获取username字段
                    try {
                        java.lang.reflect.Field usernameField = arg.getClass().getDeclaredField("username");
                        usernameField.setAccessible(true);
                        Object username = usernameField.get(arg);
                        if (username instanceof String) {
                            return (String) username;
                        }
                    } catch (Exception e) {
                        // 忽略反射异常，继续尝试其他字段
                    }
                    
                    // 尝试获取userName字段
                    try {
                        java.lang.reflect.Field userNameField = arg.getClass().getDeclaredField("userName");
                        userNameField.setAccessible(true);
                        Object userName = userNameField.get(arg);
                        if (userName instanceof String) {
                            return (String) userName;
                        }
                    } catch (Exception e) {
                        // 忽略反射异常
                    }
                }
            }
        }
        return "未知用户";
    }
    
    /**
     * 异步保存登录日志
     */
    @Async
    public void saveLoginLog(SysLoginLog loginLog) {
        loginLogService.recordLoginLog(loginLog);
    }
}
