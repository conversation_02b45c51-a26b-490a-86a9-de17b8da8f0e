package com.jcloud.admin.service;

import com.jcloud.common.dto.DeptCreateRequest;
import com.jcloud.common.dto.DeptQueryRequest;
import com.jcloud.common.dto.DeptUpdateRequest;
import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;

/**
 * 部门服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysDeptService extends BaseService<SysDept> {
    
    /**
     * 分页查询部门列表
     * @param queryRequest 查询条件
     * @return 部门分页列表
     */
    PageResult<SysDept> pageDepts(DeptQueryRequest queryRequest);
    
    /**
     * 创建部门
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createDept(DeptCreateRequest createRequest);
    
    /**
     * 更新部门
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateDept(DeptUpdateRequest updateRequest);
    
    /**
     * 根据部门编码查询部门
     * 
     * @param deptCode 部门编码
     * @return 部门信息
     */
    SysDept getDeptByCode(String deptCode);
    
    /**
     * 根据父部门ID查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<SysDept> getDeptsByParentId(Long parentId);
    
    /**
     * 根据用户ID查询部门列表
     * 
     * @param userId 用户ID
     * @return 部门列表
     */
    List<SysDept> getDeptsByUserId(Long userId);
    
    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<SysUser> getUsersByDeptId(Long deptId);
    
    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @param excludeDeptId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptCodeExists(String deptCode, Long excludeDeptId);
    
    /**
     * 检查部门名称是否存在（同级部门下）
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeDeptId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptNameExists(String deptName, Long parentId, Long excludeDeptId);
    
    /**
     * 启用/禁用部门
     * 
     * @param deptId 部门ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateDeptStatus(Long deptId, Integer status);
    
    /**
     * 批量启用/禁用部门
     * 
     * @param deptIds 部门ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean updateDeptStatusBatch(List<Long> deptIds, Integer status);
    
    /**
     * 获取所有启用的部门
     * 
     * @return 部门列表
     */
    List<SysDept> getAllEnabledDepts();
    
    /**
     * 构建部门树形结构
     * 
     * @return 部门树
     */
    List<SysDept> buildDeptTree();
    
    /**
     * 统计子部门数量
     * 
     * @param parentId 父部门ID
     * @return 子部门数量
     */
    int countChildDepts(Long parentId);
    
    /**
     * 统计部门下的用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    int countUsersByDeptId(Long deptId);
    
    /**
     * 删除部门（检查是否有子部门和用户）
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean deleteDept(Long deptId);
    
    /**
     * 批量删除部门
     * 
     * @param deptIds 部门ID列表
     * @return 是否成功
     */
    boolean deleteDeptsBatch(List<Long> deptIds);
    
    /**
     * 移动部门（更改父部门）
     * 
     * @param deptId 部门ID
     * @param newParentId 新父部门ID
     * @return 是否成功
     */
    boolean moveDept(Long deptId, Long newParentId);
    
    /**
     * 分配用户到部门
     * 
     * @param userId 用户ID
     * @param deptIds 部门ID列表
     * @param primaryDeptId 主部门ID
     * @return 是否成功
     */
    boolean assignUserToDepts(Long userId, List<Long> deptIds, Long primaryDeptId);
    
    /**
     * 从部门中移除用户
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean removeUserFromDept(Long userId, Long deptId);
    
    /**
     * 设置用户主部门
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean setUserPrimaryDept(Long userId, Long deptId);
    
    /**
     * 获取用户的主部门ID
     * 
     * @param userId 用户ID
     * @return 主部门ID
     */
    Long getUserPrimaryDeptId(Long userId);
    
    /**
     * 初始化系统部门
     * 
     * @return 是否成功
     */
    boolean initSystemDepts();
}
