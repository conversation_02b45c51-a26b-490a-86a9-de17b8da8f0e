package com.jcloud.admin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据源健康检查组件
 * 监控主库和从库的连接状态
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class DataSourceHealthIndicator {

    private final DataSource masterDataSource;
    private final DataSource slaveDataSource;

    public DataSourceHealthIndicator(DataSource masterDataSource, 
                                   @Qualifier("slaveDataSource") DataSource slaveDataSource) {
        this.masterDataSource = masterDataSource;
        this.slaveDataSource = slaveDataSource;
    }

    /**
     * 获取数据源健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        // 检查主数据源
        Map<String, Object> masterStatus = checkDataSource(masterDataSource, "master");
        healthStatus.put("master", masterStatus);

        // 检查从数据源
        Map<String, Object> slaveStatus = checkDataSource(slaveDataSource, "slave");
        healthStatus.put("slave", slaveStatus);

        // 整体状态
        boolean allHealthy = (Boolean) masterStatus.get("healthy") && (Boolean) slaveStatus.get("healthy");
        healthStatus.put("overall", allHealthy ? "UP" : "DOWN");
        
        return healthStatus;
    }

    /**
     * 检查单个数据源的健康状态
     */
    private Map<String, Object> checkDataSource(DataSource dataSource, String dataSourceName) {
        Map<String, Object> status = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单查询验证连接
            try (PreparedStatement statement = connection.prepareStatement("SELECT 1");
                 ResultSet resultSet = statement.executeQuery()) {
                
                if (resultSet.next() && resultSet.getInt(1) == 1) {
                    log.debug("数据源 {} 健康检查通过", dataSourceName);
                    status.put("healthy", true);
                    status.put("status", "Connected");
                    status.put("database", "Available");
                    status.put("validationQuery", "SELECT 1");
                } else {
                    log.warn("数据源 {} 健康检查失败：查询结果异常", dataSourceName);
                    status.put("healthy", false);
                    status.put("status", "Query failed");
                    status.put("error", "Validation query returned unexpected result");
                }
            }
        } catch (SQLException e) {
            log.error("数据源 {} 健康检查失败: {}", dataSourceName, e.getMessage());
            status.put("healthy", false);
            status.put("status", "Connection failed");
            status.put("error", e.getMessage());
            status.put("errorCode", e.getErrorCode());
        } catch (Exception e) {
            log.error("数据源 {} 健康检查异常: {}", dataSourceName, e.getMessage(), e);
            status.put("healthy", false);
            status.put("status", "Unknown error");
            status.put("error", e.getMessage());
        }
        
        return status;
    }

    /**
     * 检查主数据源是否健康
     */
    public boolean isMasterHealthy() {
        Map<String, Object> status = checkDataSource(masterDataSource, "master");
        return (Boolean) status.get("healthy");
    }

    /**
     * 检查从数据源是否健康
     */
    public boolean isSlaveHealthy() {
        Map<String, Object> status = checkDataSource(slaveDataSource, "slave");
        return (Boolean) status.get("healthy");
    }
}