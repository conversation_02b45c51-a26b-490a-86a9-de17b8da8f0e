package com.jcloud.admin.controller;

import com.jcloud.admin.config.DataSourceHealthIndicator;
import com.jcloud.admin.config.DataSourceMonitor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 数据源监控控制器
 * 提供数据源健康检查和状态查询接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "数据源监控", description = "数据源健康检查和状态监控")
@RestController
@RequestMapping("/datasource")
@RequiredArgsConstructor
@Slf4j
public class DataSourceController {

    private final DataSourceHealthIndicator healthIndicator;
    private final DataSourceMonitor dataSourceMonitor;

    @Operation(summary = "获取数据源健康状态", description = "检查主库和从库的连接状态")
    @GetMapping("/health")
    public Map<String, Object> getHealthStatus() {
        log.info("检查数据源健康状态");
        return healthIndicator.getHealthStatus();
    }

    @Operation(summary = "获取数据源连接统计", description = "获取数据源连接池统计信息")
    @GetMapping("/stats")
    public String getDataSourceStats() {
        log.info("获取数据源连接统计");
        return dataSourceMonitor.getDataSourceStats();
    }

    @Operation(summary = "检查主数据源状态", description = "检查主数据源是否健康")
    @GetMapping("/master/health")
    public boolean checkMasterHealth() {
        boolean healthy = healthIndicator.isMasterHealthy();
        log.info("主数据源健康状态: {}", healthy ? "健康" : "异常");
        return healthy;
    }

    @Operation(summary = "检查从数据源状态", description = "检查从数据源是否健康")
    @GetMapping("/slave/health")
    public boolean checkSlaveHealth() {
        boolean healthy = healthIndicator.isSlaveHealthy();
        log.info("从数据源健康状态: {}", healthy ? "健康" : "异常");
        return healthy;
    }
}