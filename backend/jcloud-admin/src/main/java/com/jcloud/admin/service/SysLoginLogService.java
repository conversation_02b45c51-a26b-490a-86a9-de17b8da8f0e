package com.jcloud.admin.service;

import com.jcloud.common.dto.LoginLogQueryRequest;
import com.jcloud.common.entity.SysLoginLog;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 登录日志服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysLoginLogService extends BaseService<SysLoginLog> {
    
    /**
     * 分页查询登录日志列表
     * @param queryRequest 查询条件
     * @return 登录日志分页列表
     */
    PageResult<SysLoginLog> pageLoginLogs(LoginLogQueryRequest queryRequest);
    
    /**
     * 记录登录日志 - 异步处理
     * @param loginLog 登录日志
     */
    void recordLoginLog(SysLoginLog loginLog);

    /**
     * 同步记录登录日志 - 返回结果
     * @param loginLog 登录日志
     * @return 是否成功
     */
    boolean recordLoginLogSync(SysLoginLog loginLog);
    
    /**
     * 根据用户名查询登录日志
     * 
     * @param userName 用户名
     * @return 登录日志列表
     */
    List<SysLoginLog> getLoginLogsByUserName(String userName);
    
    /**
     * 根据IP地址查询登录日志
     * 
     * @param ipaddr IP地址
     * @return 登录日志列表
     */
    List<SysLoginLog> getLoginLogsByIpaddr(String ipaddr);
    
    /**
     * 根据登录状态查询登录日志
     * 
     * @param status 登录状态
     * @return 登录日志列表
     */
    List<SysLoginLog> getLoginLogsByStatus(Integer status);
    
    /**
     * 获取最近的登录日志
     * 
     * @param limit 限制数量
     * @return 登录日志列表
     */
    List<SysLoginLog> getRecentLoginLogs(int limit);
    
    /**
     * 统计登录成功和失败的数量
     * 
     * @return 统计结果
     */
    List<Map<String, Object>> countByStatus();
    
    /**
     * 统计今日登录数量
     * 
     * @return 登录数量
     */
    int countTodayLogins();
    
    /**
     * 统计登录失败数量
     * 
     * @return 登录失败数量
     */
    int countFailedLogins();
    
    /**
     * 获取用户最后登录信息
     * 
     * @param userName 用户名
     * @return 最后登录信息
     */
    SysLoginLog getLastLoginByUserName(String userName);
    
    /**
     * 清理指定天数之前的登录日志
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanOldLoginLogs(int days);
    
    /**
     * 批量删除登录日志
     * 
     * @param logIds 日志ID列表
     * @return 是否成功
     */
    boolean deleteLoginLogsBatch(List<Long> logIds);
    
    /**
     * 清空所有登录日志
     * 
     * @return 是否成功
     */
    boolean clearAllLoginLogs();
}
