package com.jcloud.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.jcloud.admin.exception.DataSourceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 数据源监控组件
 * 定期检查数据源状态，提供连接管理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class DataSourceMonitor {

    private final DataSource masterDataSource;
    private final DataSource slaveDataSource;

    public DataSourceMonitor(DataSource masterDataSource, 
                           @Qualifier("slaveDataSource") DataSource slaveDataSource) {
        this.masterDataSource = masterDataSource;
        this.slaveDataSource = slaveDataSource;
    }

    private final AtomicBoolean masterHealthy = new AtomicBoolean(true);
    private final AtomicBoolean slaveHealthy = new AtomicBoolean(true);

    /**
     * 定期检查数据源健康状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkDataSourceHealth() {
        log.debug("开始数据源健康检查");
        
        // 检查主数据源
        boolean masterStatus = checkSingleDataSource(masterDataSource, "master");
        masterHealthy.set(masterStatus);
        
        // 检查从数据源
        boolean slaveStatus = checkSingleDataSource(slaveDataSource, "slave");
        slaveHealthy.set(slaveStatus);
        
        log.debug("数据源健康检查完成 - 主库: {}, 从库: {}", 
                masterStatus ? "健康" : "异常", 
                slaveStatus ? "健康" : "异常");
    }

    /**
     * 检查单个数据源状态
     */
    private boolean checkSingleDataSource(DataSource dataSource, String name) {
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单查询
            connection.prepareStatement("SELECT 1").executeQuery();
            
            // 如果是Druid数据源，记录连接池状态
            if (dataSource instanceof DruidDataSource druidDataSource) {
                logDruidStatus(druidDataSource, name);
            }
            
            return true;
        } catch (SQLException e) {
            log.error("数据源 {} 健康检查失败: {}", name, e.getMessage());
            return false;
        }
    }

    /**
     * 记录Druid连接池状态
     */
    private void logDruidStatus(DruidDataSource dataSource, String name) {
        log.debug("数据源 {} 连接池状态 - 活跃连接: {}, 空闲连接: {}, 总连接: {}", 
                name,
                dataSource.getActiveCount(),
                dataSource.getPoolingCount(),
                dataSource.getActiveCount() + dataSource.getPoolingCount());
    }

    /**
     * 获取主数据源健康状态
     */
    public boolean isMasterHealthy() {
        return masterHealthy.get();
    }

    /**
     * 获取从数据源健康状态
     */
    public boolean isSlaveHealthy() {
        return slaveHealthy.get();
    }

    /**
     * 验证数据源可用性
     * 在执行重要操作前调用
     */
    public void validateDataSourceAvailability(String dataSourceName) {
        boolean isHealthy = switch (dataSourceName.toLowerCase()) {
            case "master" -> isMasterHealthy();
            case "slave" -> isSlaveHealthy();
            default -> throw new DataSourceException(dataSourceName, "DS_UNKNOWN", "未知的数据源名称");
        };

        if (!isHealthy) {
            throw new DataSourceException(dataSourceName, "DS_UNAVAILABLE", 
                    String.format("数据源 %s 当前不可用", dataSourceName));
        }
    }

    /**
     * 获取数据源连接统计信息
     */
    public String getDataSourceStats() {
        StringBuilder stats = new StringBuilder();
        
        if (masterDataSource instanceof DruidDataSource masterDruid) {
            stats.append(String.format("主库 - 活跃: %d, 空闲: %d, 总计: %d\n",
                    masterDruid.getActiveCount(),
                    masterDruid.getPoolingCount(),
                    masterDruid.getActiveCount() + masterDruid.getPoolingCount()));
        }
        
        if (slaveDataSource instanceof DruidDataSource slaveDruid) {
            stats.append(String.format("从库 - 活跃: %d, 空闲: %d, 总计: %d",
                    slaveDruid.getActiveCount(),
                    slaveDruid.getPoolingCount(),
                    slaveDruid.getActiveCount() + slaveDruid.getPoolingCount()));
        }
        
        return stats.toString();
    }
}