package com.jcloud.admin.dto.request;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下级用户查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "下级用户查询请求")
public class SubUserQueryRequest extends PageQuery {
    
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "用户001")
    private String nickname;
    
    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "user001")
    private String username;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
    
    /**
     * 账号状态（1-正常，2-禁用）
     */
    @Schema(description = "账号状态", example = "1")
    private Integer state;
    
    /**
     * 是否实名认证（0-未实名，1-已实名）
     */
    @Schema(description = "是否实名认证", example = "1")
    private Integer isAuth;
    
    /**
     * 注册开始时间（时间戳）
     */
    @Schema(description = "注册开始时间", example = "1640995200")
    private Integer registerStartTime;
    
    /**
     * 注册结束时间（时间戳）
     */
    @Schema(description = "注册结束时间", example = "1672531199")
    private Integer registerEndTime;
    
    /**
     * 是否已首充
     */
    @Schema(description = "是否已首充", example = "true")
    private Boolean hasFirstRecharge;
    
    /**
     * 最小充值金额
     */
    @Schema(description = "最小充值金额", example = "100.00")
    private java.math.BigDecimal minRechargeAmount;
    
    /**
     * 最大充值金额
     */
    @Schema(description = "最大充值金额", example = "10000.00")
    private java.math.BigDecimal maxRechargeAmount;
}
