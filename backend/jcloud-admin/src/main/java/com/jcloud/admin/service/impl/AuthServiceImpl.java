package com.jcloud.admin.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.AuthService;
import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.admin.service.SysDeptService;
import com.jcloud.admin.service.SysRoleService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.config.CaptchaProperties;
import com.jcloud.common.dto.LoginRequest;
import com.jcloud.common.dto.LoginResponse;
import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysRole;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.util.CaptchaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import com.jcloud.common.util.TimeUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {
    
    private final SysUserService userService;
    private final SysRoleService roleService;
    private final SysDeptService deptService;
    private final DeptPermissionService deptPermissionService;
    private final BCryptPasswordEncoder passwordEncoder;
    private final CaptchaUtils captchaUtils;
    private final CaptchaProperties captchaProperties;
    
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();

        // 参数验证
        if (StrUtil.isBlank(username) || StrUtil.isBlank(password)) {
            throw new BusinessException("用户名或密码不能为空");
        }

        // 验证码验证
        if (captchaProperties.getEnabled()) {
            if (StrUtil.isBlank(loginRequest.getCaptchaKey()) || StrUtil.isBlank(loginRequest.getCaptcha())) {
                log.warn("验证码验证失败: username={}, 原因=验证码不能为空", username);
                throw new BusinessException("验证码不能为空");
            }

            try {
                boolean isValid = captchaUtils.verifyCaptcha(loginRequest.getCaptchaKey(), loginRequest.getCaptcha());
                if (!isValid) {
                    log.warn("验证码验证失败: username={}, 原因=验证码错误", username);
                    throw new BusinessException("验证码错误");
                }
                log.debug("验证码验证成功: username={}", username);
            } catch (BusinessException e) {
                // 重新抛出业务异常
                throw e;
            } catch (Exception e) {
                log.warn("验证码验证失败: username={}, error={}", username, e.getMessage());
                throw new BusinessException("验证码验证失败: " + e.getMessage());
            }
        }
        
        // 查询用户
        long userQueryStart = System.currentTimeMillis();
        SysUser user = userService.getUserByUsername(username);
        long userQueryTime = System.currentTimeMillis() - userQueryStart;
        log.debug("用户查询耗时: {}ms", userQueryTime);

        if (user == null) {
            log.warn("用户不存在: username={}", username);
            throw new BusinessException("用户名或密码错误");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            log.warn("用户已被禁用: username={}, userId={}", username, user.getId());
            throw new BusinessException("用户已被禁用");
        }

        // 验证密码 - 增强缓存密码验证逻辑
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            log.error("用户密码字段为空，可能是缓存序列化问题: username={}, userId={}", username, user.getId());
            // 清理缓存并重新查询
            userService.clearUserCacheByUsername(username);
            user = userService.getUserByUsernameFromDatabase(username);
            if (user == null || user.getPassword() == null) {
                log.error("重新查询后用户密码仍为空: username={}", username);
                throw new BusinessException("系统错误，请重试");
            }
        }

        if (!passwordEncoder.matches(password, user.getPassword())) {
            log.warn("密码验证失败: username={}, userId={}", username, user.getId());
            throw new BusinessException("用户名或密码错误");
        }

        log.debug("密码验证成功: username={}, userId={}", username, user.getId());
        
        // 执行登录
        StpUtil.login(user.getId());
        
        // 保存用户信息到session
        StpUtil.getSession().set("username", user.getUsername());
        StpUtil.getSession().set("nickname", user.getNickname());
        StpUtil.getSession().set("tenantId", user.getTenantId());
        StpUtil.getSession().set("isAdmin", user.getIsAdmin());

        // 获取用户数据权限范围（基于用户角色的最高权限）
        String userDataScope = getUserDataScope(user.getId());
        StpUtil.getSession().set("dataScope", userDataScope);

        // 获取用户主部门ID（使用新的部门权限服务）
        Long userDeptId = deptPermissionService.getUserPrimaryDeptId(user.getId());
        if (userDeptId != null) {
            StpUtil.getSession().set("deptId", userDeptId);
            log.debug("设置用户主部门到Session: userId={}, deptId={}", user.getId(), userDeptId);
        } else {
            log.debug("用户没有主部门: userId={}", user.getId());
        }

        // 获取用户权限和角色 - 优化：传递用户对象避免重复查询
        long permissionStart = System.currentTimeMillis();
        Set<String> permissions = userService.getUserPermissions(user);
        long permissionTime = System.currentTimeMillis() - permissionStart;
        log.debug("权限查询耗时: {}ms", permissionTime);

        long roleStart = System.currentTimeMillis();
        Set<String> roles = userService.getUserRoles(user);
        long roleTime = System.currentTimeMillis() - roleStart;
        log.debug("角色查询耗时: {}ms", roleTime);

        // 将权限和角色设置到会话中（sa-token会自动使用）
        StpUtil.getSession().set("permissions", permissions);
        StpUtil.getSession().set("roles", roles);

        // 更新最后登录时间
        user.setLastLoginTime(TimeUtil.now());
        userService.updateById(user);
        
        // 构建响应
        LoginResponse.LoginResponseBuilder builder = LoginResponse.builder()
                .accessToken(StpUtil.getTokenValue())
                .tokenType("Bearer")
                .expiresIn(StpUtil.getTokenTimeout());
        
        // 构建用户信息
        LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatar(user.getAvatar())
                .tenantId(user.getTenantId())
                .build();
        
        builder.userInfo(userInfo);
        
        // 设置权限和角色到响应中
        builder.permissions(new ArrayList<>(permissions))
               .roles(new ArrayList<>(roles));
        
        log.info("用户登录成功: username={}, userId={}", username, user.getId());
        
        return builder.build();
    }

    /**
     * 获取用户数据权限范围（基于用户角色的最高权限）
     */
    private String getUserDataScope(Long userId) {
        try {
            List<SysRole> userRoles = roleService.getRolesByUserId(userId);

            // 权限范围优先级：ALL > DEPT_AND_SUB > DEPT > CUSTOM > SELF
            String[] scopePriority = {"ALL", "DEPT_AND_SUB", "DEPT", "CUSTOM", "SELF"};

            for (String scope : scopePriority) {
                for (SysRole role : userRoles) {
                    if (scope.equals(role.getDataScope())) {
                        log.debug("用户数据权限范围: userId={}, dataScope={}, roleName={}",
                                userId, scope, role.getRoleName());
                        return scope;
                    }
                }
            }

            // 默认返回最严格的权限
            return "SELF";
        } catch (Exception e) {
            log.error("获取用户数据权限范围失败: userId={}", userId, e);
            return "SELF";
        }
    }

    // 获取用户主部门ID的逻辑已移至DeptPermissionService

    @Override
    public void logout() {
        if (StpUtil.isLogin()) {
            Long userId = StpUtil.getLoginIdAsLong();
            StpUtil.logout();
            log.info("用户登出成功: userId={}", userId);
        }
    }
    
    @Override
    public Map<String, Object> getCurrentUserInfo() {
        if (!StpUtil.isLogin()) {
            throw new BusinessException("用户未登录");
        }
        
        Long userId = StpUtil.getLoginIdAsLong();
        SysUser user = userService.getById(userId);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("email", user.getEmail());
        userInfo.put("phone", user.getPhone());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("tenantId", user.getTenantId());
        userInfo.put("status", user.getStatus());
        userInfo.put("lastLoginTime", user.getLastLoginTime());
        
        // 获取用户权限和角色 - 修复硬编码问题
        Set<String> permissions = userService.getUserPermissions(user.getId());
        Set<String> roles = userService.getUserRoles(user.getId());
        userInfo.put("permissions", new ArrayList<>(permissions));
        userInfo.put("roles", new ArrayList<>(roles));
        
        return userInfo;
    }
}
