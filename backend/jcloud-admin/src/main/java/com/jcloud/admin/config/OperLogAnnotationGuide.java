package com.jcloud.admin.config;

/**
 * 操作日志注解配置指南
 * 
 * 此文件展示了如何为所有Controller方法添加@OperLog注解
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class OperLogAnnotationGuide {
    
    /*
     * ==================== 角色管理Controller ====================
     * 
     * @OperLog(title = "角色管理", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * @OperLog(title = "角色管理", businessType = BusinessType.INSERT.getCode())    // 新增操作
     * @OperLog(title = "角色管理", businessType = BusinessType.UPDATE.getCode())    // 修改操作
     * @OperLog(title = "角色管理", businessType = BusinessType.DELETE.getCode())    // 删除操作
     * @OperLog(title = "角色管理", businessType = BusinessType.GRANT.getCode())     // 授权操作
     * @OperLog(title = "角色管理", businessType = BusinessType.EXPORT.getCode())    // 导出操作
     * 
     * ==================== 权限管理Controller ====================
     * 
     * @OperLog(title = "权限管理", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * @OperLog(title = "权限管理", businessType = BusinessType.INSERT.getCode())    // 新增操作
     * @OperLog(title = "权限管理", businessType = BusinessType.UPDATE.getCode())    // 修改操作
     * @OperLog(title = "权限管理", businessType = BusinessType.DELETE.getCode())    // 删除操作
     * 
     * ==================== 菜单管理Controller ====================
     * 
     * @OperLog(title = "菜单管理", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * @OperLog(title = "菜单管理", businessType = BusinessType.INSERT.getCode())    // 新增操作
     * @OperLog(title = "菜单管理", businessType = BusinessType.UPDATE.getCode())    // 修改操作
     * @OperLog(title = "菜单管理", businessType = BusinessType.DELETE.getCode())    // 删除操作
     * 
     * ==================== 部门管理Controller ====================
     * 
     * @OperLog(title = "部门管理", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * @OperLog(title = "部门管理", businessType = BusinessType.INSERT.getCode())    // 新增操作
     * @OperLog(title = "部门管理", businessType = BusinessType.UPDATE.getCode())    // 修改操作
     * @OperLog(title = "部门管理", businessType = BusinessType.DELETE.getCode())    // 删除操作
     * 
     * ==================== 用户管理Controller ====================
     * 
     * @OperLog(title = "用户管理", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * @OperLog(title = "用户管理", businessType = BusinessType.INSERT.getCode())    // 新增操作
     * @OperLog(title = "用户管理", businessType = BusinessType.UPDATE.getCode())    // 修改操作
     * @OperLog(title = "用户管理", businessType = BusinessType.DELETE.getCode())    // 删除操作
     * @OperLog(title = "用户管理", businessType = BusinessType.GRANT.getCode())     // 分配角色操作
     * @OperLog(title = "用户管理", businessType = BusinessType.EXPORT.getCode())    // 导出操作
     * @OperLog(title = "用户管理", businessType = BusinessType.IMPORT.getCode())    // 导入操作
     * 
     * ==================== 系统监控Controller ====================
     * 
     * @OperLog(title = "操作日志", businessType = BusinessType.DELETE.getCode())    // 删除日志
     * @OperLog(title = "操作日志", businessType = BusinessType.CLEAN.getCode())     // 清空日志
     * @OperLog(title = "登录日志", businessType = BusinessType.DELETE.getCode())    // 删除日志
     * @OperLog(title = "登录日志", businessType = BusinessType.CLEAN.getCode())     // 清空日志
     * @OperLog(title = "系统监控", businessType = BusinessType.OTHER.getCode())     // 查询操作
     * 
     * ==================== 登录相关Controller ====================
     * 
     * @LoginLog("用户登录")                                                        // 登录操作
     * @LoginLog("用户登出")                                                        // 登出操作
     * 
     * ==================== 注解参数说明 ====================
     * 
     * title: 操作模块名称
     * businessType: 业务类型
     *   - BusinessType.OTHER.getCode()    = 0  其它操作（查询等）
     *   - BusinessType.INSERT.getCode()   = 1  新增操作
     *   - BusinessType.UPDATE.getCode()   = 2  修改操作
     *   - BusinessType.DELETE.getCode()   = 3  删除操作
     *   - BusinessType.GRANT.getCode()    = 4  授权操作
     *   - BusinessType.EXPORT.getCode()   = 5  导出操作
     *   - BusinessType.IMPORT.getCode()   = 6  导入操作
     *   - BusinessType.FORCE.getCode()    = 7  强退操作
     *   - BusinessType.GENCODE.getCode()  = 8  生成代码
     *   - BusinessType.CLEAN.getCode()    = 9  清空数据
     * 
     * operatorType: 操作类别（默认为1-后台用户）
     * isSaveRequestData: 是否保存请求参数（默认true）
     * isSaveResponseData: 是否保存响应参数（默认true）
     * excludeParamNames: 排除的参数名称（如密码字段）
     * 
     * ==================== 使用示例 ====================
     * 
     * // 基本用法
     * @OperLog(title = "用户管理", businessType = BusinessType.INSERT.getCode())
     * 
     * // 排除敏感参数
     * @OperLog(title = "用户管理", businessType = BusinessType.UPDATE.getCode(), 
     *          excludeParamNames = {"password", "oldPassword"})
     * 
     * // 不保存响应数据
     * @OperLog(title = "用户管理", businessType = BusinessType.EXPORT.getCode(), 
     *          isSaveResponseData = false)
     * 
     */
}
