package com.jcloud.admin.dto.request;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消费查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "消费查询请求")
public class ConsumeQueryRequest extends PageQuery {
    
    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "PAY202401010001")
    private String orderId;
    
    /**
     * 消费开始时间（时间戳）
     */
    @Schema(description = "消费开始时间", example = "1640995200")
    private Integer startTime;
    
    /**
     * 消费结束时间（时间戳）
     */
    @Schema(description = "消费结束时间", example = "1672531199")
    private Integer endTime;
    
    /**
     * 最小消费金额
     */
    @Schema(description = "最小消费金额", example = "10.00")
    private java.math.BigDecimal minAmount;
    
    /**
     * 最大消费金额
     */
    @Schema(description = "最大消费金额", example = "1000.00")
    private java.math.BigDecimal maxAmount;
    
    /**
     * 消费说明（模糊查询）
     */
    @Schema(description = "消费说明", example = "购买道具")
    private String info;
}
