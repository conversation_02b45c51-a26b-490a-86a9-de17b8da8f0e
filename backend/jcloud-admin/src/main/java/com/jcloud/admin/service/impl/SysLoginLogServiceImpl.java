package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysLoginLogService;
import com.jcloud.common.dto.LoginLogQueryRequest;
import com.jcloud.common.entity.SysLoginLog;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysLoginLogMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jcloud.common.util.TimeUtil;
import java.util.List;
import java.util.Map;

/**
 * 登录日志服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysLoginLogServiceImpl extends BaseServiceImpl<SysLoginLogMapper, SysLoginLog> implements SysLoginLogService {

    @Override
    public PageResult<SysLoginLog> pageLoginLogs(LoginLogQueryRequest queryRequest) {
        QueryWrapper queryWrapper = getQueryWrapper();

        // 构建查询条件
        if (StrUtil.isNotBlank(queryRequest.getUserName())) {
            queryWrapper.like("user_name", queryRequest.getUserName());
        }
        if (StrUtil.isNotBlank(queryRequest.getIpaddr())) {
            queryWrapper.like("ipaddr", queryRequest.getIpaddr());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getLoginTimeStart() != null) {
            queryWrapper.ge("login_time", queryRequest.getLoginTimeStart());
        }
        if (queryRequest.getLoginTimeEnd() != null) {
            queryWrapper.le("login_time", queryRequest.getLoginTimeEnd());
        }

        // 排序
        queryWrapper.orderBy("login_time", false);

        // 分页查询
        Page<SysLoginLog> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysLoginLog> pageResult = baseMapper.paginate(page, queryWrapper);

        return PageResult.of(pageResult.getRecords(), pageResult.getTotalRow(),
                queryRequest.getPageNum(), queryRequest.getPageSize());
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void recordLoginLog(SysLoginLog loginLog) {
        try {
            // 设置租户信息
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId != null) {
                loginLog.setTenantId(tenantId);
            }

            // 设置创建信息
            loginLog.setCreateTime(TimeUtil.now());
            Long userId = SecurityUtils.getUserId();
            if (userId != null) {
                loginLog.setCreateBy(userId);
            }

            boolean success = save(loginLog);
            if (success) {
                log.debug("记录登录日志成功：{} - {} - {}",
                        loginLog.getUserName(), loginLog.getIpaddr(), loginLog.getMsg());
            } else {
                log.warn("记录登录日志失败：{} - {}", loginLog.getUserName(), loginLog.getMsg());
            }
        } catch (Exception e) {
            log.error("记录登录日志异常：{} - {}", loginLog.getUserName(), loginLog.getMsg(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordLoginLogSync(SysLoginLog loginLog) {
        try {
            // 设置租户信息
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId != null) {
                loginLog.setTenantId(tenantId);
            }

            // 设置创建信息
            loginLog.setCreateTime(TimeUtil.now());
            Long userId = SecurityUtils.getUserId();
            if (userId != null) {
                loginLog.setCreateBy(userId);
            }

            boolean success = save(loginLog);
            if (success) {
                log.debug("同步记录登录日志成功：{} - {} - {}",
                        loginLog.getUserName(), loginLog.getIpaddr(), loginLog.getMsg());
            } else {
                log.warn("同步记录登录日志失败：{} - {}", loginLog.getUserName(), loginLog.getMsg());
            }
            return success;
        } catch (Exception e) {
            log.error("同步记录登录日志异常：{} - {}", loginLog.getUserName(), loginLog.getMsg(), e);
            return false;
        }
    }

    @Override
    public List<SysLoginLog> getLoginLogsByUserName(String userName) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByUserName(userName, tenantId);
    }

    @Override
    public List<SysLoginLog> getLoginLogsByIpaddr(String ipaddr) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByIpaddr(ipaddr, tenantId);
    }

    @Override
    public List<SysLoginLog> getLoginLogsByStatus(Integer status) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectByStatus(status, tenantId);
    }

    @Override
    public List<SysLoginLog> getRecentLoginLogs(int limit) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectRecentLogs(limit, tenantId);
    }

    @Override
    public List<Map<String, Object>> countByStatus() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countByStatus(tenantId);
    }

    @Override
    public int countTodayLogins() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countTodayLogins(tenantId);
    }

    @Override
    public int countFailedLogins() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.countFailedLogins(tenantId);
    }

    @Override
    public SysLoginLog getLastLoginByUserName(String userName) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        return baseMapper.selectLastLoginByUserName(userName, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanOldLoginLogs(int days) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }
        int count = baseMapper.cleanOldLogs(days, tenantId);
        log.info("清理{}天前的登录日志，共清理{}条", days, count);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLoginLogsBatch(List<Long> logIds) {
        if (logIds == null || logIds.isEmpty()) {
            return true;
        }

        boolean success = removeByIds(logIds);
        if (success) {
            log.info("批量删除登录日志成功，数量：{}", logIds.size());
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAllLoginLogs() {
        QueryWrapper queryWrapper = getQueryWrapper();
        boolean success = baseMapper.deleteByQuery(queryWrapper) > 0;
        if (success) {
            log.info("清空所有登录日志成功");
        }
        return success;
    }
}
