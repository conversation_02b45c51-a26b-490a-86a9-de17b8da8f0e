package com.jcloud.admin.service;

import com.jcloud.common.dto.TenantQueryRequest;
import com.jcloud.common.dto.TenantStatsDTO;
import com.jcloud.common.dto.TenantConfigDTO;
import com.jcloud.common.dto.TenantUserDTO;
import com.jcloud.common.entity.SysTenant;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 租户管理服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysTenantService {

    /**
     * 分页查询租户列表
     * 
     * @param queryRequest 查询请求
     * @return 分页结果
     */
    Page<SysTenant> pageTenants(TenantQueryRequest queryRequest);

    /**
     * 获取所有启用的租户
     * 
     * @return 租户列表
     */
    List<SysTenant> getAllEnabledTenants();

    /**
     * 根据ID获取租户详情
     * 
     * @param id 租户ID
     * @return 租户信息
     */
    SysTenant getTenantById(Long id);

    /**
     * 根据租户编码获取租户信息
     * 
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    SysTenant getTenantByCode(String tenantCode);

    /**
     * 创建租户
     * 
     * @param tenant 租户信息
     * @return 创建的租户
     */
    SysTenant createTenant(SysTenant tenant);

    /**
     * 更新租户
     * 
     * @param tenant 租户信息
     * @return 更新的租户
     */
    SysTenant updateTenant(SysTenant tenant);

    /**
     * 删除租户
     * 
     * @param id 租户ID
     * @return 是否成功
     */
    boolean deleteTenant(Long id);

    /**
     * 批量删除租户
     * 
     * @param ids 租户ID列表
     * @return 是否成功
     */
    boolean deleteTenants(List<Long> ids);

    /**
     * 启用租户
     * 
     * @param id 租户ID
     * @return 是否成功
     */
    boolean enableTenant(Long id);

    /**
     * 禁用租户
     * 
     * @param id 租户ID
     * @return 是否成功
     */
    boolean disableTenant(Long id);

    /**
     * 更新租户状态
     * 
     * @param id 租户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateTenantStatus(Long id, Integer status);

    /**
     * 检查租户编码是否存在
     * 
     * @param tenantCode 租户编码
     * @param excludeId 排除的租户ID（编辑时使用）
     * @return 是否存在
     */
    boolean checkTenantCodeExists(String tenantCode, Long excludeId);

    /**
     * 获取租户统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    TenantStatsDTO getTenantStats(Long tenantId);

    /**
     * 获取租户配置
     *
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<TenantConfigDTO> getTenantConfigs(Long tenantId);

    /**
     * 更新租户配置
     *
     * @param tenantId 租户ID
     * @param configs 配置列表
     * @return 是否成功
     */
    boolean updateTenantConfigs(Long tenantId, List<TenantConfigDTO> configs);

    /**
     * 获取租户用户列表
     *
     * @param tenantId 租户ID
     * @return 用户列表
     */
    List<TenantUserDTO> getTenantUsers(Long tenantId);

    /**
     * 获取可分配给租户的用户列表
     *
     * @param tenantId 租户ID
     * @return 可分配用户列表
     */
    List<TenantUserDTO> getAvailableUsersForTenant(Long tenantId);

    /**
     * 分配用户到租户
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean assignUsersToTenant(Long tenantId, List<Long> userIds);

    /**
     * 从租户移除用户
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeUserFromTenant(Long tenantId, Long userId);

    /**
     * 导出租户数据
     * 
     * @param tenantIds 租户ID列表
     * @return 导出数据
     */
    byte[] exportTenants(List<Long> tenantIds);
}
