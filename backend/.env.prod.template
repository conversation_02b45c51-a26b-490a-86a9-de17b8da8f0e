# jCloud生产环境变量配置模板
# 复制此文件为 .env.prod 并填入实际的生产环境配置

# ==================== 服务器配置 ====================
SERVER_PORT=8081

# ==================== 数据库配置 ====================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=jcloud
DB_USERNAME=jcloud_user
DB_PASSWORD=your_secure_password_here
DB_SSL=false

# ==================== Redis配置 ====================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DATABASE=10

# ==================== 日志配置 ====================
LOG_PATH=/app/logs

# ==================== sa-token配置 ====================
SA_TOKEN_NAME=Authorization
SA_TOKEN_TIMEOUT=7200
SA_TOKEN_ACTIVITY_TIMEOUT=1800
SA_TOKEN_CONCURRENT=true
SA_TOKEN_SHARE=true
SA_TOKEN_STYLE=uuid
SA_TOKEN_JWT_SECRET=jcloud-jwt-secret-key-prod-2024-change-this

# ==================== 验证码配置 ====================
CAPTCHA_ENABLED=true
CAPTCHA_TYPE=ALPHANUMERIC
CAPTCHA_LENGTH=4
CAPTCHA_EXPIRE=300
CAPTCHA_CASE_SENSITIVE=false
CAPTCHA_MAX_ATTEMPTS=5
CAPTCHA_LOCK_TIME=600
CAPTCHA_BRUTE_FORCE_PROTECTION=true

# ==================== 缓存配置 ====================
CACHE_TTL=3600

# ==================== 文件上传配置 ====================
FILE_UPLOAD_PATH=/app/uploads
FILE_MAX_SIZE=10MB
FILE_ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# ==================== 安全配置 ====================
# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_DIGIT=true
PASSWORD_REQUIRE_SPECIAL=false

# 登录安全
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCK_TIME=1800
LOGIN_CAPTCHA_AFTER_ATTEMPTS=3

# ==================== 监控配置 ====================
# Druid监控
DRUID_MONITOR_ENABLED=false
DRUID_USERNAME=admin
DRUID_PASSWORD=admin123
DRUID_ALLOW_IPS=127.0.0.1

# Prometheus监控
PROMETHEUS_ENABLED=false

# ==================== API文档配置 ====================
API_DOC_ENABLED=false
API_DOC_CONTACT=<EMAIL>
API_DOC_HOST=

# ==================== 其他配置 ====================
# 应用环境标识
SPRING_PROFILES_ACTIVE=prod

# JVM配置建议
# JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat