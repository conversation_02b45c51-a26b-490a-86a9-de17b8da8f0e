# jCloud后端Dockerfile
# 使用多阶段构建优化镜像大小

# 构建阶段
FROM maven:3.9.6-eclipse-temurin-21 AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY jcloud-common/pom.xml ./jcloud-common/
COPY jcloud-auth/pom.xml ./jcloud-auth/
COPY jcloud-admin/pom.xml ./jcloud-admin/

# 下载依赖（利用Docker缓存层）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY jcloud-common/src ./jcloud-common/src
COPY jcloud-auth/src ./jcloud-auth/src
COPY jcloud-admin/src ./jcloud-admin/src

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行阶段
FROM eclipse-temurin:21-jre-alpine

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户
RUN addgroup -g 1001 -S jcloud && \
    adduser -S jcloud -u 1001 -G jcloud

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads && \
    chown -R jcloud:jcloud /app

# 从构建阶段复制JAR文件
COPY --from=builder /app/jcloud-admin/target/jcloud-admin-1.0.0.jar app.jar

# 复制生产环境配置文件
COPY jcloud-admin/src/main/resources/application-prod.yml /app/config/

# 设置文件权限
RUN chown -R jcloud:jcloud /app

# 切换到应用用户
USER jcloud

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8081/api/actuator/health || exit 1

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx2g \
    -XX:+UseG1GC \
    -XX:+UseStringDeduplication \
    -XX:+OptimizeStringConcat \
    -XX:MaxGCPauseMillis=200 \
    -XX:+UnlockExperimentalVMOptions \
    -XX:+UseCGroupMemoryLimitForHeap \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.profiles.active=prod \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=Asia/Shanghai"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

# 元数据标签
LABEL maintainer="jCloud Team <<EMAIL>>" \
      version="1.0.0" \
      description="jCloud权限管理系统后端服务" \
      org.opencontainers.image.title="jCloud Backend" \
      org.opencontainers.image.description="jCloud权限管理系统后端服务" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="jCloud" \
      org.opencontainers.image.licenses="MIT"
