<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.SysSqlAuditLogMapper">

    <!-- 批量插入审计日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_sql_audit_log (
            platform, module, url, user, user_ip, host_ip, data_source,
            sql_statement, sql_params, execution_time, is_slow_sql, sql_type,
            affected_rows, status, error_message, create_time, tenant_id, ext_info
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.platform,jdbcType=VARCHAR},
                #{log.module,jdbcType=VARCHAR},
                #{log.url,jdbcType=VARCHAR},
                #{log.user,jdbcType=VARCHAR},
                #{log.userIp,jdbcType=VARCHAR},
                #{log.hostIp,jdbcType=VARCHAR},
                #{log.dataSource,jdbcType=VARCHAR},
                #{log.sqlStatement,jdbcType=LONGVARCHAR},
                #{log.sqlParams,jdbcType=LONGVARCHAR},
                #{log.executionTime,jdbcType=BIGINT},
                #{log.isSlowSql,jdbcType=TINYINT},
                #{log.sqlType,jdbcType=VARCHAR},
                #{log.affectedRows,jdbcType=INTEGER},
                #{log.status,jdbcType=VARCHAR},
                #{log.errorMessage,jdbcType=LONGVARCHAR},
                #{log.createTime,jdbcType=TIMESTAMP},
                #{log.tenantId,jdbcType=BIGINT},
                #{log.extInfo,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>

    <!-- 删除指定时间之前的审计日志 -->
    <delete id="deleteByCreateTimeBefore">
        DELETE FROM sys_sql_audit_log 
        WHERE create_time &lt; #{beforeDate,jdbcType=TIMESTAMP}
    </delete>

    <!-- 统计慢SQL数量 -->
    <select id="countSlowSql" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM sys_sql_audit_log 
        WHERE is_slow_sql = 1
        <if test="startTime != null">
            AND create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <!-- 统计各SQL类型的执行次数 -->
    <select id="countBySqlType" resultType="java.util.HashMap">
        SELECT 
            sql_type as sqlType,
            COUNT(*) as count,
            AVG(execution_time) as avgExecutionTime,
            MAX(execution_time) as maxExecutionTime
        FROM sys_sql_audit_log 
        WHERE 1=1
        <if test="startTime != null">
            AND create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY sql_type
        ORDER BY count DESC
    </select>

    <!-- 查询最耗时的SQL -->
    <select id="findTopSlowSql" resultType="com.jcloud.common.entity.SysSqlAuditLog">
        SELECT 
            id, platform, module, url, user, user_ip, host_ip, data_source,
            sql_statement, sql_params, execution_time, is_slow_sql, sql_type,
            affected_rows, status, error_message, create_time, tenant_id, ext_info
        FROM sys_sql_audit_log 
        ORDER BY execution_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 按用户统计SQL执行情况 -->
    <select id="countByUser" resultType="java.util.HashMap">
        SELECT 
            user,
            COUNT(*) as totalCount,
            SUM(CASE WHEN is_slow_sql = 1 THEN 1 ELSE 0 END) as slowSqlCount,
            AVG(execution_time) as avgExecutionTime
        FROM sys_sql_audit_log 
        WHERE create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        GROUP BY user
        ORDER BY totalCount DESC
        LIMIT 20
    </select>

    <!-- 按数据源统计SQL执行情况 -->
    <select id="countByDataSource" resultType="java.util.HashMap">
        SELECT 
            data_source as dataSource,
            COUNT(*) as totalCount,
            SUM(CASE WHEN is_slow_sql = 1 THEN 1 ELSE 0 END) as slowSqlCount,
            AVG(execution_time) as avgExecutionTime
        FROM sys_sql_audit_log 
        WHERE create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        GROUP BY data_source
        ORDER BY totalCount DESC
    </select>

    <!-- 查询SQL执行趋势（按小时统计） -->
    <select id="getExecutionTrend" resultType="java.util.HashMap">
        SELECT 
            DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as hour,
            COUNT(*) as totalCount,
            SUM(CASE WHEN is_slow_sql = 1 THEN 1 ELSE 0 END) as slowSqlCount,
            AVG(execution_time) as avgExecutionTime
        FROM sys_sql_audit_log 
        WHERE create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00')
        ORDER BY hour ASC
    </select>

</mapper>
