<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.SysMenuMapper">

    <!-- 根据权限编码列表查询菜单ID列表 -->
    <select id="selectMenuIdsByPermissionCodes" resultType="java.lang.Long">
        SELECT id FROM sys_menu
        WHERE tenant_id = #{tenantId}
          AND menu_type = 2
          AND status = 1
          AND deleted = 0
          AND permission_code IN
        <foreach collection="permissionCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

</mapper>
