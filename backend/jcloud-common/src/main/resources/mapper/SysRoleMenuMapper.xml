<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.SysRoleMenuMapper">

    <!-- 根据角色ID删除角色菜单关联 -->
    <delete id="deleteByRoleId">
        DELETE FROM sys_role_menu 
        WHERE role_id = #{roleId} AND tenant_id = #{tenantId}
    </delete>

    <!-- 根据菜单ID删除角色菜单关联 -->
    <delete id="deleteByMenuId">
        DELETE FROM sys_role_menu 
        WHERE menu_id = #{menuId} AND tenant_id = #{tenantId}
    </delete>

    <!-- 根据角色ID获取菜单列表 -->
    <select id="selectMenusByRoleId" resultType="com.jcloud.common.entity.SysMenu">
        SELECT m.*
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        WHERE rm.role_id = #{roleId} 
          AND rm.tenant_id = #{tenantId}
          AND m.tenant_id = #{tenantId}
          AND m.status = 1
          AND m.deleted = 0
        ORDER BY m.sort_order ASC, m.create_time ASC
    </select>

    <!-- 根据角色ID获取菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" resultType="java.lang.Long">
        SELECT rm.menu_id
        FROM sys_role_menu rm
        INNER JOIN sys_menu m ON rm.menu_id = m.id
        WHERE rm.role_id = #{roleId} 
          AND rm.tenant_id = #{tenantId}
          AND m.tenant_id = #{tenantId}
          AND m.status = 1
          AND m.deleted = 0
        ORDER BY m.sort_order ASC
    </select>

    <!-- 根据用户ID获取菜单列表（通过角色关联） -->
    <select id="selectMenusByUserId" resultType="com.jcloud.common.entity.SysMenu">
        SELECT DISTINCT m.*
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND ur.tenant_id = #{tenantId}
          AND rm.tenant_id = #{tenantId}
          AND m.tenant_id = #{tenantId}
          AND m.status = 1
          AND m.deleted = 0
          AND m.menu_type IN (0, 1)
        ORDER BY m.sort_order ASC, m.create_time ASC
    </select>

    <!-- 批量插入角色菜单关联 -->
    <insert id="batchInsert">
        INSERT INTO sys_role_menu (tenant_id, role_id, menu_id, create_time, create_by)
        VALUES
        <foreach collection="roleMenus" item="item" separator=",">
            (#{item.tenantId}, #{item.roleId}, #{item.menuId}, #{item.createTime}, #{item.createBy})
        </foreach>
    </insert>

</mapper>
