package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 角色查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "角色查询请求")
public class RoleQueryRequest extends PageQuery {

    /**
     * 搜索关键词（同时搜索角色编码和角色名称）
     */
    @Schema(description = "搜索关键词", example = "管理员")
    private String keyword;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码", example = "ADMIN")
    private String roleCode;
    
    /**
     * 角色名称
     */
    @Schema(description = "角色名称", example = "管理员")
    private String roleName;
    
    /**
     * 角色类型（SYSTEM-系统角色，CUSTOM-自定义角色）
     */
    @Schema(description = "角色类型", example = "SYSTEM")
    private String roleType;
    
    /**
     * 数据权限范围（ALL-全部，DEPT-部门，DEPT_AND_SUB-部门及子部门，SELF-仅本人，CUSTOM-自定义）
     */
    @Schema(description = "数据权限范围", example = "ALL")
    private String dataScope;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
}
