package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户部门关联实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("sys_user_dept")
@Schema(description = "用户部门关联")
public class SysUserDept extends BaseEntity {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 是否主部门（0-否，1-是）
     */
    @Schema(description = "是否主部门")
    private Integer isMain;

    /**
     * 是否删除（0-否，1-是）
     */
    @Schema(description = "是否删除")
    private Integer deleted = 0;

    /**
     * 版本号（乐观锁）
     */
    @Schema(description = "版本号")
    private Integer version = 0;
}
