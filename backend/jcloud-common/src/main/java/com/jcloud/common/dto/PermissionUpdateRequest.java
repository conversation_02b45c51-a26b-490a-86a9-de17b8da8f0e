package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

/**
 * 权限更新请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "权限更新请求")
public class PermissionUpdateRequest {
    
    /**
     * 权限ID
     */
    @Schema(description = "权限ID", example = "1")
    @NotNull(message = "权限ID不能为空")
    private Long id;
    
    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户列表")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;
    
    /**
     * 资源路径
     */
    @Schema(description = "资源路径", example = "/system/user/page")
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET")
    @Size(max = 10, message = "HTTP方法长度不能超过10个字符")
    @Pattern(regexp = "^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)?$", message = "HTTP方法不正确")
    private String method;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "用户管理相关权限")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
