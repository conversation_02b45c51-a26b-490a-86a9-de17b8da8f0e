package com.jcloud.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取地址类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class AddressUtils {
    
    /**
     * IP地址查询
     */
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
    
    // 未知地址
    public static final String UNKNOWN = "XX XX";
    
    /**
     * 根据IP地址获取地理位置（性能优化版本）
     *
     * @param ip IP地址
     * @return 地理位置
     */
    public static String getRealAddressByIP(String ip) {
        // 参数校验
        if (StrUtil.isEmpty(ip)) {
            return "未知位置";
        }

        // 处理本地回环地址（包括IPv6）
        if (isLocalAddress(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            return "本地访问";
        }

        // 内网不查询
        if (isInternalIP(ip)) {
            return "内网IP";
        }

        // 生产环境可以通过配置开启真实的IP地理位置查询
        log.debug("跳过IP地理位置查询以提升性能: {}", ip);
        return "外网IP";

        // 注释掉的原始网络请求代码（生产环境可选择性启用）
        /*
        try {
            String rspStr = HttpUtil.get(IP_URL + "?ip=" + ip + "&json=true", 1000); // 减少超时时间到1秒
            if (StrUtil.isEmpty(rspStr)) {
                return UNKNOWN;
            }

            // 检查响应是否为有效的JSON格式
            if (!isValidJson(rspStr)) {
                log.warn("获取地理位置响应格式异常，IP: {}", ip);
                return UNKNOWN;
            }

            JSONObject obj = JSONUtil.parseObj(rspStr);
            String region = obj.getStr("pro");
            String city = obj.getStr("city");

            // 处理空值情况
            if (StrUtil.isEmpty(region) && StrUtil.isEmpty(city)) {
                return UNKNOWN;
            }

            region = StrUtil.isEmpty(region) ? "" : region;
            city = StrUtil.isEmpty(city) ? "" : city;

            return String.format("%s %s", region, city).trim();
        } catch (Exception e) {
            log.error("获取地理位置异常，IP: {}", ip, e);
        }
        return UNKNOWN;
        */
    }
    
    /**
     * 检查是否为本地地址（包括IPv4和IPv6的本地回环地址）
     *
     * @param ip IP地址
     * @return 是否为本地地址
     */
    public static boolean isLocalAddress(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return false;
        }

        // IPv4本地回环地址
        if ("127.0.0.1".equals(ip) || "localhost".equalsIgnoreCase(ip)) {
            return true;
        }

        // IPv6本地回环地址
        if ("::1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            return true;
        }

        return false;
    }

    /**
     * 检查字符串是否为有效的JSON格式
     *
     * @param jsonStr JSON字符串
     * @return 是否为有效JSON
     */
    public static boolean isValidJson(String jsonStr) {
        if (StrUtil.isEmpty(jsonStr)) {
            return false;
        }

        try {
            JSONUtil.parseObj(jsonStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为内网IP地址
     *
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isInternalIP(String ip) {
        if (StrUtil.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            return false;
        }

        // 先检查是否为本地地址
        if (isLocalAddress(ip)) {
            return true;
        }

        // 检查IPv6内网地址
        if (ip.contains(":")) {
            return isInternalIPv6(ip);
        }

        // 检查IPv4内网地址
        byte[] addr = textToNumericFormatV4(ip);
        if (addr == null) {
            return false;
        }
        
        final byte b0 = addr[0];
        final byte b1 = addr[1];
        
        // 10.x.x.x/8
        final byte SECTION_1 = 0x0A;
        // 172.16.x.x/12
        final byte SECTION_2 = (byte) 0xAC;
        final byte SECTION_3 = (byte) 0x10;
        final byte SECTION_4 = (byte) 0x1F;
        // 192.168.x.x/16
        final byte SECTION_5 = (byte) 0xC0;
        final byte SECTION_6 = (byte) 0xA8;
        
        switch (b0) {
            case SECTION_1:
                return true;
            case SECTION_2:
                if (b1 >= SECTION_3 && b1 <= SECTION_4) {
                    return true;
                }
            case SECTION_5:
                switch (b1) {
                    case SECTION_6:
                        return true;
                }
            default:
                return false;
        }
    }

    /**
     * 检查是否为IPv6内网地址
     *
     * @param ip IPv6地址
     * @return 是否为IPv6内网地址
     */
    public static boolean isInternalIPv6(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return false;
        }

        // IPv6本地回环地址
        if ("::1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            return true;
        }

        // IPv6链路本地地址 (fe80::/10)
        if (ip.toLowerCase().startsWith("fe80:")) {
            return true;
        }

        // IPv6唯一本地地址 (fc00::/7)
        if (ip.toLowerCase().startsWith("fc") || ip.toLowerCase().startsWith("fd")) {
            return true;
        }

        // IPv6站点本地地址 (fec0::/10) - 已废弃但仍可能存在
        if (ip.toLowerCase().startsWith("fec0:")) {
            return true;
        }

        return false;
    }

    /**
     * 将IPv4地址转换成字节
     * 
     * @param text IPv4地址
     * @return byte 字节
     */
    public static byte[] textToNumericFormatV4(String text) {
        if (text.length() == 0) {
            return null;
        }
        
        byte[] bytes = new byte[4];
        String[] elements = text.split("\\.", -1);
        try {
            long l;
            int i;
            switch (elements.length) {
                case 1:
                    l = Long.parseLong(elements[0]);
                    if ((l < 0L) || (l > 4294967295L)) {
                        return null;
                    }
                    bytes[0] = (byte) (int) (l >> 24 & 0xFF);
                    bytes[1] = (byte) (int) ((l & 0xFFFFFF) >> 16 & 0xFF);
                    bytes[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 2:
                    l = Integer.parseInt(elements[0]);
                    if ((l < 0L) || (l > 255L)) {
                        return null;
                    }
                    bytes[0] = (byte) (int) (l & 0xFF);
                    l = Integer.parseInt(elements[1]);
                    if ((l < 0L) || (l > 16777215L)) {
                        return null;
                    }
                    bytes[1] = (byte) (int) (l >> 16 & 0xFF);
                    bytes[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 3:
                    for (i = 0; i < 2; ++i) {
                        l = Integer.parseInt(elements[i]);
                        if ((l < 0L) || (l > 255L)) {
                            return null;
                        }
                        bytes[i] = (byte) (int) (l & 0xFF);
                    }
                    l = Integer.parseInt(elements[2]);
                    if ((l < 0L) || (l > 65535L)) {
                        return null;
                    }
                    bytes[2] = (byte) (int) (l >> 8 & 0xFF);
                    bytes[3] = (byte) (int) (l & 0xFF);
                    break;
                case 4:
                    for (i = 0; i < 4; ++i) {
                        l = Integer.parseInt(elements[i]);
                        if ((l < 0L) || (l > 255L)) {
                            return null;
                        }
                        bytes[i] = (byte) (int) (l & 0xFF);
                    }
                    break;
                default:
                    return null;
            }
        } catch (NumberFormatException e) {
            return null;
        }
        return bytes;
    }
}
