package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 菜单查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "菜单查询请求")
public class MenuQueryRequest extends PageQuery {
    
    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID", example = "0")
    private Long parentId;
    
    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称", example = "用户管理")
    private String menuName;
    
    /**
     * 菜单类型（0-目录，1-菜单，2-按钮）
     */
    @Schema(description = "菜单类型", example = "1")
    private Integer menuType;
    
    /**
     * 路由路径
     */
    @Schema(description = "路由路径", example = "/system/user")
    private String path;
    
    /**
     * 权限标识
     */
    @Schema(description = "权限标识", example = "system:user:list")
    private String permissionCode;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 是否显示（0-隐藏，1-显示）
     */
    @Schema(description = "是否显示", example = "1")
    private Integer visible;
    
    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
}
