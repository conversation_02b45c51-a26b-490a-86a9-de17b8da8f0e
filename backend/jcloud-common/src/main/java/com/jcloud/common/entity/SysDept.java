package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 部门实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_dept")
@Schema(description = "部门信息")
public class SysDept extends BaseEntity {
    
    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID")
    private Long parentId;
    
    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String deptCode;
    
    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;
    
    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private Long leaderId;
    
    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
