package com.jcloud.common.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 基础实体类
 * 包含公共字段：ID、创建时间、更新时间、创建人、更新人等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "基础实体")
public abstract class BaseEntity {
    
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 创建时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()")
    @Schema(description = "创建时间")
    private Long createTime;
    
    /**
     * 更新时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()", onUpdateValue = "unix_timestamp()")
    @Schema(description = "更新时间")
    private Long updateTime;
    
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createBy;
    
    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateBy;
    
    /**
     * 租户ID（多租户支持）
     */
    @Column(tenantId = true)
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    @Column(isLogicDelete = true)
    @Schema(description = "删除标识")
    private Integer deleted = 0;
    
    /**
     * 版本号（乐观锁）
     */
    @Column(version = true)
    @Schema(description = "版本号")
    private Integer version = 0;
}
