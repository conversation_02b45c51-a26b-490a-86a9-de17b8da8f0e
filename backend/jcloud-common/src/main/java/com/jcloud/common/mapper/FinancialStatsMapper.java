package com.jcloud.common.mapper;

import com.jcloud.common.annotation.DataSource;
import com.jcloud.common.dto.FinancialStatsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 财务统计Mapper接口
 * 专用于调用财务统计存储过程，使用从库数据源
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@DataSource("slave")
public interface FinancialStatsMapper {
    
    /**
     * 调用财务统计存储过程
     * 通过存储过程GetStatisticsReport获取财务统计数据
     * 
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @param includeAnchor 是否包含主播数据
     * @return 财务统计数据列表
     */
    List<FinancialStatsResponse> getStatisticsReport(
        @Param("startTime") String startTime,
        @Param("endTime") String endTime,
        @Param("includeAnchor") Boolean includeAnchor
    );
}