package com.jcloud.common.audit;

import cn.dev33.satoken.stp.StpUtil;
import com.jcloud.common.config.SqlAuditProperties;
import com.jcloud.common.entity.SysSqlAuditLog;
import com.jcloud.common.mapper.SysSqlAuditLogMapper;
import com.mybatisflex.core.audit.AuditMessage;
import com.mybatisflex.core.audit.MessageReporter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数据库消息报告器
 * 将SQL审计日志异步存储到数据库中
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseMessageReporter implements MessageReporter {

    private final SysSqlAuditLogMapper sqlAuditLogMapper;
    private final SqlAuditProperties sqlAuditProperties;
    private final DataMaskingProcessor dataMaskingProcessor;

    /**
     * 消息缓冲队列
     */
    private final ConcurrentLinkedQueue<AuditMessage> messageBuffer = new ConcurrentLinkedQueue<>();
    
    /**
     * 缓冲区计数器
     */
    private final AtomicInteger bufferCount = new AtomicInteger(0);

    @Override
    public void sendMessages(List<AuditMessage> messages) {
        if (!sqlAuditProperties.isDatabaseStorageEnabled() || messages == null || messages.isEmpty()) {
            return;
        }

        try {
            if (sqlAuditProperties.getDatabase().isAsync()) {
                // 异步处理
                handleMessagesAsync(messages);
            } else {
                // 同步处理
                handleMessagesSync(messages);
            }
        } catch (Exception e) {
            log.error("处理SQL审计消息失败", e);
        }
    }

    /**
     * 异步处理消息
     */
    @Async
    public void handleMessagesAsync(List<AuditMessage> messages) {
        // 添加到缓冲区
        for (AuditMessage message : messages) {
            messageBuffer.offer(message);
            bufferCount.incrementAndGet();
        }

        // 检查是否需要刷新缓冲区
        if (shouldFlushBuffer()) {
            flushBuffer();
        }
    }

    /**
     * 同步处理消息
     */
    private void handleMessagesSync(List<AuditMessage> messages) {
        List<SysSqlAuditLog> auditLogs = convertToAuditLogs(messages);
        if (!auditLogs.isEmpty()) {
            saveAuditLogs(auditLogs);
        }
    }

    /**
     * 判断是否应该刷新缓冲区
     */
    private boolean shouldFlushBuffer() {
        int batchSize = sqlAuditProperties.getDatabase().getBatchSize();
        return bufferCount.get() >= batchSize;
    }

    /**
     * 刷新缓冲区
     */
    @Async
    public void flushBuffer() {
        if (messageBuffer.isEmpty()) {
            return;
        }

        List<AuditMessage> messagesToProcess = new ArrayList<>();
        AuditMessage message;
        
        // 从缓冲区取出消息
        while ((message = messageBuffer.poll()) != null && 
               messagesToProcess.size() < sqlAuditProperties.getDatabase().getBatchSize()) {
            messagesToProcess.add(message);
            bufferCount.decrementAndGet();
        }

        if (!messagesToProcess.isEmpty()) {
            List<SysSqlAuditLog> auditLogs = convertToAuditLogs(messagesToProcess);
            saveAuditLogs(auditLogs);
        }
    }

    /**
     * 将审计消息转换为审计日志实体
     */
    private List<SysSqlAuditLog> convertToAuditLogs(List<AuditMessage> messages) {
        List<SysSqlAuditLog> auditLogs = new ArrayList<>();
        
        for (AuditMessage message : messages) {
            try {
                SysSqlAuditLog auditLog = convertToAuditLog(message);
                if (auditLog != null) {
                    auditLogs.add(auditLog);
                }
            } catch (Exception e) {
                log.warn("转换审计消息失败: {}", message, e);
            }
        }
        
        return auditLogs;
    }

    /**
     * 将单个审计消息转换为审计日志实体
     */
    private SysSqlAuditLog convertToAuditLog(AuditMessage message) {
        if (message == null) {
            return null;
        }

        // 对SQL参数进行脱敏处理
        Object[] queryParams = message.getQueryParams() != null ?
            message.getQueryParams().toArray() : new Object[0];
        Object[] maskedParams = dataMaskingProcessor.maskSensitiveData(
            message.getFullSql(),
            queryParams
        );

        // 获取租户ID
        Long tenantId = null;
        try {
            if (StpUtil.isLogin()) {
                Object tenantIdObj = StpUtil.getSession().get("tenantId");
                if (tenantIdObj != null) {
                    tenantId = Long.valueOf(tenantIdObj.toString());
                }
            }
        } catch (Exception e) {
            // 忽略获取租户ID失败的异常
        }

        return SysSqlAuditLog.builder()
                .platform(message.getPlatform())
                .module(message.getModule())
                .url(message.getUrl())
                .user(message.getUser())
                .userIp(message.getUserIp())
                .hostIp(message.getHostIp())
                .dataSource(getCurrentDataSource())
                .sqlStatement(message.getFullSql())
                .sqlParams(formatSqlParams(maskedParams))
                .executionTime(message.getElapsedTime())
                .isSlowSql(SysSqlAuditLog.isSlowSql(message.getElapsedTime(), 
                    sqlAuditProperties.getSlowSqlThreshold()))
                .sqlType(SysSqlAuditLog.getSqlType(message.getFullSql()))
                .affectedRows(message.getQueryCount())
                .status("SUCCESS")
                .createTime(LocalDateTime.now())
                .tenantId(tenantId)
                .build();
    }

    /**
     * 获取当前数据源
     */
    private String getCurrentDataSource() {
        try {
            Class<?> contextHolderClass = Class.forName("com.jcloud.common.config.DataSourceContextHolder");
            java.lang.reflect.Method getDataSourceMethod = contextHolderClass.getMethod("getDataSource");
            String dataSource = (String) getDataSourceMethod.invoke(null);
            return dataSource != null ? dataSource : "master";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 格式化SQL参数
     */
    private String formatSqlParams(Object[] params) {
        if (params == null || params.length == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(params[i] != null ? params[i].toString() : "null");
        }
        sb.append("]");
        
        return sb.toString();
    }

    /**
     * 保存审计日志到数据库
     */
    private void saveAuditLogs(List<SysSqlAuditLog> auditLogs) {
        try {
            if (auditLogs.size() == 1) {
                sqlAuditLogMapper.insert(auditLogs.get(0));
            } else {
                sqlAuditLogMapper.batchInsert(auditLogs);
            }
            
            log.debug("成功保存{}条SQL审计日志", auditLogs.size());
        } catch (Exception e) {
            log.error("保存SQL审计日志失败", e);
        }
    }
}
