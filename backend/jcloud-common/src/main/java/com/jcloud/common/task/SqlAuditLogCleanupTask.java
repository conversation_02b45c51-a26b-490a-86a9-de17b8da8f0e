package com.jcloud.common.task;

import com.jcloud.common.config.SqlAuditProperties;
import com.jcloud.common.service.SqlAuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * SQL审计日志清理定时任务
 * 定期清理过期的SQL审计日志，避免数据库存储空间过大
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "jcloud.sql-audit", name = "enabled", havingValue = "true")
public class SqlAuditLogCleanupTask {

    private final SqlAuditLogService sqlAuditLogService;
    private final SqlAuditProperties sqlAuditProperties;

    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredLogs() {
        if (!sqlAuditProperties.isDatabaseStorageEnabled()) {
            log.debug("数据库存储未启用，跳过SQL审计日志清理任务");
            return;
        }

        int retentionDays = sqlAuditProperties.getDatabase().getRetentionDays();
        if (retentionDays <= 0) {
            log.debug("保留天数设置为永久保留，跳过SQL审计日志清理任务");
            return;
        }

        try {
            log.info("开始执行SQL审计日志清理任务，保留{}天内的数据", retentionDays);
            
            long startTime = System.currentTimeMillis();
            int deletedCount = sqlAuditLogService.cleanupExpiredLogs(retentionDays);
            long endTime = System.currentTimeMillis();
            
            log.info("SQL审计日志清理任务完成，删除{}条记录，耗时{}ms", 
                deletedCount, (endTime - startTime));
                
        } catch (Exception e) {
            log.error("SQL审计日志清理任务执行失败", e);
        }
    }

    /**
     * 每小时执行一次缓冲区刷新（如果启用了异步存储）
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void flushAuditBuffer() {
        if (!sqlAuditProperties.isDatabaseStorageEnabled() || 
            !sqlAuditProperties.getDatabase().isAsync()) {
            return;
        }

        try {
            // 这里可以添加强制刷新缓冲区的逻辑
            // 由于DatabaseMessageReporter已经有自动刷新机制，这里主要是作为备用
            log.debug("执行SQL审计日志缓冲区检查");
        } catch (Exception e) {
            log.warn("SQL审计日志缓冲区检查失败", e);
        }
    }
}
