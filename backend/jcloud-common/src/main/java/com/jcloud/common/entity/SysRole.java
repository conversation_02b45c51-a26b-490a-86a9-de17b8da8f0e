package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 角色实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_role")
@Schema(description = "角色信息")
public class SysRole extends BaseEntity {
    
    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;
    
    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;
    
    /**
     * 角色类型（SYSTEM-系统角色，CUSTOM-自定义角色）
     */
    @Schema(description = "角色类型")
    private String roleType;
    
    /**
     * 数据权限范围（ALL-全部，DEPT-部门，DEPT_AND_SUB-部门及子部门，SELF-仅本人，CUSTOM-自定义）
     */
    @Schema(description = "数据权限范围")
    private String dataScope;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
