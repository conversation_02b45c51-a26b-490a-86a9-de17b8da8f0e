package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_user")
@Schema(description = "用户信息")
public class SysUser extends BaseEntity {
    
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;
    
    /**
     * 密码
     * 注意：使用JsonProperty控制序列化行为
     * - 缓存序列化时保留密码字段（用于登录验证）
     * - API响应时隐藏密码字段（安全考虑）
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Schema(description = "密码", hidden = true)
    private String password;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatar;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;
    
    /**
     * 性别（0-未知，1-男，2-女）
     */
    @Schema(description = "性别")
    private Integer gender;
    
    /**
     * 生日
     */
    @Schema(description = "生日")
    private LocalDate birthday;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 是否管理员（0-否，1-是）
     */
    @Schema(description = "是否管理员")
    private Integer isAdmin;
    
    /**
     * 最后登录时间（时间戳，秒）
     */
    @Schema(description = "最后登录时间")
    private Long lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String lastLoginIp;
    
    /**
     * 密码更新时间（时间戳，秒）
     */
    @Schema(description = "密码更新时间")
    private Long passwordUpdateTime;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
