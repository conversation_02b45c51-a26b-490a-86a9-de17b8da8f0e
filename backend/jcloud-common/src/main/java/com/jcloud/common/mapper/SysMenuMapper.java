package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysMenu;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 菜单Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {
    
    /**
     * 根据父菜单ID查询子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE parent_id = #{parentId} AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 根据用户ID查询菜单列表（通过角色权限）
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    @Select("SELECT DISTINCT m.* FROM sys_menu m " +
            "INNER JOIN sys_permission p ON m.permission_code = p.permission_code " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} " +
            "AND m.status = 1 AND m.visible = 1 AND m.deleted = 0 " +
            "AND m.menu_type IN (0, 1) " +
            "AND p.status = 1 AND p.deleted = 0 " +
            "AND r.status = 1 AND r.deleted = 0 " +
            "ORDER BY m.sort_order ASC, m.create_time ASC")
    List<SysMenu> selectMenusByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据菜单类型查询菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE menu_type = #{menuType} AND status = 1 AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectByMenuType(@Param("menuType") Integer menuType);
    
    /**
     * 根据权限标识查询菜单
     * 
     * @param permissionCode 权限标识
     * @return 菜单信息
     */
    @Select("SELECT * FROM sys_menu WHERE permission_code = #{permissionCode} AND deleted = 0")
    SysMenu selectByPermissionCode(@Param("permissionCode") String permissionCode);
    
    /**
     * 检查菜单名称是否存在（同级菜单下）
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @param excludeMenuId 排除的菜单ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_menu " +
            "WHERE menu_name = #{menuName} AND parent_id = #{parentId} AND tenant_id = #{tenantId} AND deleted = 0 " +
            "AND (#{excludeMenuId} IS NULL OR id != #{excludeMenuId})")
    Integer existsByMenuNameAndParentId(@Param("menuName") String menuName,
                                      @Param("parentId") Long parentId,
                                      @Param("excludeMenuId") Long excludeMenuId,
                                      @Param("tenantId") Long tenantId);
    
    /**
     * 检查路由路径是否存在
     *
     * @param path 路由路径
     * @param excludeMenuId 排除的菜单ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_menu " +
            "WHERE path = #{path} AND tenant_id = #{tenantId} AND deleted = 0 " +
            "AND (#{excludeMenuId} IS NULL OR id != #{excludeMenuId})")
    Integer existsByPath(@Param("path") String path, @Param("excludeMenuId") Long excludeMenuId, @Param("tenantId") Long tenantId);
    
    /**
     * 统计子菜单数量
     * 
     * @param parentId 父菜单ID
     * @return 子菜单数量
     */
    @Select("SELECT COUNT(1) FROM sys_menu WHERE parent_id = #{parentId} AND deleted = 0")
    int countByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询所有启用的菜单
     * 
     * @return 菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE status = 1 AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectAllEnabledMenus();
    
    /**
     * 查询所有可见的菜单
     * 
     * @return 菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE status = 1 AND visible = 1 AND deleted = 0 " +
            "AND menu_type IN (0, 1) " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectAllVisibleMenus();

    /**
     * 查询所有可见的导航菜单（不包含按钮类型）
     *
     * @return 导航菜单列表
     */
    @Select("SELECT * FROM sys_menu WHERE status = 1 AND visible = 1 AND deleted = 0 " +
            "AND menu_type IN (0, 1) " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectAllVisibleNavigationMenus();

    /**
     * 根据菜单ID列表查询菜单
     * 
     * @param menuIds 菜单ID列表
     * @return 菜单列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_menu WHERE id IN " +
            "<foreach collection='menuIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND deleted = 0 ORDER BY sort_order ASC, create_time ASC" +
            "</script>")
    List<SysMenu> selectByIds(@Param("menuIds") List<Long> menuIds);
    
    /**
     * 查询菜单的所有父级菜单ID
     * 
     * @param menuId 菜单ID
     * @return 父级菜单ID列表
     */
    @Select("WITH RECURSIVE menu_tree AS (" +
            "  SELECT id, parent_id, menu_name FROM sys_menu WHERE id = #{menuId} AND deleted = 0" +
            "  UNION ALL" +
            "  SELECT m.id, m.parent_id, m.menu_name FROM sys_menu m " +
            "  INNER JOIN menu_tree mt ON m.id = mt.parent_id WHERE m.deleted = 0" +
            ") SELECT id FROM menu_tree WHERE id != #{menuId}")
    List<Long> selectParentIds(@Param("menuId") Long menuId);
    
    /**
     * 查询菜单的所有子级菜单ID
     * 
     * @param menuId 菜单ID
     * @return 子级菜单ID列表
     */
    @Select("WITH RECURSIVE menu_tree AS (" +
            "  SELECT id, parent_id, menu_name FROM sys_menu WHERE parent_id = #{menuId} AND deleted = 0" +
            "  UNION ALL" +
            "  SELECT m.id, m.parent_id, m.menu_name FROM sys_menu m " +
            "  INNER JOIN menu_tree mt ON m.parent_id = mt.id WHERE m.deleted = 0" +
            ") SELECT id FROM menu_tree")
    List<Long> selectChildIds(@Param("menuId") Long menuId);

    // ==================== 权限相关查询方法 ====================

    /**
     * 根据用户ID查询权限编码列表
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 权限编码列表
     */
    @Select("SELECT DISTINCT m.permission_code FROM sys_menu m " +
            "INNER JOIN sys_role_menu rm ON m.id = rm.menu_id " +
            "INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} " +
            "AND m.status = 1 AND m.deleted = 0 " +
            "AND m.permission_code IS NOT NULL AND m.permission_code != '' " +
            "AND r.status = 1 AND r.deleted = 0 " +
            "ORDER BY m.permission_code")
    List<String> selectPermissionCodesByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 根据角色ID查询权限编码列表
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 权限编码列表
     */
    @Select("SELECT DISTINCT m.permission_code FROM sys_menu m " +
            "INNER JOIN sys_role_menu rm ON m.id = rm.menu_id " +
            "WHERE rm.role_id = #{roleId} AND rm.tenant_id = #{tenantId} " +
            " AND m.status = 1 AND m.deleted = 0 " +
            "AND m.permission_code IS NOT NULL AND m.permission_code != '' " +
            "ORDER BY m.permission_code")
    List<String> selectPermissionCodesByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);

    /**
     * 根据角色ID查询权限菜单列表
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 权限菜单列表
     */
    @Select("SELECT m.* FROM sys_menu m " +
            "INNER JOIN sys_role_menu rm ON m.id = rm.menu_id " +
            "WHERE rm.role_id = #{roleId} AND rm.tenant_id = #{tenantId} " +
            " AND m.status = 1 AND m.deleted = 0 " +
            "ORDER BY m.sort_order ASC, m.create_time ASC")
    List<SysMenu> selectPermissionMenusByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);

    /**
     * 查询所有权限菜单
     * @param tenantId 租户ID
     * @return 权限菜单列表
     */
    @Select("SELECT * FROM sys_menu " +
            "WHERE tenant_id = #{tenantId}" +
            "AND status = 1 AND deleted = 0 " +
            "AND permission_code IS NOT NULL AND permission_code != '' " +
            "ORDER BY parent_id ASC, sort_order ASC, create_time ASC")
    List<SysMenu> selectAllPermissionMenus(@Param("tenantId") Long tenantId);

    /**
     * 根据权限编码查询权限菜单
     * @param permissionCode 权限编码
     * @param tenantId 租户ID
     * @return 权限菜单
     */
    @Select("SELECT * FROM sys_menu " +
            "WHERE permission_code = #{permissionCode} AND tenant_id = #{tenantId} " +
            "AND menu_type IN (1, 2) AND status = 1 AND deleted = 0 " +
            "LIMIT 1")
    SysMenu selectPermissionMenuByCode(@Param("permissionCode") String permissionCode, @Param("tenantId") Long tenantId);

    /**
     * 根据父菜单ID查询权限菜单列表
     * @param parentId 父菜单ID
     * @param tenantId 租户ID
     * @return 权限菜单列表
     */
    @Select("SELECT * FROM sys_menu " +
            "WHERE parent_id = #{parentId} AND tenant_id = #{tenantId} " +
            "AND menu_type IN (1, 2) AND status = 1 AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysMenu> selectPermissionMenusByParentId(@Param("parentId") Long parentId, @Param("tenantId") Long tenantId);

    /**
     * 根据权限编码列表查询菜单ID列表
     * @param permissionCodes 权限编码列表
     * @param tenantId 租户ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByPermissionCodes(@Param("permissionCodes") List<String> permissionCodes, @Param("tenantId") Long tenantId);

    /**
     * 删除角色的权限菜单关联（只删除按钮类型）
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_role_menu " +
            "WHERE role_id = #{roleId} AND tenant_id = #{tenantId} " +
            "AND menu_id IN (" +
            "  SELECT id FROM sys_menu " +
            "  WHERE menu_type IN (1, 2) AND deleted = 0" +
            ")")
    int deleteRolePermissionMenus(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
}
