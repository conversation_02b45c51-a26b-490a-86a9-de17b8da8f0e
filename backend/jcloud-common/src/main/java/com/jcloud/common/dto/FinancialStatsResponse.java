package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;

/**
 * 财务统计数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "财务统计数据")
public class FinancialStatsResponse {
    
    /**
     * 统计分类
     */
    @Schema(description = "统计分类", example = "用户相关统计")
    private String category;
    
    /**
     * 统计项名称
     */
    @Schema(description = "统计项名称", example = "新增用户数")
    private String statName;
    
    /**
     * 统计值
     */
    @Schema(description = "统计值", example = "1000.50")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal statValue;
    
    /**
     * 单位
     */
    @Schema(description = "单位", example = "元")
    private String unit;
}