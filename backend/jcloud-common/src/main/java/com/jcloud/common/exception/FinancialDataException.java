package com.jcloud.common.exception;

/**
 * 财务数据异常类
 * 用于处理财务数据查询过程中的业务异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FinancialDataException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    public FinancialDataException() {
        super();
    }
    
    public FinancialDataException(String message) {
        super(message);
    }
    
    public FinancialDataException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public FinancialDataException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public FinancialDataException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    /**
     * 创建存储过程调用异常
     */
    public static FinancialDataException procedureCallError(String message, Throwable cause) {
        return new FinancialDataException("PROCEDURE_CALL_ERROR", message, cause);
    }
    
    /**
     * 创建参数验证异常
     */
    public static FinancialDataException parameterValidationError(String message) {
        return new FinancialDataException("PARAMETER_VALIDATION_ERROR", message);
    }
    
    /**
     * 创建数据源连接异常
     */
    public static FinancialDataException dataSourceConnectionError(String message, Throwable cause) {
        return new FinancialDataException("DATASOURCE_CONNECTION_ERROR", message, cause);
    }
    
    /**
     * 创建查询超时异常
     */
    public static FinancialDataException queryTimeoutError(String message, Throwable cause) {
        return new FinancialDataException("QUERY_TIMEOUT_ERROR", message, cause);
    }
}