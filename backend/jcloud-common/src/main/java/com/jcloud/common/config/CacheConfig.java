package com.jcloud.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 支持Redis和Caffeine多级缓存
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Redis缓存管理器
     * 使用GenericJackson2JsonRedisSerializer解决类型转换问题
     * 强制使用Jackson序列化器，避免FastJSON2干扰
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "jcloud.cache.type", havingValue = "redis", matchIfMissing = true)
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        log.info("配置Redis缓存管理器 - 强制使用Jackson序列化器");

        // 使用默认的GenericJackson2JsonRedisSerializer，它会自动保存类型信息
        GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();

        // 配置缓存
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                // 默认1小时过期
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(org.springframework.data.redis.serializer.RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(org.springframework.data.redis.serializer.RedisSerializationContext.SerializationPair.fromSerializer(jsonRedisSerializer))
                .disableCachingNullValues(); // 不缓存空值

        // 针对不同缓存设置不同的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户缓存 - 30分钟
        cacheConfigurations.put("userCache", config.entryTtl(Duration.ofMinutes(30)));
        
        // 角色缓存 - 1小时
        cacheConfigurations.put("roleCache", config.entryTtl(Duration.ofHours(1)));
        
        // 权限缓存 - 2小时
        cacheConfigurations.put("permissionCache", config.entryTtl(Duration.ofHours(2)));
        
        // 租户缓存 - 4小时
        cacheConfigurations.put("tenantCache", config.entryTtl(Duration.ofHours(4)));
        
        // 菜单缓存 - 2小时
        cacheConfigurations.put("menuCache", config.entryTtl(Duration.ofHours(2)));
        
        // 部门缓存 - 1小时
        cacheConfigurations.put("deptCache", config.entryTtl(Duration.ofHours(1)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(config)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * Caffeine本地缓存管理器
     */
    @Bean
    @ConditionalOnProperty(name = "jcloud.cache.type", havingValue = "caffeine")
    public CacheManager caffeineCacheManager() {
        log.info("配置Caffeine本地缓存管理器");

        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // 配置Caffeine
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 最大缓存数量
                .maximumSize(1000)
                // 写入后30分钟过期
                .expireAfterWrite(30, TimeUnit.MINUTES)
                // 访问后10分钟过期
                .expireAfterAccess(10, TimeUnit.MINUTES)
                .recordStats()); // 启用统计

        // 设置缓存名称
        cacheManager.setCacheNames(java.util.List.of(
                "userCache", "roleCache", "permissionCache", 
                "tenantCache", "menuCache", "deptCache"
        ));

        return cacheManager;
    }

    /**
     * 缓存专用Redis模板配置
     * 使用GenericJackson2JsonRedisSerializer解决类型转换问题
     * 强制使用Jackson序列化器，避免FastJSON2干扰
     */
    @Bean("cacheRedisTemplate")
    public RedisTemplate<String, Object> cacheRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        log.info("配置缓存专用RedisTemplate - 强制使用Jackson序列化器");

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        // 使用默认的GenericJackson2JsonRedisSerializer，它会自动保存类型信息
        GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用GenericJackson2JsonRedisSerializer
        template.setValueSerializer(jsonRedisSerializer);
        // hash的value序列化方式采用GenericJackson2JsonRedisSerializer
        template.setHashValueSerializer(jsonRedisSerializer);

        template.afterPropertiesSet();
        return template;
    }
}
