package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户统计信息DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "租户统计信息")
public class TenantStatsDTO {
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
    
    /**
     * 用户总数
     */
    @Schema(description = "用户总数", example = "50")
    private Integer userCount;
    
    /**
     * 角色总数
     */
    @Schema(description = "角色总数", example = "5")
    private Integer roleCount;
    
    /**
     * 部门总数
     */
    @Schema(description = "部门总数", example = "10")
    private Integer deptCount;
    
    /**
     * 存储使用量(MB)
     */
    @Schema(description = "存储使用量(MB)", example = "256")
    private Integer storageUsed;
    
    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间", example = "2024-01-15T10:30:00")
    private LocalDateTime lastLoginTime;
    
    /**
     * 活跃用户数（最近30天）
     */
    @Schema(description = "活跃用户数（最近30天）", example = "35")
    private Integer activeUserCount;
    
    /**
     * 今日登录用户数
     */
    @Schema(description = "今日登录用户数", example = "12")
    private Integer todayLoginCount;
    
    /**
     * 本月新增用户数
     */
    @Schema(description = "本月新增用户数", example = "8")
    private Integer monthlyNewUserCount;
    
    /**
     * 数据创建时间
     */
    @Schema(description = "数据创建时间", example = "2024-01-15T10:30:00")
    private LocalDateTime dataTime;
}
