package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 登录日志查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "登录日志查询请求")
public class LoginLogQueryRequest extends PageQuery {
    
    /**
     * 用户账号
     */
    @Schema(description = "用户账号", example = "admin")
    private String userName;
    
    /**
     * 登录IP地址
     */
    @Schema(description = "登录IP地址", example = "***********")
    private String ipaddr;
    
    /**
     * 登录状态（0-成功，1-失败）
     */
    @Schema(description = "登录状态", example = "0")
    private Integer status;
    
    /**
     * 登录时间开始
     */
    @Schema(description = "登录时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime loginTimeStart;
    
    /**
     * 登录时间结束
     */
    @Schema(description = "登录时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime loginTimeEnd;
}
