package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

/**
 * 部门创建请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "部门创建请求")
public class DeptCreateRequest {
    
    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", example = "0")
    @NotNull(message = "父部门ID不能为空")
    private Long parentId;
    
    /**
     * 部门编码
     */
    @Schema(description = "部门编码", example = "TECH")
    @NotBlank(message = "部门编码不能为空")
    @Size(max = 50, message = "部门编码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "部门编码只能包含大写字母、数字和下划线，且必须以大写字母开头")
    private String deptCode;
    
    /**
     * 部门名称
     */
    @Schema(description = "部门名称", example = "技术部")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    private String deptName;
    
    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID", example = "1")
    private Long leaderId;
    
    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "010-12345678")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Pattern(regexp = "^[0-9-+()\\s]*$", message = "联系电话格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "技术研发部门")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
