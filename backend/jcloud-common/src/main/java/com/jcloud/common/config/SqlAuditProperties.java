package com.jcloud.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;

/**
 * SQL审计配置属性类
 * 支持多种存储方式和数据脱敏配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "jcloud.sql-audit")
public class SqlAuditProperties {

    /**
     * 是否启用SQL审计功能
     */
    private boolean enabled = true;

    /**
     * 慢SQL阈值（毫秒）
     */
    private long slowSqlThreshold = 1000L;

    /**
     * 存储方式列表
     */
    private List<String> storageTypes = new ArrayList<>();

    /**
     * 数据脱敏配置
     */
    private DataMasking dataMasking = new DataMasking();

    /**
     * 数据库存储配置
     */
    private Database database = new Database();

    /**
     * 文件存储配置
     */
    private File file = new File();

    /**
     * 性能配置
     */
    private Performance performance = new Performance();

    /**
     * 数据脱敏配置类
     */
    @Data
    public static class DataMasking {
        /**
         * 是否启用数据脱敏
         */
        private boolean enabled = true;

        /**
         * 敏感字段列表（支持正则表达式）
         */
        private List<String> sensitiveFields = new ArrayList<>();
    }

    /**
     * 数据库存储配置类
     */
    @Data
    public static class Database {
        /**
         * 是否异步存储
         */
        private boolean async = true;

        /**
         * 批量大小
         */
        private int batchSize = 100;

        /**
         * 刷新间隔（秒）
         */
        private int flushInterval = 30;

        /**
         * 保留天数（0表示永久保留）
         */
        private int retentionDays = 90;
    }

    /**
     * 文件存储配置类
     */
    @Data
    public static class File {
        /**
         * 日志文件路径
         */
        private String path = "./logs/sql-audit";

        /**
         * 文件名模式
         */
        private String filenamePattern = "sql-audit-%d{yyyy-MM-dd}.log";

        /**
         * 单个文件最大大小
         */
        private String maxFileSize = "100MB";

        /**
         * 最大历史文件数
         */
        private int maxHistory = 30;
    }

    /**
     * 性能配置类
     */
    @Data
    public static class Performance {
        /**
         * 是否启用性能监控
         */
        private boolean monitorEnabled = true;

        /**
         * 缓冲区大小
         */
        private int bufferSize = 1000;

        /**
         * 最大等待时间（毫秒）
         */
        private long maxWaitTime = 5000L;
    }

    /**
     * 检查是否启用了指定的存储类型
     */
    public boolean isStorageTypeEnabled(String storageType) {
        return enabled && storageTypes.contains(storageType);
    }

    /**
     * 检查是否启用了数据库存储
     */
    public boolean isDatabaseStorageEnabled() {
        return isStorageTypeEnabled("database");
    }

    /**
     * 检查是否启用了文件存储
     */
    public boolean isFileStorageEnabled() {
        return isStorageTypeEnabled("file");
    }

    /**
     * 检查是否启用了控制台存储
     */
    public boolean isConsoleStorageEnabled() {
        return isStorageTypeEnabled("console");
    }
}
