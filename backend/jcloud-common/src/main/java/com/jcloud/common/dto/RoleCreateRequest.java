package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 角色创建请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "角色创建请求")
public class RoleCreateRequest {
    
    /**
     * 角色编码
     */
    @Schema(description = "角色编码", example = "ADMIN")
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "角色编码只能包含大写字母、数字和下划线，且必须以大写字母开头")
    private String roleCode;
    
    /**
     * 角色名称
     */
    @Schema(description = "角色名称", example = "管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;
    
    /**
     * 角色类型（SYSTEM-系统角色，CUSTOM-自定义角色）
     */
    @Schema(description = "角色类型", example = "CUSTOM")
    @NotBlank(message = "角色类型不能为空")
    @Pattern(regexp = "^(SYSTEM|CUSTOM)$", message = "角色类型只能是SYSTEM或CUSTOM")
    private String roleType;
    
    /**
     * 数据权限范围（ALL-全部，DEPT-部门，DEPT_AND_SUB-部门及子部门，SELF-仅本人，CUSTOM-自定义）
     */
    @Schema(description = "数据权限范围", example = "SELF")
    @NotBlank(message = "数据权限范围不能为空")
    @Pattern(regexp = "^(ALL|DEPT|DEPT_AND_SUB|SELF|CUSTOM)$", message = "数据权限范围值不正确")
    private String dataScope;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;
    
    /**
     * 权限ID列表
     */
    @Schema(description = "权限ID列表", example = "[1, 2, 3]")
    private List<Long> permissionIds;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "系统管理员角色")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
