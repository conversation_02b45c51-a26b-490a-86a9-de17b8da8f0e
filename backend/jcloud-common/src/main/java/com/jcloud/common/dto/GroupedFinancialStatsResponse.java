package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

/**
 * 分组财务统计数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "分组财务统计数据")
public class GroupedFinancialStatsResponse {
    
    /**
     * 用户相关统计
     */
    @Schema(description = "用户相关统计")
    private List<FinancialStatsResponse> userStats;
    
    /**
     * 主播相关统计
     */
    @Schema(description = "主播相关统计")
    private List<FinancialStatsResponse> anchorStats;
    
    /**
     * 合计统计
     */
    @Schema(description = "合计统计")
    private List<FinancialStatsResponse> totalStats;
    
    /**
     * 其他业务统计
     */
    @Schema(description = "其他业务统计")
    private List<FinancialStatsResponse> businessStats;
}