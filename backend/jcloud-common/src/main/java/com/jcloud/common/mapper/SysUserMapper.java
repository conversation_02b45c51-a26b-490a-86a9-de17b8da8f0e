package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysUser;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据用户名查询用户（包含租户过滤）
     * 
     * @param username 用户名
     * @param tenantId 租户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND tenant_id = #{tenantId} AND deleted = 0")
    SysUser selectByUsername(@Param("username") String username, @Param("tenantId") Long tenantId);
    
    /**
     * 根据邮箱查询用户（包含租户过滤）
     * 
     * @param email 邮箱
     * @param tenantId 租户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND tenant_id = #{tenantId} AND deleted = 0")
    SysUser selectByEmail(@Param("email") String email, @Param("tenantId") Long tenantId);
    
    /**
     * 根据手机号查询用户（包含租户过滤）
     *
     * @param phone 手机号
     * @param tenantId 租户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND tenant_id = #{tenantId} AND deleted = 0")
    SysUser selectByPhone(@Param("phone") String phone, @Param("tenantId") Long tenantId);

    /**
     * 分页查询用户列表（包含关联信息）
     * 
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 用户分页列表
     */
    Page<SysUser> selectUserPageWithDept(Page<SysUser> page, @Param("ew") QueryWrapper queryWrapper);
    
    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM sys_user u " +
            "INNER JOIN sys_user_dept ud ON u.id = ud.user_id " +
            "WHERE ud.dept_id = #{deptId} AND u.tenant_id = #{tenantId} AND u.deleted = 0")
    List<SysUser> selectByDeptId(@Param("deptId") Long deptId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM sys_user u " +
            "INNER JOIN sys_user_role ur ON u.id = ur.user_id " +
            "WHERE ur.role_id = #{roleId} AND u.tenant_id = #{tenantId} AND u.deleted = 0")
    List<SysUser> selectByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
    
    /**
     * 更新用户最后登录信息
     * 
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET last_login_time = #{loginTime}, last_login_ip = #{loginIp} " +
            "WHERE id = #{userId}")
    int updateLastLoginInfo(@Param("userId") Long userId, 
                           @Param("loginTime") String loginTime, 
                           @Param("loginIp") String loginIp);
    
    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @param tenantId 租户ID
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET password = #{newPassword}, password_update_time = NOW() " +
            "WHERE id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0")
    int resetPassword(@Param("userId") Long userId, 
                     @Param("newPassword") String newPassword, 
                     @Param("tenantId") Long tenantId);
    
    /**
     * 批量更新用户状态
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @param tenantId 租户ID
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE sys_user SET status = #{status} " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 AND id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int updateStatusBatch(@Param("userIds") List<Long> userIds, 
                         @Param("status") Integer status, 
                         @Param("tenantId") Long tenantId);
}
