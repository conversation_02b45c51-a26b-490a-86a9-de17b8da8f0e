package com.jcloud.common.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具类
 * 处理时间戳与LocalDateTime之间的转换
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TimeUtil {
    
    /**
     * 默认时间格式
     */
    public static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 默认时区
     */
    public static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();
    
    /**
     * 默认格式化器
     */
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_PATTERN);
    
    /**
     * 获取当前时间戳（秒）
     * 
     * @return 当前时间戳
     */
    public static Long now() {
        return Instant.now().getEpochSecond();
    }
    
    /**
     * 获取当前时间戳（毫秒）
     * 
     * @return 当前时间戳（毫秒）
     */
    public static Long nowMillis() {
        return Instant.now().toEpochMilli();
    }
    
    /**
     * 时间戳转LocalDateTime
     * 
     * @param timestamp 时间戳（秒）
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), DEFAULT_ZONE);
    }
    
    /**
     * LocalDateTime转时间戳
     * 
     * @param localDateTime LocalDateTime
     * @return 时间戳（秒）
     */
    public static Long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atZone(DEFAULT_ZONE).toEpochSecond();
    }
    
    /**
     * 时间戳格式化为字符串
     * 
     * @param timestamp 时间戳（秒）
     * @return 格式化后的时间字符串
     */
    public static String format(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return toLocalDateTime(timestamp).format(DEFAULT_FORMATTER);
    }
    
    /**
     * 时间戳格式化为字符串
     * 
     * @param timestamp 时间戳（秒）
     * @param pattern 格式模式
     * @return 格式化后的时间字符串
     */
    public static String format(Long timestamp, String pattern) {
        if (timestamp == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return toLocalDateTime(timestamp).format(formatter);
    }
    
    /**
     * 字符串解析为时间戳
     * 
     * @param timeStr 时间字符串
     * @return 时间戳（秒）
     */
    public static Long parse(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, DEFAULT_FORMATTER);
        return toTimestamp(localDateTime);
    }
    
    /**
     * 字符串解析为时间戳
     * 
     * @param timeStr 时间字符串
     * @param pattern 格式模式
     * @return 时间戳（秒）
     */
    public static Long parse(String timeStr, String pattern) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
        return toTimestamp(localDateTime);
    }
    
    /**
     * 判断时间戳是否为今天
     * 
     * @param timestamp 时间戳（秒）
     * @return 是否为今天
     */
    public static boolean isToday(Long timestamp) {
        if (timestamp == null) {
            return false;
        }
        LocalDateTime dateTime = toLocalDateTime(timestamp);
        LocalDateTime now = LocalDateTime.now();
        return dateTime.toLocalDate().equals(now.toLocalDate());
    }
    
    /**
     * 计算两个时间戳之间的天数差
     *
     * @param startTimestamp 开始时间戳（秒）
     * @param endTimestamp 结束时间戳（秒）
     * @return 天数差
     */
    public static long daysBetween(Long startTimestamp, Long endTimestamp) {
        if (startTimestamp == null || endTimestamp == null) {
            return 0;
        }
        LocalDateTime start = toLocalDateTime(startTimestamp);
        LocalDateTime end = toLocalDateTime(endTimestamp);
        return java.time.Duration.between(start.toLocalDate().atStartOfDay(),
                                        end.toLocalDate().atStartOfDay()).toDays();
    }

    /**
     * 获取今天开始时间戳（00:00:00）
     *
     * @return 今天开始时间戳
     */
    public static Long getTodayStart() {
        LocalDateTime today = LocalDateTime.now().toLocalDate().atStartOfDay();
        return toTimestamp(today);
    }

    /**
     * 获取今天结束时间戳（23:59:59）
     *
     * @return 今天结束时间戳
     */
    public static Long getTodayEnd() {
        LocalDateTime today = LocalDateTime.now().toLocalDate().atTime(23, 59, 59);
        return toTimestamp(today);
    }

    /**
     * 获取指定日期的开始时间戳（00:00:00）
     *
     * @param dateStr 日期字符串（yyyy-MM-dd）
     * @return 指定日期开始时间戳
     */
    public static Long getDayStart(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr + " 00:00:00", DEFAULT_FORMATTER);
            return toTimestamp(dateTime);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取指定日期的结束时间戳（23:59:59）
     *
     * @param dateStr 日期字符串（yyyy-MM-dd）
     * @return 指定日期结束时间戳
     */
    public static Long getDayEnd(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr + " 23:59:59", DEFAULT_FORMATTER);
            return toTimestamp(dateTime);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 时间戳转日期字符串（yyyy-MM-dd）
     *
     * @param timestamp 时间戳（秒）
     * @return 日期字符串
     */
    public static String formatDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return format(timestamp, "yyyy-MM-dd");
    }

    /**
     * 时间戳转时间字符串（HH:mm:ss）
     *
     * @param timestamp 时间戳（秒）
     * @return 时间字符串
     */
    public static String formatTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return format(timestamp, "HH:mm:ss");
    }
}
