package com.jcloud.common.annotation;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperLog {
    
    /**
     * 操作模块
     */
    String title() default "";
    
    /**
     * 业务类型
     * 0-其它，1-新增，2-修改，3-删除，4-授权，5-导出，6-导入，7-强退，8-生成代码，9-清空数据
     */
    int businessType() default 0;
    
    /**
     * 操作类别
     * 0-其它，1-后台用户，2-手机端用户
     */
    int operatorType() default 1;
    
    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;
    
    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;
    
    /**
     * 排除指定的请求参数
     */
    String[] excludeParamNames() default {};
}
