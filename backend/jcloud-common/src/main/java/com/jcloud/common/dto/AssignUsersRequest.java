package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 分配用户到租户请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "分配用户到租户请求")
public class AssignUsersRequest {
    
    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true)
    private Long tenantId;
    
    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @Schema(description = "用户ID列表", required = true)
    private List<Long> userIds;
    
    /**
     * 用户角色（1-管理员，2-普通用户）
     */
    @Schema(description = "用户角色", example = "2")
    private Integer userRole = 2;
}
