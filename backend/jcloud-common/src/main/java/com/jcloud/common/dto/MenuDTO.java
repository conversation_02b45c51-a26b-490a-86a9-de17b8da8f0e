package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 菜单DTO
 * 用于前端菜单渲染
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "菜单信息")
public class MenuDTO {
    
    /**
     * 菜单ID
     */
    @Schema(description = "菜单ID")
    private Long id;
    
    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID")
    private Long parentId;
    
    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称")
    private String name;
    
    /**
     * 菜单标题
     */
    @Schema(description = "菜单标题")
    private String title;
    
    /**
     * 路由路径
     */
    @Schema(description = "路由路径")
    private String path;
    
    /**
     * 组件路径
     */
    @Schema(description = "组件路径")
    private String component;
    
    /**
     * 菜单图标
     */
    @Schema(description = "菜单图标")
    private String icon;
    
    /**
     * 权限标识
     */
    @Schema(description = "权限标识")
    private String permission;
    
    /**
     * 菜单类型（0-目录，1-菜单，2-按钮）
     */
    @Schema(description = "菜单类型")
    private Integer type;
    
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
    
    /**
     * 是否显示（0-隐藏，1-显示）
     */
    @Schema(description = "是否显示")
    private Boolean visible;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Boolean enabled;
    
    /**
     * 子菜单列表
     */
    @Schema(description = "子菜单列表")
    private List<MenuDTO> children;
}
