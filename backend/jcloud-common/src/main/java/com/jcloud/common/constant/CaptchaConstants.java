package com.jcloud.common.constant;

/**
 * 验证码常量类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CaptchaConstants {
    
    /**
     * 验证码缓存Key前缀
     */
    String CAPTCHA_CACHE_PREFIX = "captcha:";
    
    /**
     * 验证码类型 - 数字
     */
    String TYPE_NUMERIC = "NUMERIC";
    
    /**
     * 验证码类型 - 字母
     */
    String TYPE_ALPHABETIC = "ALPHABETIC";
    
    /**
     * 验证码类型 - 数字字母混合
     */
    String TYPE_ALPHANUMERIC = "ALPHANUMERIC";
    
    /**
     * 验证码类型 - 算术
     */
    String TYPE_ARITHMETIC = "ARITHMETIC";
    
    /**
     * 默认验证码长度
     */
    Integer DEFAULT_LENGTH = 4;
    
    /**
     * 默认验证码有效期（秒）
     */
    Integer DEFAULT_EXPIRE_TIME = 300;
    
    /**
     * 默认图片宽度
     */
    Integer DEFAULT_WIDTH = 120;
    
    /**
     * 默认图片高度
     */
    Integer DEFAULT_HEIGHT = 40;
    
    /**
     * 数字字符集
     */
    String NUMERIC_CHARS = "0123456789";
    
    /**
     * 字母字符集
     */
    String ALPHABETIC_CHARS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz";
    
    /**
     * 数字字母混合字符集
     */
    String ALPHANUMERIC_CHARS = "0123456789ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz";
    
    /**
     * 算术运算符
     */
    String[] ARITHMETIC_OPERATORS = {"+", "-", "*"};
    
    /**
     * 默认字体名称
     */
    String DEFAULT_FONT_NAME = "Arial";
    
    /**
     * 默认字体大小
     */
    Integer DEFAULT_FONT_SIZE = 25;
    
    /**
     * 默认干扰线数量
     */
    Integer DEFAULT_LINE_COUNT = 5;
    
    /**
     * 默认噪点数量
     */
    Integer DEFAULT_NOISE_COUNT = 50;
    
    /**
     * 验证码图片格式
     */
    String IMAGE_FORMAT = "png";
    
    /**
     * Base64图片前缀
     */
    String BASE64_IMAGE_PREFIX = "data:image/png;base64,";
    
    /**
     * 验证码验证失败最大次数
     */
    Integer MAX_VERIFY_ATTEMPTS = 5;
    
    /**
     * 验证码验证失败锁定时间（秒）
     */
    Integer LOCK_TIME = 600;
    
    /**
     * 验证码验证失败计数缓存Key前缀
     */
    String VERIFY_FAIL_COUNT_PREFIX = "captcha:fail:";
}
