package com.jcloud.common.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;


/**
 * 租户用户关联实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table("sys_tenant_user")
@Schema(description = "租户用户关联信息")
public class SysTenantUser {
    
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 用户在租户中的角色（1-管理员，2-普通用户）
     */
    @Schema(description = "用户角色")
    private Integer userRole;
    
    /**
     * 分配时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()")
    @Schema(description = "分配时间")
    private Long assignTime;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 创建时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()")
    @Schema(description = "创建时间")
    private Long createTime;
    
    /**
     * 更新时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()", onUpdateValue = "unix_timestamp()")
    @Schema(description = "更新时间")
    private Long updateTime;
    
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createBy;
    
    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateBy;
    
    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    @Column(isLogicDelete = true)
    @Schema(description = "删除标识")
    private Integer deleted = 0;
    
    /**
     * 版本号（乐观锁）
     */
    @Column(version = true)
    @Schema(description = "版本号")
    private Integer version = 0;
}
