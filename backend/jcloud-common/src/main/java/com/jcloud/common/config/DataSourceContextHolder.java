package com.jcloud.common.config;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据源上下文持有者
 * 使用ThreadLocal保存当前线程的数据源标识
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class DataSourceContextHolder {
    
    /**
     * 数据源名称常量
     */
    public static final String MASTER = "master";
    public static final String SLAVE = "slave";
    
    /**
     * 使用ThreadLocal保存数据源标识
     */
    private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置数据源
     * 
     * @param dataSource 数据源名称
     */
    public static void setDataSource(String dataSource) {
        log.debug("切换数据源到: {}", dataSource);
        CONTEXT_HOLDER.set(dataSource);
    }
    
    /**
     * 获取当前数据源
     * 
     * @return 数据源名称，默认为master
     */
    public static String getDataSource() {
        String dataSource = CONTEXT_HOLDER.get();
        return dataSource != null ? dataSource : MASTER;
    }
    
    /**
     * 清除数据源设置
     */
    public static void clearDataSource() {
        CONTEXT_HOLDER.remove();
        log.debug("清除数据源设置");
    }
}