package com.jcloud.common.service;

import com.jcloud.common.entity.SysSqlAuditLog;
import com.mybatisflex.core.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * SQL审计日志服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SqlAuditLogService extends IService<SysSqlAuditLog> {

    /**
     * 批量保存审计日志
     *
     * @param logs 审计日志列表
     * @return 保存成功的记录数
     */
    int batchSave(List<SysSqlAuditLog> logs);

    /**
     * 清理过期的审计日志
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredLogs(int retentionDays);

    /**
     * 获取SQL执行统计信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    Map<String, Object> getExecutionStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取慢SQL统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 慢SQL统计信息
     */
    Map<String, Object> getSlowSqlStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最耗时的SQL列表
     *
     * @param limit 限制数量
     * @return 最耗时的SQL列表
     */
    List<SysSqlAuditLog> getTopSlowSql(int limit);

    /**
     * 按SQL类型统计执行情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return SQL类型统计结果
     */
    List<Map<String, Object>> getStatsBySqlType(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按用户统计SQL执行情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户统计结果
     */
    List<Map<String, Object>> getStatsByUser(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按数据源统计SQL执行情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据源统计结果
     */
    List<Map<String, Object>> getStatsByDataSource(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取SQL执行趋势
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行趋势数据
     */
    List<Map<String, Object>> getExecutionTrend(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取审计日志概览信息
     *
     * @return 概览信息
     */
    Map<String, Object> getAuditOverview();
}
