package com.jcloud.common.audit;

import com.jcloud.common.config.SqlAuditProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 数据脱敏处理器
 * 对SQL参数中的敏感信息进行脱敏处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataMaskingProcessor {

    private final SqlAuditProperties sqlAuditProperties;

    /**
     * 对SQL参数进行脱敏处理
     *
     * @param sql  SQL语句
     * @param args SQL参数
     * @return 脱敏后的参数数组
     */
    public Object[] maskSensitiveData(String sql, Object[] args) {
        if (!sqlAuditProperties.getDataMasking().isEnabled() || args == null || args.length == 0) {
            return args;
        }

        try {
            Object[] maskedArgs = new Object[args.length];
            List<String> sensitiveFields = sqlAuditProperties.getDataMasking().getSensitiveFields();

            for (int i = 0; i < args.length; i++) {
                maskedArgs[i] = maskParameter(args[i], sql, sensitiveFields);
            }

            return maskedArgs;
        } catch (Exception e) {
            log.warn("数据脱敏处理异常，返回原始参数", e);
            return args;
        }
    }

    /**
     * 对单个参数进行脱敏处理
     */
    private Object maskParameter(Object param, String sql, List<String> sensitiveFields) {
        if (param == null) {
            return null;
        }

        String paramStr = param.toString();
        
        // 检查SQL语句中是否包含敏感字段
        if (containsSensitiveField(sql, sensitiveFields)) {
            return maskSensitiveValue(paramStr);
        }

        // 根据参数值的特征进行脱敏
        return maskByValuePattern(paramStr);
    }

    /**
     * 检查SQL语句是否包含敏感字段
     */
    private boolean containsSensitiveField(String sql, List<String> sensitiveFields) {
        if (sql == null || sensitiveFields == null || sensitiveFields.isEmpty()) {
            return false;
        }

        String lowerSql = sql.toLowerCase();
        return sensitiveFields.stream()
                .anyMatch(field -> lowerSql.contains(field.toLowerCase()));
    }

    /**
     * 根据值的模式进行脱敏
     */
    private Object maskByValuePattern(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }

        // 手机号脱敏：138****5678
        if (isMobileNumber(value)) {
            return maskMobileNumber(value);
        }

        // 邮箱脱敏：u***@example.com
        if (isEmail(value)) {
            return maskEmail(value);
        }

        // 身份证号脱敏：1234***5678
        if (isIdCard(value)) {
            return maskIdCard(value);
        }

        // 银行卡号脱敏：1234 **** **** 5678
        if (isBankCard(value)) {
            return maskBankCard(value);
        }

        // 密码相关脱敏：完全隐藏
        if (isPassword(value)) {
            return "***";
        }

        return value;
    }

    /**
     * 对敏感值进行脱敏
     */
    private String maskSensitiveValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }

        // 如果是明显的密码，完全脱敏
        if (isPassword(value)) {
            return "***";
        }

        // 其他敏感信息，保留部分字符
        if (value.length() <= 3) {
            return "***";
        } else if (value.length() <= 6) {
            return value.charAt(0) + "***";
        } else {
            return value.substring(0, 2) + "***" + value.substring(value.length() - 2);
        }
    }

    /**
     * 判断是否为手机号
     */
    private boolean isMobileNumber(String value) {
        return Pattern.matches("^1[3-9]\\d{9}$", value);
    }

    /**
     * 脱敏手机号
     */
    private String maskMobileNumber(String mobile) {
        if (mobile.length() == 11) {
            return mobile.substring(0, 3) + "****" + mobile.substring(7);
        }
        return mobile;
    }

    /**
     * 判断是否为邮箱
     */
    private boolean isEmail(String value) {
        return Pattern.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$", value);
    }

    /**
     * 脱敏邮箱
     */
    private String maskEmail(String email) {
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            String username = email.substring(0, atIndex);
            String domain = email.substring(atIndex);
            
            if (username.length() <= 1) {
                return "***" + domain;
            } else if (username.length() <= 3) {
                return username.charAt(0) + "***" + domain;
            } else {
                return username.charAt(0) + "***" + username.charAt(username.length() - 1) + domain;
            }
        }
        return email;
    }

    /**
     * 判断是否为身份证号
     */
    private boolean isIdCard(String value) {
        return Pattern.matches("^\\d{15}$|^\\d{17}[\\dXx]$", value);
    }

    /**
     * 脱敏身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard.length() == 15) {
            return idCard.substring(0, 4) + "*******" + idCard.substring(11);
        } else if (idCard.length() == 18) {
            return idCard.substring(0, 4) + "**********" + idCard.substring(14);
        }
        return idCard;
    }

    /**
     * 判断是否为银行卡号
     */
    private boolean isBankCard(String value) {
        return Pattern.matches("^\\d{16,19}$", value);
    }

    /**
     * 脱敏银行卡号
     */
    private String maskBankCard(String bankCard) {
        if (bankCard.length() >= 8) {
            return bankCard.substring(0, 4) + " **** **** " + bankCard.substring(bankCard.length() - 4);
        }
        return bankCard;
    }

    /**
     * 判断是否为密码
     */
    private boolean isPassword(String value) {
        // 简单的密码判断逻辑，可以根据实际需求调整
        return value.length() >= 6 && value.length() <= 50 && 
               (value.matches(".*[a-zA-Z].*") || value.matches(".*\\d.*"));
    }
}
