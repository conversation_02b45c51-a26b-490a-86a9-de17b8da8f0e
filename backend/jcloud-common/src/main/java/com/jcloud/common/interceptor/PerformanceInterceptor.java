package com.jcloud.common.interceptor;

import com.jcloud.common.util.PerformanceMonitor;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 性能监控拦截器
 * 自动监控所有API请求的响应时间
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PerformanceInterceptor implements HandlerInterceptor {

    private final PerformanceMonitor performanceMonitor;
    
    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final String ENDPOINT_ATTRIBUTE = "endpoint";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String endpoint = getEndpoint(request);
        long startTime = performanceMonitor.startRequest(endpoint);
        
        // 将开始时间和端点信息存储到请求属性中
        request.setAttribute(START_TIME_ATTRIBUTE, startTime);
        request.setAttribute(ENDPOINT_ATTRIBUTE, endpoint);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                              Object handler, Exception ex) {
        Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
        String endpoint = (String) request.getAttribute(ENDPOINT_ATTRIBUTE);
        
        if (startTime != null && endpoint != null) {
            performanceMonitor.endRequest(endpoint, startTime);
            
            // 如果有异常，记录错误
            if (ex != null || response.getStatus() >= 400) {
                performanceMonitor.recordError(endpoint);
            }
        }
    }

    /**
     * 获取端点标识
     */
    private String getEndpoint(HttpServletRequest request) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        
        // 移除上下文路径
        String contextPath = request.getContextPath();
        if (uri.startsWith(contextPath)) {
            uri = uri.substring(contextPath.length());
        }
        
        // 简化路径参数（将数字ID替换为{id}）
        uri = uri.replaceAll("/\\d+", "/{id}");
        
        return method + " " + uri;
    }
}
