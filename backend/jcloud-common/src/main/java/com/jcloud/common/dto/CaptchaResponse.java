package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "验证码响应")
public class CaptchaResponse {
    
    /**
     * 验证码标识
     */
    @Schema(description = "验证码标识", example = "uuid-123456")
    private String captchaId;
    
    /**
     * 验证码图片Base64编码
     */
    @Schema(description = "验证码图片Base64编码")
    private String captchaImage;
    
    /**
     * 验证码有效期（秒）
     */
    @Schema(description = "验证码有效期（秒）", example = "300")
    private Integer expireTime;
    
    /**
     * 验证码类型
     */
    @Schema(description = "验证码类型", example = "ALPHANUMERIC")
    private String captchaType;
}
