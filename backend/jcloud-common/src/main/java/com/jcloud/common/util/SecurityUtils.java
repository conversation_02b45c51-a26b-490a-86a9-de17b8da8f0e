package com.jcloud.common.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.common.constant.CommonConstants;

/**
 * 安全工具类
 * 提供用户认证、权限验证、租户信息获取等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecurityUtils {
    /**
     * 获取当前登录用户ID
     */
    public static Long getUserId() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsLong();
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }
    
    /**
     * 获取当前登录用户名
     */
    public static String getUsername() {
        try {
            if (StpUtil.isLogin()) {
                return (String) StpUtil.getSession().get("username");
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }
    
    /**
     * 获取当前用户的租户ID
     */
    public static Long getTenantId() {
        try {
            if (StpUtil.isLogin()) {
                Object tenantId = StpUtil.getSession().get("tenantId");
                if (tenantId != null) {
                    return Long.valueOf(tenantId.toString());
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回默认租户ID
        }
        return CommonConstants.DEFAULT_TENANT_ID;
    }
    
    /**
     * 获取当前用户的部门ID
     */
    public static Long getDeptId() {
        try {
            if (StpUtil.isLogin()) {
                Object deptId = StpUtil.getSession().get("deptId");
                if (deptId != null) {
                    return Long.valueOf(deptId.toString());
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }
    
    /**
     * 获取当前用户的数据权限范围
     */
    public static String getDataScope() {
        try {
            if (StpUtil.isLogin()) {
                return (String) StpUtil.getSession().get("dataScope");
            }
        } catch (Exception e) {
            // 忽略异常，返回最严格的权限
        }
        return "SELF";
    }
    
    /**
     * 判断当前用户是否为超级管理员
     */
    public static boolean isSuperAdmin() {
        try {
            if (StpUtil.isLogin()) {
                Object isAdmin = StpUtil.getSession().get("isAdmin");
                if (isAdmin != null) {
                    return Boolean.parseBoolean(isAdmin.toString());
                }
                
                // 也可以通过角色判断
                return StpUtil.hasRole(CommonConstants.SUPER_ADMIN_ROLE);
            }
        } catch (Exception e) {
            // 忽略异常，返回false
        }
        return false;
    }
    
    /**
     * 判断当前用户是否为管理员
     */
    public static boolean isAdmin() {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.hasRole(CommonConstants.ADMIN_ROLE) || isSuperAdmin();
            }
        } catch (Exception e) {
            // 忽略异常，返回false
        }
        return false;
    }
    
    /**
     * 检查当前用户是否有指定权限
     */
    public static boolean hasPermission(String permission) {
        try {
            if (StpUtil.isLogin()) {
                // 超级管理员拥有所有权限
                if (isSuperAdmin()) {
                    return true;
                }
                return StpUtil.hasPermission(permission);
            }
        } catch (Exception e) {
            // 忽略异常，返回false
        }
        return false;
    }
    
    /**
     * 检查当前用户是否有指定角色
     */
    public static boolean hasRole(String role) {
        try {
            if (StpUtil.isLogin()) {
                return StpUtil.hasRole(role);
            }
        } catch (Exception e) {
            // 忽略异常，返回false
        }
        return false;
    }
    
    /**
     * 检查当前用户是否有任意一个指定权限
     */
    public static boolean hasAnyPermission(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        
        for (String permission : permissions) {
            if (hasPermission(permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查当前用户是否有任意一个指定角色
     */
    public static boolean hasAnyRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }
        
        for (String role : roles) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查当前用户是否可以访问指定租户的数据
     */
    public static boolean canAccessTenant(Long targetTenantId) {
        // 超级管理员可以访问所有租户数据
        if (isSuperAdmin()) {
            return true;
        }
        
        // 普通用户只能访问自己租户的数据
        Long currentTenantId = getTenantId();
        return currentTenantId != null && currentTenantId.equals(targetTenantId);
    }
    
    /**
     * 获取当前用户的数据权限范围描述
     *
     * @return 权限范围描述
     */
    public static String getDataScopeDescription() {
        if (isSuperAdmin()) {
            return "超级管理员（全部数据）";
        }

        String dataScope = getDataScope();
        switch (dataScope) {
            case "ALL":
                return "全部数据权限";
            case "DEPT":
                return "本部门数据权限";
            case "DEPT_AND_SUB":
                return "本部门及子部门数据权限";
            case "SELF":
                return "仅本人数据权限";
            case "CUSTOM":
                return "自定义数据权限";
            default:
                return "未知权限范围";
        }
    }

    // 注意：数据权限检查现在由MyBatis-Flex的JCloudDataPermissionDialect自动处理
    // 不再需要手动实现canAccessUser和canAccessDept方法
}
