package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 租户查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "租户查询请求")
public class TenantQueryRequest extends PageQuery {
    
    /**
     * 搜索关键词（同时搜索租户编码和租户名称）
     */
    @Schema(description = "搜索关键词", example = "示例")
    private String keyword;
    
    /**
     * 租户编码
     */
    @Schema(description = "租户编码", example = "TENANT_001")
    private String tenantCode;
    
    /**
     * 租户名称
     */
    @Schema(description = "租户名称", example = "示例租户")
    private String tenantName;
    
    /**
     * 联系人
     */
    @Schema(description = "联系人", example = "张三")
    private String contactName;
    
    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;
    
    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;
    
    /**
     * 租户状态（0-禁用 1-启用）
     */
    @Schema(description = "租户状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;
    
    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime createTimeEnd;
    
    /**
     * 是否过期（true-已过期 false-未过期）
     */
    @Schema(description = "是否过期", example = "false")
    private Boolean expired;
    
    /**
     * 用户数量限制最小值
     */
    @Schema(description = "用户数量限制最小值", example = "10")
    private Integer userLimitMin;
    
    /**
     * 用户数量限制最大值
     */
    @Schema(description = "用户数量限制最大值", example = "1000")
    private Integer userLimitMax;
    
    /**
     * 存储空间限制最小值(MB)
     */
    @Schema(description = "存储空间限制最小值(MB)", example = "100")
    private Integer storageLimitMin;
    
    /**
     * 存储空间限制最大值(MB)
     */
    @Schema(description = "存储空间限制最大值(MB)", example = "10240")
    private Integer storageLimitMax;
}
