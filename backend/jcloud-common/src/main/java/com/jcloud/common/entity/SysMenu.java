package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * 菜单实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_menu")
@Schema(description = "菜单信息")
public class SysMenu extends BaseEntity {
    
    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID")
    private Long parentId;
    
    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称")
    private String menuName;
    
    /**
     * 菜单类型（0-目录，1-菜单，2-按钮）
     */
    @Schema(description = "菜单类型")
    private Integer menuType;
    
    /**
     * 路由路径
     */
    @Schema(description = "路由路径")
    private String path;
    
    /**
     * 组件路径
     */
    @Schema(description = "组件路径")
    private String component;
    
    /**
     * 菜单图标
     */
    @Schema(description = "菜单图标")
    private String icon;
    
    /**
     * 权限标识
     */
    @Schema(description = "权限标识")
    private String permissionCode;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 是否显示（0-隐藏，1-显示）
     */
    @Schema(description = "是否显示")
    private Integer visible;
    
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 子菜单列表（用于树形结构，不存储到数据库）
     */
    @Schema(description = "子菜单列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SysMenu> children;
}
