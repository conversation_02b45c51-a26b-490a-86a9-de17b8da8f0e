package com.jcloud.common.page;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 分页结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "分页结果")
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    @Schema(description = "数据列表")
    private List<T> records;
    
    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Long total;
    
    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Integer pageSize;
    
    /**
     * 总页数
     */
    @Schema(description = "总页数")
    private Integer totalPages;
    
    /**
     * 是否有下一页
     */
    @Schema(description = "是否有下一页")
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;
    
    /**
     * 构造分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer pageNum, Integer pageSize) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setRecords(records);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        
        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / pageSize);
        pageResult.setTotalPages(totalPages);
        
        // 计算是否有上一页和下一页
        pageResult.setHasPrevious(pageNum > 1);
        pageResult.setHasNext(pageNum < totalPages);
        
        return pageResult;
    }
    
    /**
     * 空分页结果
     */
    public static <T> PageResult<T> empty() {
        return of(List.of(), 0L, 1, 10);
    }
}
