package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 验证码验证请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "验证码验证请求")
public class CaptchaRequest {
    
    /**
     * 验证码标识
     */
    @Schema(description = "验证码标识", example = "uuid-123456")
    @NotBlank(message = "验证码标识不能为空")
    private String captchaId;
    
    /**
     * 验证码值
     */
    @Schema(description = "验证码值", example = "1234")
    @NotBlank(message = "验证码不能为空")
    private String captchaCode;
}
