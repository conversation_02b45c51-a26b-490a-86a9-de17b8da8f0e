package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysRoleMenu;
import com.jcloud.common.entity.SysMenu;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {
    
    /**
     * 根据角色ID删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deleteByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据菜单ID删除角色菜单关联
     * 
     * @param menuId 菜单ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deleteByMenuId(@Param("menuId") Long menuId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据角色ID获取菜单列表
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据角色ID获取菜单ID列表
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据用户ID获取菜单列表（通过角色关联）
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    /**
     * 批量插入角色菜单关联
     * 
     * @param roleMenus 角色菜单关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("roleMenus") List<SysRoleMenu> roleMenus);
}
