package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户配置DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "租户配置信息")
public class TenantConfigDTO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 配置键
     */
    @Schema(description = "配置键")
    private String configKey;
    
    /**
     * 配置值
     */
    @Schema(description = "配置值")
    private String configValue;
    
    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String configName;
    
    /**
     * 配置描述
     */
    @Schema(description = "配置描述")
    private String configDesc;
    
    /**
     * 配置类型（1-系统配置，0-自定义配置）
     */
    @Schema(description = "配置类型")
    private Integer configType;
    
    /**
     * 是否系统配置（1-是，0-否）
     */
    @Schema(description = "是否系统配置")
    private Integer isSystem;
    
    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sortOrder;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
