package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * vim_user表实体类（从库）
 * 用于同步主播用户数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table("vim_user")
@Schema(description = "vim_user用户信息")
public class VimUser {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer id;
    
    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    
    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;
    
    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String userimage;
    
    /**
     * 最后一次登录时间（时间戳）
     */
    @Schema(description = "最后一次登录时间")
    private Integer lastLoginTime;
    
    /**
     * 最后一次登录IP
     */
    @Schema(description = "最后一次登录IP")
    private String lastLoginIp;
    
    /**
     * 当前货币
     */
    @Schema(description = "当前货币")
    private BigDecimal coin;
    
    /**
     * 当前钥匙
     */
    @Schema(description = "当前钥匙")
    private BigDecimal key;
    
    /**
     * 自动锻造钥匙（0：关闭，1：开启）
     */
    @Schema(description = "自动锻造钥匙")
    private Integer autokey;
    
    /**
     * 邀请码
     */
    @Schema(description = "邀请码")
    private String inviteCode;
    
    /**
     * 当前绑定的邀请用户
     */
    @Schema(description = "当前绑定的邀请用户")
    private Integer inviteUser;
    
    /**
     * 创建时间（时间戳）
     */
    @Schema(description = "创建时间")
    private Integer createTime;
    
    /**
     * Steam ID
     */
    @Schema(description = "Steam ID")
    private String steamId;
    
    /**
     * 提货链接
     */
    @Schema(description = "提货链接")
    private String steamLink;
    
    /**
     * 经验
     */
    @Schema(description = "经验")
    private BigDecimal exp;
    
    /**
     * 等级
     */
    @Schema(description = "等级")
    private Integer level;
    
    /**
     * 客户端种子
     */
    @Schema(description = "客户端种子")
    private String seed;
    
    /**
     * 身份（1：用户，2：线上主播，3：线下主播）
     */
    @Schema(description = "身份")
    private Integer identity;
    
    /**
     * 是否实名认证（0：未实名，1：已实名）
     */
    @Schema(description = "是否实名认证")
    private Integer isauth;
    
    /**
     * 账号状态（1：正常，2：禁用）
     */
    @Schema(description = "账号状态")
    private Integer state;
    
    /**
     * 优先支付通道
     */
    @Schema(description = "优先支付通道")
    private Integer pid;
}
