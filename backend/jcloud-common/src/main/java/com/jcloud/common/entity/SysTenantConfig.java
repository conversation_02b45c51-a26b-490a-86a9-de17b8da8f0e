package com.jcloud.common.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;




/**
 * 租户配置实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table("sys_tenant_config")
@Schema(description = "租户配置信息")
public class SysTenantConfig {
    
    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 配置键
     */
    @Schema(description = "配置键")
    private String configKey;
    
    /**
     * 配置值
     */
    @Schema(description = "配置值")
    private String configValue;
    
    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String configName;
    
    /**
     * 配置描述
     */
    @Schema(description = "配置描述")
    private String configDesc;
    
    /**
     * 配置类型（1-系统配置，0-自定义配置）
     */
    @Schema(description = "配置类型")
    private Integer configType;
    
    /**
     * 是否系统配置（1-是，0-否）
     */
    @Schema(description = "是否系统配置")
    private Integer isSystem;
    
    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sortOrder;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 创建时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()")
    @Schema(description = "创建时间")
    private Long createTime;
    
    /**
     * 更新时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()", onUpdateValue = "unix_timestamp()")
    @Schema(description = "更新时间")
    private Long updateTime;
    
    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createBy;
    
    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateBy;
    
    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    @Column(isLogicDelete = true)
    @Schema(description = "删除标识")
    private Integer deleted = 0;
    
    /**
     * 版本号（乐观锁）
     */
    @Column(version = true)
    @Schema(description = "版本号")
    private Integer version = 0;
}
