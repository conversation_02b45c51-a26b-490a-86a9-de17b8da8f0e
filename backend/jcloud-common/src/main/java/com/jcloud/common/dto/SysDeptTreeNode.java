package com.jcloud.common.dto;

import com.jcloud.common.entity.SysDept;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 部门树节点DTO
 * 
 * 继承自SysDept，添加children字段用于构建树形结构
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "部门树节点")
public class SysDeptTreeNode extends SysDept {
    
    /**
     * 子部门列表
     */
    @Schema(description = "子部门列表")
    private List<SysDeptTreeNode> children;
}
