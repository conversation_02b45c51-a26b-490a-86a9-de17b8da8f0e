package com.jcloud.common.page;

import lombok.Data;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 分页查询参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "分页查询参数")
public class PageQuery {
    
    /**
     * 页码（从1开始）
     */
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String orderBy;
    
    /**
     * 排序方向（ASC/DESC）
     */
    @Schema(description = "排序方向", example = "DESC")
    private String orderDirection = "DESC";
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return pageSize;
    }
}
