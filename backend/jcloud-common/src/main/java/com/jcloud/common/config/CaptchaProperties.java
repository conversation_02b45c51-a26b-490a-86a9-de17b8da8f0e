package com.jcloud.common.config;

import com.jcloud.common.constant.CaptchaConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 验证码配置属性类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "jcloud.captcha")
public class  CaptchaProperties {
    
    /**
     * 是否启用验证码
     */
    private Boolean enabled = true;
    
    /**
     * 验证码类型
     * NUMERIC: 数字验证码
     * ALPHABETIC: 字母验证码
     * ALPHANUMERIC: 数字字母混合验证码
     * ARITHMETIC: 算术验证码
     */
    private String type = CaptchaConstants.TYPE_ALPHANUMERIC;
    
    /**
     * 验证码长度
     */
    private Integer length = CaptchaConstants.DEFAULT_LENGTH;
    
    /**
     * 验证码有效期（秒）
     */
    private Integer expireTime = CaptchaConstants.DEFAULT_EXPIRE_TIME;
    
    /**
     * 是否区分大小写
     */
    private Boolean caseSensitive = false;
    
    /**
     * 图片配置
     */
    private ImageConfig image = new ImageConfig();
    
    /**
     * 安全配置
     */
    private SecurityConfig security = new SecurityConfig();
    
    /**
     * 图片配置类
     */
    @Data
    public static class ImageConfig {
        
        /**
         * 图片宽度
         */
        private Integer width = CaptchaConstants.DEFAULT_WIDTH;
        
        /**
         * 图片高度
         */
        private Integer height = CaptchaConstants.DEFAULT_HEIGHT;
        
        /**
         * 字体名称
         */
        private String fontName = CaptchaConstants.DEFAULT_FONT_NAME;
        
        /**
         * 字体大小
         */
        private Integer fontSize = CaptchaConstants.DEFAULT_FONT_SIZE;
        
        /**
         * 干扰线数量
         */
        private Integer lineCount = CaptchaConstants.DEFAULT_LINE_COUNT;
        
        /**
         * 噪点数量
         */
        private Integer noiseCount = CaptchaConstants.DEFAULT_NOISE_COUNT;
        
        /**
         * 背景颜色（RGB格式，如：255,255,255）
         */
        private String backgroundColor = "255,255,255";
        
        /**
         * 文字颜色范围（RGB格式，如：0,0,0-100,100,100）
         */
        private String textColorRange = "0,0,0-100,100,100";
        
        /**
         * 干扰线颜色范围（RGB格式，如：150,150,150-200,200,200）
         */
        private String lineColorRange = "150,150,150-200,200,200";
        
        /**
         * 噪点颜色范围（RGB格式，如：100,100,100-150,150,150）
         */
        private String noiseColorRange = "100,100,100-150,150,150";
    }
    
    /**
     * 安全配置类
     */
    @Data
    public static class SecurityConfig {
        
        /**
         * 验证码验证失败最大次数
         */
        private Integer maxVerifyAttempts = CaptchaConstants.MAX_VERIFY_ATTEMPTS;
        
        /**
         * 验证码验证失败锁定时间（秒）
         */
        private Integer lockTime = CaptchaConstants.LOCK_TIME;
        
        /**
         * 是否启用防暴力破解
         */
        private Boolean enableBruteForceProtection = true;
    }
}
