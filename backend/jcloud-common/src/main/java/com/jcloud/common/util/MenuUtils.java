package com.jcloud.common.util;

import com.jcloud.common.dto.MenuDTO;
import com.jcloud.common.entity.SysMenu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class MenuUtils {
    
    /**
     * 将SysMenu转换为MenuDTO
     * 
     * @param sysMenu 系统菜单
     * @return 菜单DTO
     */
    public static MenuDTO convertToDTO(SysMenu sysMenu) {
        if (sysMenu == null) {
            return null;
        }
        
        MenuDTO menuDTO = new MenuDTO();
        menuDTO.setId(sysMenu.getId());
        menuDTO.setParentId(sysMenu.getParentId());
        menuDTO.setName(sysMenu.getMenuName());
        menuDTO.setTitle(sysMenu.getMenuName());
        menuDTO.setPath(sysMenu.getPath());
        menuDTO.setComponent(sysMenu.getComponent());
        menuDTO.setIcon(sysMenu.getIcon());
        menuDTO.setPermission(sysMenu.getPermissionCode());
        menuDTO.setType(sysMenu.getMenuType());
        menuDTO.setSort(sysMenu.getSortOrder());
        menuDTO.setVisible(sysMenu.getVisible() != null && sysMenu.getVisible() == 1);
        menuDTO.setEnabled(sysMenu.getStatus() != null && sysMenu.getStatus() == 1);
        
        return menuDTO;
    }
    
    /**
     * 将SysMenu列表转换为MenuDTO列表
     * 
     * @param sysMenus 系统菜单列表
     * @return 菜单DTO列表
     */
    public static List<MenuDTO> convertToDTOList(List<SysMenu> sysMenus) {
        if (sysMenus == null || sysMenus.isEmpty()) {
            return new ArrayList<>();
        }
        
        return sysMenus.stream()
                .map(MenuUtils::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 构建菜单树
     * 
     * @param menuList 菜单列表
     * @return 菜单树
     */
    public static List<MenuDTO> buildMenuTree(List<MenuDTO> menuList) {
        if (menuList == null || menuList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按父菜单ID分组
        Map<Long, List<MenuDTO>> menuMap = menuList.stream()
                .collect(Collectors.groupingBy(menu -> menu.getParentId() == null ? 0L : menu.getParentId()));
        
        // 递归构建树结构
        return buildTree(menuMap, 0L);
    }
    
    /**
     * 递归构建菜单树
     * 
     * @param menuMap 菜单映射
     * @param parentId 父菜单ID
     * @return 菜单树
     */
    private static List<MenuDTO> buildTree(Map<Long, List<MenuDTO>> menuMap, Long parentId) {
        List<MenuDTO> children = menuMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 排序
        children.sort((m1, m2) -> {
            Integer sort1 = m1.getSort() != null ? m1.getSort() : 0;
            Integer sort2 = m2.getSort() != null ? m2.getSort() : 0;
            return sort1.compareTo(sort2);
        });
        
        // 递归设置子菜单
        for (MenuDTO menu : children) {
            List<MenuDTO> subChildren = buildTree(menuMap, menu.getId());
            if (!subChildren.isEmpty()) {
                menu.setChildren(subChildren);
            }
        }
        
        return children;
    }
    
    /**
     * 将SysMenu列表转换为树形MenuDTO列表
     * 
     * @param sysMenus 系统菜单列表
     * @return 树形菜单DTO列表
     */
    public static List<MenuDTO> convertToTreeDTOList(List<SysMenu> sysMenus) {
        List<MenuDTO> menuDTOList = convertToDTOList(sysMenus);
        return buildMenuTree(menuDTOList);
    }
}
