package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 操作日志查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "操作日志查询请求")
public class OperLogQueryRequest extends PageQuery {
    
    /**
     * 操作模块
     */
    @Schema(description = "操作模块", example = "用户管理")
    private String title;
    
    /**
     * 业务类型
     */
    @Schema(description = "业务类型", example = "1")
    private Integer businessType;
    
    /**
     * 操作人员
     */
    @Schema(description = "操作人员", example = "admin")
    private String operName;
    
    /**
     * 操作状态（0-正常，1-异常）
     */
    @Schema(description = "操作状态", example = "0")
    private Integer status;
    
    /**
     * 操作时间开始
     */
    @Schema(description = "操作时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime operTimeStart;
    
    /**
     * 操作时间结束
     */
    @Schema(description = "操作时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime operTimeEnd;
}
