package com.jcloud.common.exception;

import com.jcloud.common.result.ResultCode;

/**
 * 权限异常类
 * 
 * 用于权限相关的异常处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class PermissionException extends BusinessException {
    
    /**
     * 权限异常类型
     */
    public enum PermissionType {
        /** 数据权限异常 */
        DATA_PERMISSION("数据权限"),
        /** 部门权限异常 */
        DEPT_PERMISSION("部门权限"),
        /** 租户权限异常 */
        TENANT_PERMISSION("租户权限"),
        /** 菜单权限异常 */
        MENU_PERMISSION("菜单权限"),
        /** 操作权限异常 */
        OPERATION_PERMISSION("操作权限");
        
        private final String description;
        
        PermissionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final PermissionType permissionType;
    private final String resource;
    private final String operation;
    
    public PermissionException(PermissionType permissionType, String resource, String operation) {
        super(String.format("%s异常：无权限%s资源[%s]",
                permissionType.getDescription(), operation, resource));
        this.permissionType = permissionType;
        this.resource = resource;
        this.operation = operation;
    }
    
    public PermissionException(PermissionType permissionType, String message) {
        super(permissionType.getDescription() + "异常：" + message);
        this.permissionType = permissionType;
        this.resource = null;
        this.operation = null;
    }

    public PermissionException(PermissionType permissionType, String message, Throwable cause) {
        super(permissionType.getDescription() + "异常：" + message, cause);
        this.permissionType = permissionType;
        this.resource = null;
        this.operation = null;
    }
    
    public PermissionType getPermissionType() {
        return permissionType;
    }
    
    public String getResource() {
        return resource;
    }
    
    public String getOperation() {
        return operation;
    }
    
    /**
     * 创建数据权限异常
     */
    public static PermissionException dataPermission(String resource, String operation) {
        return new PermissionException(PermissionType.DATA_PERMISSION, resource, operation);
    }
    
    /**
     * 创建部门权限异常
     */
    public static PermissionException deptPermission(String resource, String operation) {
        return new PermissionException(PermissionType.DEPT_PERMISSION, resource, operation);
    }
    
    /**
     * 创建租户权限异常
     */
    public static PermissionException tenantPermission(String resource, String operation) {
        return new PermissionException(PermissionType.TENANT_PERMISSION, resource, operation);
    }
    
    /**
     * 创建菜单权限异常
     */
    public static PermissionException menuPermission(String resource, String operation) {
        return new PermissionException(PermissionType.MENU_PERMISSION, resource, operation);
    }
    
    /**
     * 创建操作权限异常
     */
    public static PermissionException operationPermission(String resource, String operation) {
        return new PermissionException(PermissionType.OPERATION_PERMISSION, resource, operation);
    }
}
