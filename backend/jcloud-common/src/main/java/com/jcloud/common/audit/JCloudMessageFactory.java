package com.jcloud.common.audit;

import cn.dev33.satoken.stp.StpUtil;
import com.mybatisflex.core.audit.AuditMessage;
import com.mybatisflex.core.audit.MessageFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * jCloud自定义审计消息工厂
 * 负责创建包含用户上下文信息的审计消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class JCloudMessageFactory implements MessageFactory {

    private static final String PLATFORM = "jCloud";
    private static final String UNKNOWN = "unknown";

    @Override
    public AuditMessage create() {
        AuditMessage message = new AuditMessage();
        
        try {
            // 设置平台信息
            message.setPlatform(PLATFORM);
            
            // 设置时间戳 - MyBatis-Flex 1.8.0中AuditMessage没有setCreateTime方法
            // message.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            // 获取用户信息
            setUserInfo(message);
            
            // 获取请求信息
            setRequestInfo(message);
            
            // 获取主机信息
            setHostInfo(message);
            
        } catch (Exception e) {
            log.warn("创建审计消息时发生异常", e);
        }
        
        return message;
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(AuditMessage message) {
        try {
            if (StpUtil.isLogin()) {
                // 获取当前登录用户ID
                Object loginId = StpUtil.getLoginId();
                message.setUser(loginId != null ? loginId.toString() : UNKNOWN);
                
                // 尝试获取用户名（如果session中有存储）
                try {
                    Object username = StpUtil.getSession().get("username");
                    if (username != null) {
                        message.setUser(message.getUser() + "(" + username + ")");
                    }
                } catch (Exception e) {
                    // 忽略获取用户名失败的异常
                }
                
                // 获取用户角色信息
                try {
                    Object roles = StpUtil.getSession().get("roles");
                    if (roles != null) {
                        message.setModule("Role:" + roles.toString());
                    }
                } catch (Exception e) {
                    // 忽略获取角色失败的异常
                }
            } else {
                message.setUser("anonymous");
            }
        } catch (Exception e) {
            message.setUser(UNKNOWN);
            log.debug("获取用户信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(AuditMessage message) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // 设置请求URL
                String requestUrl = request.getRequestURL().toString();
                if (request.getQueryString() != null) {
                    requestUrl += "?" + request.getQueryString();
                }
                message.setUrl(requestUrl);
                
                // 设置HTTP方法
                String method = request.getMethod();
                if (message.getModule() == null) {
                    message.setModule(method);
                } else {
                    message.setModule(message.getModule() + " " + method);
                }
                
                // 设置客户端IP
                message.setUserIp(getClientIpAddress(request));
                
                // 设置User-Agent（可选，存储在扩展信息中）
                String userAgent = request.getHeader("User-Agent");
                if (userAgent != null && userAgent.length() > 100) {
                    userAgent = userAgent.substring(0, 100) + "...";
                }
                
                // 设置会话ID
                String sessionId = request.getSession(false) != null ? 
                    request.getSession().getId() : UNKNOWN;
                
                // 将额外信息组合到module字段中
                if (message.getModule() == null) {
                    message.setModule("Session:" + sessionId);
                } else {
                    message.setModule(message.getModule() + " Session:" + sessionId);
                }
                
            } else {
                message.setUrl(UNKNOWN);
                message.setUserIp(UNKNOWN);
            }
        } catch (Exception e) {
            message.setUrl(UNKNOWN);
            message.setUserIp(UNKNOWN);
            log.debug("获取请求信息失败", e);
        }
    }

    /**
     * 设置主机信息
     */
    private void setHostInfo(AuditMessage message) {
        try {
            InetAddress localhost = InetAddress.getLocalHost();
            message.setHostIp(localhost.getHostAddress());
        } catch (Exception e) {
            message.setHostIp(UNKNOWN);
            log.debug("获取主机IP失败", e);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，取第一个IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }
}
