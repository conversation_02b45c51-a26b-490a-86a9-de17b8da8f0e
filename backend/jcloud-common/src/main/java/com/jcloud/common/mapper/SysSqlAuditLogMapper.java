package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysSqlAuditLog;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * SQL审计日志数据访问层
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysSqlAuditLogMapper extends BaseMapper<SysSqlAuditLog> {

    /**
     * 批量插入审计日志
     *
     * @param logs 审计日志列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("logs") List<SysSqlAuditLog> logs);

    /**
     * 删除指定天数之前的审计日志
     *
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    int deleteByCreateTimeBefore(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 统计慢SQL数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 慢SQL数量
     */
    long countSlowSql(@Param("startTime") LocalDateTime startTime, 
                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计各SQL类型的执行次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return SQL类型统计结果
     */
    List<Object> countBySqlType(@Param("startTime") LocalDateTime startTime, 
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最耗时的SQL
     *
     * @param limit 限制数量
     * @return 最耗时的SQL列表
     */
    List<SysSqlAuditLog> findTopSlowSql(@Param("limit") int limit);

    /**
     * 按用户统计SQL执行情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户统计结果
     */
    List<Map<String, Object>> countByUser(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 按数据源统计SQL执行情况
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据源统计结果
     */
    List<Map<String, Object>> countByDataSource(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 获取SQL执行趋势
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行趋势数据
     */
    List<Map<String, Object>> getExecutionTrend(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
}
