package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 权限查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限查询请求")
public class PermissionQueryRequest extends PageQuery {
    
    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "system:user:list")
    private String permissionCode;
    
    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户列表")
    private String permissionName;
    
    /**
     * 权限类型（MENU-菜单，BUTTON-按钮，API-接口）
     */
    @Schema(description = "权限类型", example = "API")
    private String permissionType;
    
    /**
     * 资源路径
     */
    @Schema(description = "资源路径", example = "/system/user/page")
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET")
    private String method;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
}
