package com.jcloud.common.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.common.config.CaptchaProperties;
import com.jcloud.common.constant.CaptchaConstants;
import com.jcloud.common.dto.CaptchaResponse;
import com.jcloud.common.exception.CaptchaException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 验证码工具类
 * 提供验证码生成、验证、失效管理等功能
 * 支持多种验证码类型和图形验证码生成
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaptchaUtils {
    
    private final CaptchaProperties captchaProperties;
    private final StringRedisTemplate redisTemplate;
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 生成验证码
     * 
     * @return 验证码响应对象
     */
    public CaptchaResponse generateCaptcha() {
        try {
            // 生成验证码标识
            String captchaId = IdUtil.simpleUUID();
            
            // 根据类型生成验证码内容
            String captchaCode = generateCaptchaCode();
            
            // 生成验证码图片
            String captchaImage = generateCaptchaImage(captchaCode);
            
            // 存储到Redis缓存
            storeCaptchaToCache(captchaId, captchaCode);
            
            log.debug("生成验证码成功，ID: {}, 类型: {}", captchaId, captchaProperties.getType());
            
            return CaptchaResponse.builder()
                    .captchaId(captchaId)
                    .captchaImage(captchaImage)
                    .expireTime(captchaProperties.getExpireTime())
                    .captchaType(captchaProperties.getType())
                    .build();
                    
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw CaptchaException.generateFailed(e);
        }
    }
    
    /**
     * 验证验证码
     * 
     * @param captchaId 验证码标识
     * @param inputCode 用户输入的验证码
     * @return 验证结果
     */
    public boolean verifyCaptcha(String captchaId, String inputCode) {
        if (StrUtil.isBlank(captchaId) || StrUtil.isBlank(inputCode)) {
            throw CaptchaException.captchaError("验证码标识或验证码不能为空");
        }
        
        try {
            // 检查是否被锁定
            if (isLocked(captchaId)) {
                throw CaptchaException.tooManyAttempts();
            }
            
            // 从缓存获取验证码
            String cachedCode = getCaptchaFromCache(captchaId);
            if (StrUtil.isBlank(cachedCode)) {
                throw CaptchaException.captchaNotFound();
            }
            
            // 验证码比较
            boolean isValid = compareCaptcha(cachedCode, inputCode);
            
            if (isValid) {
                // 验证成功，删除缓存
                invalidateCaptcha(captchaId);
                clearFailCount(captchaId);
                log.debug("验证码验证成功，ID: {}", captchaId);
                return true;
            } else {
                // 验证失败，记录失败次数
                recordFailAttempt(captchaId);
                log.warn("验证码验证失败，ID: {}, 输入: {}", captchaId, inputCode);
                throw CaptchaException.captchaError();
            }
            
        } catch (CaptchaException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证验证码时发生异常，ID: {}", captchaId, e);
            throw CaptchaException.captchaError("验证码验证失败");
        }
    }
    
    /**
     * 使验证码失效
     * 
     * @param captchaId 验证码标识
     */
    public void invalidateCaptcha(String captchaId) {
        if (StrUtil.isNotBlank(captchaId)) {
            String cacheKey = buildCacheKey(captchaId);
            redisTemplate.delete(cacheKey);
            log.debug("验证码已失效，ID: {}", captchaId);
        }
    }
    
    /**
     * 根据类型生成验证码内容
     */
    private String generateCaptchaCode() {
        String type = captchaProperties.getType();
        int length = captchaProperties.getLength();
        
        switch (type) {
            case CaptchaConstants.TYPE_NUMERIC:
                return generateRandomString(CaptchaConstants.NUMERIC_CHARS, length);
            case CaptchaConstants.TYPE_ALPHABETIC:
                return generateRandomString(CaptchaConstants.ALPHABETIC_CHARS, length);
            case CaptchaConstants.TYPE_ALPHANUMERIC:
                return generateRandomString(CaptchaConstants.ALPHANUMERIC_CHARS, length);
            case CaptchaConstants.TYPE_ARITHMETIC:
                return generateArithmeticExpression();
            default:
                return generateRandomString(CaptchaConstants.ALPHANUMERIC_CHARS, length);
        }
    }
    
    /**
     * 生成随机字符串
     */
    private String generateRandomString(String chars, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
    
    /**
     * 生成算术表达式验证码
     */
    private String generateArithmeticExpression() {
        int num1 = random.nextInt(10) + 1;
        int num2 = random.nextInt(10) + 1;
        String operator = CaptchaConstants.ARITHMETIC_OPERATORS[random.nextInt(CaptchaConstants.ARITHMETIC_OPERATORS.length)];
        
        int result;
        switch (operator) {
            case "+":
                result = num1 + num2;
                break;
            case "-":
                // 确保结果为正数
                if (num1 < num2) {
                    int temp = num1;
                    num1 = num2;
                    num2 = temp;
                }
                result = num1 - num2;
                break;
            case "*":
                // 使用较小的数字避免结果过大
                num1 = random.nextInt(5) + 1;
                num2 = random.nextInt(5) + 1;
                result = num1 * num2;
                break;
            default:
                result = num1 + num2;
        }
        
        // 返回格式：表达式=结果，实际存储结果用于验证
        return String.valueOf(result);
    }
    
    /**
     * 生成验证码图片
     */
    private String generateCaptchaImage(String code) throws IOException {
        CaptchaProperties.ImageConfig imageConfig = captchaProperties.getImage();
        
        // 创建图片
        BufferedImage image = new BufferedImage(
                imageConfig.getWidth(), 
                imageConfig.getHeight(), 
                BufferedImage.TYPE_INT_RGB
        );
        
        Graphics2D g2d = image.createGraphics();
        
        try {
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            // 设置背景色
            g2d.setColor(parseColor(imageConfig.getBackgroundColor()));
            g2d.fillRect(0, 0, imageConfig.getWidth(), imageConfig.getHeight());
            
            // 绘制干扰线
            drawInterferenceLines(g2d, imageConfig);
            
            // 绘制验证码文字
            drawCaptchaText(g2d, code, imageConfig);
            
            // 绘制噪点
            drawNoise(g2d, imageConfig);
            
        } finally {
            g2d.dispose();
        }
        
        // 转换为Base64
        return imageToBase64(image);
    }
    
    /**
     * 绘制干扰线
     */
    private void drawInterferenceLines(Graphics2D g2d, CaptchaProperties.ImageConfig imageConfig) {
        Color[] lineColors = parseColorRange(imageConfig.getLineColorRange());
        
        for (int i = 0; i < imageConfig.getLineCount(); i++) {
            g2d.setColor(lineColors[random.nextInt(lineColors.length)]);
            int x1 = random.nextInt(imageConfig.getWidth());
            int y1 = random.nextInt(imageConfig.getHeight());
            int x2 = random.nextInt(imageConfig.getWidth());
            int y2 = random.nextInt(imageConfig.getHeight());
            g2d.drawLine(x1, y1, x2, y2);
        }
    }
    
    /**
     * 绘制验证码文字
     */
    private void drawCaptchaText(Graphics2D g2d, String code, CaptchaProperties.ImageConfig imageConfig) {
        Font font = new Font(imageConfig.getFontName(), Font.BOLD, imageConfig.getFontSize());
        g2d.setFont(font);
        
        Color[] textColors = parseColorRange(imageConfig.getTextColorRange());
        
        int charWidth = imageConfig.getWidth() / code.length();
        int charHeight = imageConfig.getHeight();
        
        for (int i = 0; i < code.length(); i++) {
            g2d.setColor(textColors[random.nextInt(textColors.length)]);
            
            // 随机位置和角度
            int x = i * charWidth + random.nextInt(charWidth / 4);
            int y = charHeight / 2 + random.nextInt(charHeight / 4);
            
            // 随机旋转角度
            double angle = (random.nextDouble() - 0.5) * 0.4;
            g2d.rotate(angle, x, y);
            
            g2d.drawString(String.valueOf(code.charAt(i)), x, y);
            
            // 恢复旋转
            g2d.rotate(-angle, x, y);
        }
    }
    
    /**
     * 绘制噪点
     */
    private void drawNoise(Graphics2D g2d, CaptchaProperties.ImageConfig imageConfig) {
        Color[] noiseColors = parseColorRange(imageConfig.getNoiseColorRange());
        
        for (int i = 0; i < imageConfig.getNoiseCount(); i++) {
            g2d.setColor(noiseColors[random.nextInt(noiseColors.length)]);
            int x = random.nextInt(imageConfig.getWidth());
            int y = random.nextInt(imageConfig.getHeight());
            g2d.fillOval(x, y, 1, 1);
        }
    }
    
    /**
     * 解析颜色
     */
    private Color parseColor(String colorStr) {
        String[] rgb = colorStr.split(",");
        return new Color(
                Integer.parseInt(rgb[0].trim()),
                Integer.parseInt(rgb[1].trim()),
                Integer.parseInt(rgb[2].trim())
        );
    }
    
    /**
     * 解析颜色范围
     */
    private Color[] parseColorRange(String colorRangeStr) {
        String[] range = colorRangeStr.split("-");
        Color startColor = parseColor(range[0]);
        Color endColor = parseColor(range[1]);
        
        // 生成颜色范围内的随机颜色
        Color[] colors = new Color[10];
        for (int i = 0; i < colors.length; i++) {
            int r = startColor.getRed() + random.nextInt(endColor.getRed() - startColor.getRed() + 1);
            int g = startColor.getGreen() + random.nextInt(endColor.getGreen() - startColor.getGreen() + 1);
            int b = startColor.getBlue() + random.nextInt(endColor.getBlue() - startColor.getBlue() + 1);
            colors[i] = new Color(r, g, b);
        }
        return colors;
    }
    
    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, CaptchaConstants.IMAGE_FORMAT, baos);
        byte[] imageBytes = baos.toByteArray();
        return CaptchaConstants.BASE64_IMAGE_PREFIX + Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 存储验证码到缓存
     */
    private void storeCaptchaToCache(String captchaId, String code) {
        String cacheKey = buildCacheKey(captchaId);
        redisTemplate.opsForValue().set(
                cacheKey, 
                code, 
                captchaProperties.getExpireTime(), 
                TimeUnit.SECONDS
        );
    }
    
    /**
     * 从缓存获取验证码
     */
    private String getCaptchaFromCache(String captchaId) {
        String cacheKey = buildCacheKey(captchaId);
        return redisTemplate.opsForValue().get(cacheKey);
    }
    
    /**
     * 比较验证码
     */
    private boolean compareCaptcha(String cachedCode, String inputCode) {
        if (captchaProperties.getCaseSensitive()) {
            return cachedCode.equals(inputCode);
        } else {
            return cachedCode.equalsIgnoreCase(inputCode);
        }
    }
    
    /**
     * 记录验证失败次数
     */
    private void recordFailAttempt(String captchaId) {
        if (!captchaProperties.getSecurity().getEnableBruteForceProtection()) {
            return;
        }
        
        String failCountKey = buildFailCountKey(captchaId);
        String countStr = redisTemplate.opsForValue().get(failCountKey);
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;
        
        if (count >= captchaProperties.getSecurity().getMaxVerifyAttempts()) {
            // 锁定
            redisTemplate.opsForValue().set(
                    failCountKey, 
                    String.valueOf(count), 
                    captchaProperties.getSecurity().getLockTime(), 
                    TimeUnit.SECONDS
            );
        } else {
            redisTemplate.opsForValue().set(
                    failCountKey, 
                    String.valueOf(count), 
                    captchaProperties.getExpireTime(), 
                    TimeUnit.SECONDS
            );
        }
    }
    
    /**
     * 检查是否被锁定
     */
    private boolean isLocked(String captchaId) {
        if (!captchaProperties.getSecurity().getEnableBruteForceProtection()) {
            return false;
        }
        
        String failCountKey = buildFailCountKey(captchaId);
        String countStr = redisTemplate.opsForValue().get(failCountKey);
        if (countStr == null) {
            return false;
        }
        
        int count = Integer.parseInt(countStr);
        return count >= captchaProperties.getSecurity().getMaxVerifyAttempts();
    }
    
    /**
     * 清除失败次数
     */
    private void clearFailCount(String captchaId) {
        String failCountKey = buildFailCountKey(captchaId);
        redisTemplate.delete(failCountKey);
    }
    
    /**
     * 构建缓存Key
     */
    private String buildCacheKey(String captchaId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId != null) {
            return CaptchaConstants.CAPTCHA_CACHE_PREFIX + tenantId + ":" + captchaId;
        }
        return CaptchaConstants.CAPTCHA_CACHE_PREFIX + captchaId;
    }
    
    /**
     * 构建失败次数缓存Key
     */
    private String buildFailCountKey(String captchaId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId != null) {
            return CaptchaConstants.VERIFY_FAIL_COUNT_PREFIX + tenantId + ":" + captchaId;
        }
        return CaptchaConstants.VERIFY_FAIL_COUNT_PREFIX + captchaId;
    }
}
