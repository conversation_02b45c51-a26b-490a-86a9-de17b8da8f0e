package com.jcloud.common.dto;

import com.jcloud.common.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 部门查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "部门查询请求")
public class DeptQueryRequest extends PageQuery {

    /**
     * 搜索关键词（同时搜索部门编码和部门名称）
     */
    @Schema(description = "搜索关键词", example = "技术")
    private String keyword;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", example = "0")
    private Long parentId;
    
    /**
     * 部门编码
     */
    @Schema(description = "部门编码", example = "TECH")
    private String deptCode;
    
    /**
     * 部门名称
     */
    @Schema(description = "部门名称", example = "技术部")
    private String deptName;
    
    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID", example = "1")
    private Long leaderId;
    
    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "010-12345678")
    private String phone;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
    
    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2023-01-01 00:00:00")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2023-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
}
