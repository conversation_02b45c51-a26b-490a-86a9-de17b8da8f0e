package com.jcloud.common.service.impl;

import com.jcloud.common.entity.SysSqlAuditLog;
import com.jcloud.common.mapper.SysSqlAuditLogMapper;
import com.jcloud.common.service.SqlAuditLogService;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SQL审计日志服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SqlAuditLogServiceImpl extends ServiceImpl<SysSqlAuditLogMapper, SysSqlAuditLog> 
        implements SqlAuditLogService {

    private final SysSqlAuditLogMapper sqlAuditLogMapper;

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<SysSqlAuditLog> logs) {
        if (logs == null || logs.isEmpty()) {
            return 0;
        }

        try {
            if (logs.size() == 1) {
                boolean success = save(logs.get(0));
                return success ? 1 : 0;
            } else {
                return sqlAuditLogMapper.batchInsert(logs);
            }
        } catch (Exception e) {
            log.error("批量保存SQL审计日志失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredLogs(int retentionDays) {
        if (retentionDays <= 0) {
            log.warn("保留天数必须大于0，跳过清理操作");
            return 0;
        }

        try {
            LocalDateTime beforeDate = LocalDateTime.now().minusDays(retentionDays);
            int deletedCount = sqlAuditLogMapper.deleteByCreateTimeBefore(beforeDate);
            
            if (deletedCount > 0) {
                log.info("清理过期SQL审计日志完成，删除{}条记录，保留{}天内的数据", deletedCount, retentionDays);
            }
            
            return deletedCount;
        } catch (Exception e) {
            log.error("清理过期SQL审计日志失败", e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> getExecutionStats(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 总执行次数
            long totalCount = count();
            stats.put("totalCount", totalCount);
            
            // 慢SQL数量
            long slowSqlCount = sqlAuditLogMapper.countSlowSql(startTime, endTime);
            stats.put("slowSqlCount", slowSqlCount);
            
            // 慢SQL比例
            double slowSqlRatio = totalCount > 0 ? (double) slowSqlCount / totalCount * 100 : 0;
            stats.put("slowSqlRatio", Math.round(slowSqlRatio * 100.0) / 100.0);
            
            // 按SQL类型统计
            List<Object> sqlTypeStatsRaw = sqlAuditLogMapper.countBySqlType(startTime, endTime);
            stats.put("sqlTypeStats", sqlTypeStatsRaw);
            
            return stats;
        } catch (Exception e) {
            log.error("获取SQL执行统计信息失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getSlowSqlStats(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 慢SQL总数
            long slowSqlCount = sqlAuditLogMapper.countSlowSql(startTime, endTime);
            stats.put("slowSqlCount", slowSqlCount);
            
            // 最耗时的SQL
            List<SysSqlAuditLog> topSlowSql = sqlAuditLogMapper.findTopSlowSql(10);
            stats.put("topSlowSql", topSlowSql);
            
            return stats;
        } catch (Exception e) {
            log.error("获取慢SQL统计信息失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<SysSqlAuditLog> getTopSlowSql(int limit) {
        try {
            return sqlAuditLogMapper.findTopSlowSql(limit);
        } catch (Exception e) {
            log.error("获取最耗时SQL列表失败", e);
            return List.of();
        }
    }

    @Override
    public List<Map<String, Object>> getStatsBySqlType(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<Object> rawResults = sqlAuditLogMapper.countBySqlType(startTime, endTime);
            return rawResults.stream()
                .filter(obj -> obj instanceof Map)
                .map(obj -> (Map<String, Object>) obj)
                .toList();
        } catch (Exception e) {
            log.error("按SQL类型统计执行情况失败", e);
            return List.of();
        }
    }

    @Override
    public List<Map<String, Object>> getStatsByUser(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return sqlAuditLogMapper.countByUser(startTime, endTime);
        } catch (Exception e) {
            log.error("按用户统计SQL执行情况失败", e);
            return List.of();
        }
    }

    @Override
    public List<Map<String, Object>> getStatsByDataSource(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return sqlAuditLogMapper.countByDataSource(startTime, endTime);
        } catch (Exception e) {
            log.error("按数据源统计SQL执行情况失败", e);
            return List.of();
        }
    }

    @Override
    public List<Map<String, Object>> getExecutionTrend(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return sqlAuditLogMapper.getExecutionTrend(startTime, endTime);
        } catch (Exception e) {
            log.error("获取SQL执行趋势失败", e);
            return List.of();
        }
    }

    @Override
    public Map<String, Object> getAuditOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
            LocalDateTime yesterdayStart = todayStart.minusDays(1);
            
            // 今日统计
            Map<String, Object> todayStats = getExecutionStats(todayStart, now);
            overview.put("today", todayStats);
            
            // 昨日统计
            Map<String, Object> yesterdayStats = getExecutionStats(yesterdayStart, todayStart);
            overview.put("yesterday", yesterdayStats);
            
            // 最近7天趋势
            LocalDateTime weekAgo = now.minusDays(7);
            List<Map<String, Object>> weekTrend = getExecutionTrend(weekAgo, now);
            overview.put("weekTrend", weekTrend);
            
            return overview;
        } catch (Exception e) {
            log.error("获取审计日志概览信息失败", e);
            return new HashMap<>();
        }
    }
}
