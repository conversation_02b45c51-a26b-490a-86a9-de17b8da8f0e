package com.jcloud.common.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * SQL审计日志实体类
 * 记录所有SQL执行的详细信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("sys_sql_audit_log")
public class SysSqlAuditLog {

    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 平台标识
     */
    private String platform;

    /**
     * 模块信息（包含HTTP方法、角色等）
     */
    private String module;

    /**
     * 请求URL
     */
    private String url;

    /**
     * 用户信息（用户ID和用户名）
     */
    private String user;

    /**
     * 用户IP地址
     */
    private String userIp;

    /**
     * 服务器IP地址
     */
    private String hostIp;

    /**
     * 数据源名称
     */
    private String dataSource;

    /**
     * SQL语句
     */
    private String sqlStatement;

    /**
     * SQL参数（JSON格式，已脱敏）
     */
    private String sqlParams;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 是否为慢SQL
     */
    private Boolean isSlowSql;

    /**
     * SQL类型（SELECT, INSERT, UPDATE, DELETE等）
     */
    private String sqlType;

    /**
     * 影响行数
     */
    private Integer affectedRows;

    /**
     * 执行状态（SUCCESS, ERROR）
     */
    private String status;

    /**
     * 错误信息（如果执行失败）
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 租户ID（多租户支持）
     */
    private Long tenantId;

    /**
     * 扩展信息（JSON格式）
     */
    private String extInfo;

    /**
     * 获取SQL类型
     */
    public static String getSqlType(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "UNKNOWN";
        }
        
        String upperSql = sql.trim().toUpperCase();
        if (upperSql.startsWith("SELECT")) {
            return "SELECT";
        } else if (upperSql.startsWith("INSERT")) {
            return "INSERT";
        } else if (upperSql.startsWith("UPDATE")) {
            return "UPDATE";
        } else if (upperSql.startsWith("DELETE")) {
            return "DELETE";
        } else if (upperSql.startsWith("CREATE")) {
            return "CREATE";
        } else if (upperSql.startsWith("DROP")) {
            return "DROP";
        } else if (upperSql.startsWith("ALTER")) {
            return "ALTER";
        } else {
            return "OTHER";
        }
    }

    /**
     * 判断是否为慢SQL
     */
    public static boolean isSlowSql(long executionTime, long threshold) {
        return executionTime > threshold;
    }
}
