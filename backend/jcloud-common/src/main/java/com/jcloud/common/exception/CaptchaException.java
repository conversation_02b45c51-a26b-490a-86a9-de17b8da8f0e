package com.jcloud.common.exception;

/**
 * 验证码异常类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class CaptchaException extends BusinessException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 验证码错误码
     */
    public static final Integer CAPTCHA_ERROR_CODE = 4001;
    
    /**
     * 验证码过期错误码
     */
    public static final Integer CAPTCHA_EXPIRED_CODE = 4002;
    
    /**
     * 验证码不存在错误码
     */
    public static final Integer CAPTCHA_NOT_FOUND_CODE = 4003;
    
    /**
     * 验证码验证失败次数过多错误码
     */
    public static final Integer CAPTCHA_TOO_MANY_ATTEMPTS_CODE = 4004;
    
    /**
     * 验证码生成失败错误码
     */
    public static final Integer CAPTCHA_GENERATE_FAILED_CODE = 4005;
    
    public CaptchaException(String message) {
        super(CAPTCHA_ERROR_CODE, message);
    }
    
    public CaptchaException(Integer code, String message) {
        super(code, message);
    }
    
    public CaptchaException(String message, Throwable cause) {
        super(CAPTCHA_ERROR_CODE, message, cause);
    }
    
    public CaptchaException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 验证码错误
     */
    public static CaptchaException captchaError() {
        return new CaptchaException(CAPTCHA_ERROR_CODE, "验证码错误");
    }
    
    /**
     * 验证码错误
     */
    public static CaptchaException captchaError(String message) {
        return new CaptchaException(CAPTCHA_ERROR_CODE, message);
    }
    
    /**
     * 验证码过期
     */
    public static CaptchaException captchaExpired() {
        return new CaptchaException(CAPTCHA_EXPIRED_CODE, "验证码已过期");
    }
    
    /**
     * 验证码不存在
     */
    public static CaptchaException captchaNotFound() {
        return new CaptchaException(CAPTCHA_NOT_FOUND_CODE, "验证码不存在");
    }
    
    /**
     * 验证码验证失败次数过多
     */
    public static CaptchaException tooManyAttempts() {
        return new CaptchaException(CAPTCHA_TOO_MANY_ATTEMPTS_CODE, "验证码验证失败次数过多，请稍后再试");
    }
    
    /**
     * 验证码生成失败
     */
    public static CaptchaException generateFailed() {
        return new CaptchaException(CAPTCHA_GENERATE_FAILED_CODE, "验证码生成失败");
    }
    
    /**
     * 验证码生成失败
     */
    public static CaptchaException generateFailed(String message) {
        return new CaptchaException(CAPTCHA_GENERATE_FAILED_CODE, message);
    }
    
    /**
     * 验证码生成失败
     */
    public static CaptchaException generateFailed(Throwable cause) {
        return new CaptchaException(CAPTCHA_GENERATE_FAILED_CODE, "验证码生成失败", cause);
    }
}
