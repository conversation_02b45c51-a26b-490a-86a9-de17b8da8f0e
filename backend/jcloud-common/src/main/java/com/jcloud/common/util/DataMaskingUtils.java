package com.jcloud.common.util;

/**
 * 数据脱敏工具类
 * 提供各种敏感数据的脱敏处理方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DataMaskingUtils {

    /**
     * 手机号脱敏
     * 格式：138****8888
     * 
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }
        
        // 移除所有非数字字符
        String cleanPhone = phone.replaceAll("\\D", "");
        
        if (cleanPhone.length() < 7) {
            return phone;
        }
        
        // 中国手机号通常是11位
        if (cleanPhone.length() == 11) {
            return cleanPhone.substring(0, 3) + "****" + cleanPhone.substring(7);
        }
        
        // 其他长度的手机号，保留前3位和后4位
        if (cleanPhone.length() > 7) {
            return cleanPhone.substring(0, 3) + "****" + cleanPhone.substring(cleanPhone.length() - 4);
        }
        
        // 长度不足7位的，只显示前3位
        return cleanPhone.substring(0, 3) + "****";
    }
    
    /**
     * 邮箱脱敏
     * 格式：u***@example.com
     * 
     * @param email 原始邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (email == null || email.trim().isEmpty() || !email.contains("@")) {
            return email;
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email;
        }
        
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 1) {
            return email;
        }
        
        // 保留用户名的第一个字符，其余用*替代
        String maskedUsername = username.charAt(0) + "***";
        
        return maskedUsername + "@" + domain;
    }
    
    /**
     * 身份证号脱敏
     * 格式：110***********1234
     * 
     * @param idCard 原始身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdCard(String idCard) {
        if (idCard == null || idCard.trim().isEmpty() || idCard.length() < 8) {
            return idCard;
        }
        
        // 保留前3位和后4位
        if (idCard.length() >= 8) {
            int prefixLength = 3;
            int suffixLength = 4;
            int maskLength = idCard.length() - prefixLength - suffixLength;
            
            StringBuilder masked = new StringBuilder();
            masked.append(idCard.substring(0, prefixLength));
            for (int i = 0; i < maskLength; i++) {
                masked.append("*");
            }
            masked.append(idCard.substring(idCard.length() - suffixLength));
            
            return masked.toString();
        }
        
        return idCard;
    }
    
    /**
     * 银行卡号脱敏
     * 格式：6222***********1234
     * 
     * @param bankCard 原始银行卡号
     * @return 脱敏后的银行卡号
     */
    public static String maskBankCard(String bankCard) {
        if (bankCard == null || bankCard.trim().isEmpty() || bankCard.length() < 8) {
            return bankCard;
        }
        
        // 保留前4位和后4位
        if (bankCard.length() >= 8) {
            int prefixLength = 4;
            int suffixLength = 4;
            int maskLength = bankCard.length() - prefixLength - suffixLength;
            
            StringBuilder masked = new StringBuilder();
            masked.append(bankCard.substring(0, prefixLength));
            for (int i = 0; i < maskLength; i++) {
                masked.append("*");
            }
            masked.append(bankCard.substring(bankCard.length() - suffixLength));
            
            return masked.toString();
        }
        
        return bankCard;
    }
    
    /**
     * 姓名脱敏
     * 格式：张*（两个字），张**（三个字及以上）
     * 
     * @param name 原始姓名
     * @return 脱敏后的姓名
     */
    public static String maskName(String name) {
        if (name == null || name.trim().isEmpty() || name.length() <= 1) {
            return name;
        }
        
        if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            StringBuilder masked = new StringBuilder();
            masked.append(name.charAt(0));
            for (int i = 1; i < name.length(); i++) {
                masked.append("*");
            }
            return masked.toString();
        }
    }
    
    /**
     * 地址脱敏
     * 保留省市，详细地址用*替代
     * 
     * @param address 原始地址
     * @return 脱敏后的地址
     */
    public static String maskAddress(String address) {
        if (address == null || address.trim().isEmpty() || address.length() <= 6) {
            return address;
        }
        
        // 保留前6个字符（通常是省市信息），其余用*替代
        StringBuilder masked = new StringBuilder();
        masked.append(address.substring(0, 6));
        for (int i = 6; i < address.length(); i++) {
            masked.append("*");
        }
        
        return masked.toString();
    }
    
    /**
     * 通用脱敏方法
     * 根据脱敏类型进行相应的脱敏处理
     * 
     * @param data 原始数据
     * @param maskType 脱敏类型
     * @return 脱敏后的数据
     */
    public static String mask(String data, String maskType) {
        if (data == null || data.trim().isEmpty() || maskType == null || maskType.trim().isEmpty()) {
            return data;
        }
        
        switch (maskType.toLowerCase()) {
            case "phone":
                return maskPhone(data);
            case "email":
                return maskEmail(data);
            case "idcard":
                return maskIdCard(data);
            case "bankcard":
                return maskBankCard(data);
            case "name":
                return maskName(data);
            case "address":
                return maskAddress(data);
            default:
                return data;
        }
    }
}
