package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

/**
 * 菜单更新请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "菜单更新请求")
public class MenuUpdateRequest {
    
    /**
     * 菜单ID
     */
    @Schema(description = "菜单ID", example = "1")
    @NotNull(message = "菜单ID不能为空")
    private Long id;
    
    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID", example = "0")
    private Long parentId;
    
    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称", example = "用户管理")
    @Size(max = 100, message = "菜单名称长度不能超过100个字符")
    private String menuName;
    
    /**
     * 路由路径
     */
    @Schema(description = "路由路径", example = "/system/user")
    @Size(max = 200, message = "路由路径长度不能超过200个字符")
    private String path;
    
    /**
     * 组件路径
     */
    @Schema(description = "组件路径", example = "system/user/index")
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String component;
    
    /**
     * 菜单图标
     */
    @Schema(description = "菜单图标", example = "user")
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String icon;
    
    /**
     * 权限标识
     */
    @Schema(description = "权限标识", example = "system:user:list")
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String permissionCode;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 是否显示（0-隐藏，1-显示）
     */
    @Schema(description = "是否显示", example = "1")
    @Min(value = 0, message = "显示状态值不正确")
    @Max(value = 1, message = "显示状态值不正确")
    private Integer visible;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "用户管理菜单")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
