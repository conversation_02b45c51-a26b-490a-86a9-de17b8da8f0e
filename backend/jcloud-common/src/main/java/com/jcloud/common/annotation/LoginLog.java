package com.jcloud.common.annotation;

import java.lang.annotation.*;

/**
 * 登录日志注解
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LoginLog {
    
    /**
     * 操作描述
     */
    String value() default "用户登录";
    
    /**
     * 是否记录登录成功
     */
    boolean recordSuccess() default true;
    
    /**
     * 是否记录登录失败
     */
    boolean recordFailure() default true;
}
