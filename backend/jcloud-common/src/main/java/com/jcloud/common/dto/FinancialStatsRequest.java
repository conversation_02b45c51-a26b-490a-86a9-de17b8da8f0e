package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 财务统计查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "财务统计查询请求")
public class FinancialStatsRequest {
    
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "开始时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式")
    private String startTime;
    
    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-01-31 23:59:59")
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "结束时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式")
    private String endTime;
    
    /**
     * 是否包含主播数据
     */
    @Schema(description = "是否包含主播数据", example = "true")
    private Boolean includeAnchor = true;
}