package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysTenant;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 租户信息Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysTenantMapper extends BaseMapper<SysTenant> {

    /**
     * 根据租户编码查询租户信息
     * 
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    @Select("SELECT * FROM sys_tenant WHERE tenant_code = #{tenantCode} AND deleted = 0")
    SysTenant selectByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 检查租户编码是否存在
     * 
     * @param tenantCode 租户编码
     * @param excludeId 排除的租户ID（编辑时使用）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_tenant " +
            "WHERE tenant_code = #{tenantCode} AND deleted = 0 " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    int checkTenantCodeExists(@Param("tenantCode") String tenantCode, @Param("excludeId") Long excludeId);

    /**
     * 根据租户状态查询租户数量
     * 
     * @param status 租户状态
     * @return 租户数量
     */
    @Select("SELECT COUNT(1) FROM sys_tenant WHERE status = #{status} AND deleted = 0")
    int countByStatus(@Param("status") Integer status);

    /**
     * 查询即将过期的租户数量
     * 
     * @param days 天数
     * @return 租户数量
     */
    @Select("SELECT COUNT(1) FROM sys_tenant " +
            "WHERE expire_time IS NOT NULL " +
            "AND expire_time <= DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "AND expire_time > NOW() " +
            "AND deleted = 0")
    int countExpiringSoon(@Param("days") int days);

    /**
     * 查询已过期的租户数量
     * 
     * @return 租户数量
     */
    @Select("SELECT COUNT(1) FROM sys_tenant " +
            "WHERE expire_time IS NOT NULL " +
            "AND expire_time < NOW() " +
            "AND deleted = 0")
    int countExpired();
}
