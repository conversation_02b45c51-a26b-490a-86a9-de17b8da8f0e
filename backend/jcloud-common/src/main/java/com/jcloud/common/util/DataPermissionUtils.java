package com.jcloud.common.util;

import com.mybatisflex.core.tenant.TenantManager;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 数据权限工具类
 * 
 * 基于MyBatis-Flex提供的数据权限和多租户功能的便捷工具方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class DataPermissionUtils {
    
    /**
     * 临时忽略租户条件执行操作
     * 
     * 使用场景：
     * - 系统管理员需要跨租户查询数据
     * - 数据迁移或同步操作
     * - 系统级别的统计分析
     * 
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T withoutTenantCondition(Supplier<T> supplier) {
        log.debug("临时忽略租户条件执行操作");
        return TenantManager.withoutTenantCondition(supplier);
    }
    
    /**
     * 临时忽略租户条件执行操作（无返回值）
     * 
     * @param runnable 要执行的操作
     */
    public static void withoutTenantCondition(Runnable runnable) {
        log.debug("临时忽略租户条件执行操作（无返回值）");
        TenantManager.withoutTenantCondition(() -> {
            runnable.run();
            return null;
        });
    }
    
    /**
     * 检查当前用户是否可以访问指定租户的数据
     * 
     * @param targetTenantId 目标租户ID
     * @return 是否有权限访问
     */
    public static boolean canAccessTenant(Long targetTenantId) {
        // 超级管理员可以访问所有租户数据
        if (SecurityUtils.isSuperAdmin()) {
            return true;
        }
        
        Long currentTenantId = SecurityUtils.getTenantId();
        if (currentTenantId == null) {
            log.warn("当前用户没有租户ID");
            return false;
        }
        
        return currentTenantId.equals(targetTenantId);
    }
    
    // 注意：数据权限检查现在由MyBatis-Flex的JCloudDataPermissionDialect自动处理
    // 在查询时会自动应用权限过滤，无需手动检查

    // canAccessTenant方法已在SecurityUtils中实现，避免重复定义
    
    /**
     * 获取当前用户的数据权限范围描述
     * 
     * @return 权限范围描述
     */
    public static String getDataScopeDescription() {
        if (SecurityUtils.isSuperAdmin()) {
            return "超级管理员（全部数据）";
        }
        
        String dataScope = SecurityUtils.getDataScope();
        switch (dataScope) {
            case "ALL":
                return "全部数据权限";
            case "DEPT":
                return "本部门数据权限";
            case "DEPT_AND_SUB":
                return "本部门及子部门数据权限";
            case "SELF":
                return "仅本人数据权限";
            case "CUSTOM":
                return "自定义数据权限";
            default:
                return "未知权限范围";
        }
    }
    
    /**
     * 记录数据权限检查日志
     * 
     * @param operation 操作类型
     * @param targetType 目标类型（user、dept、tenant等）
     * @param targetId 目标ID
     * @param allowed 是否允许
     */
    public static void logDataPermissionCheck(String operation, String targetType, Long targetId, boolean allowed) {
        if (log.isDebugEnabled()) {
            log.debug("数据权限检查: operation={}, targetType={}, targetId={}, allowed={}, " +
                     "currentUser={}, dataScope={}, tenantId={}", 
                     operation, targetType, targetId, allowed,
                     SecurityUtils.getUserId(), SecurityUtils.getDataScope(), SecurityUtils.getTenantId());
        }
    }
    
    /**
     * 验证数据权限并记录日志
     * 
     * @param operation 操作类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param permissionChecker 权限检查函数
     * @return 是否有权限
     */
    public static boolean checkAndLog(String operation, String targetType, Long targetId, 
                                     Supplier<Boolean> permissionChecker) {
        boolean allowed = permissionChecker.get();
        logDataPermissionCheck(operation, targetType, targetId, allowed);
        return allowed;
    }
}
