package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

/**
 * 权限创建请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "权限创建请求")
public class PermissionCreateRequest {
    
    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "system:user:list")
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9:_]*$", message = "权限编码只能包含小写字母、数字、冒号和下划线，且必须以小写字母开头")
    private String permissionCode;
    
    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户列表")
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;
    
    /**
     * 权限类型（MENU-菜单，BUTTON-按钮，API-接口）
     */
    @Schema(description = "权限类型", example = "API")
    @NotBlank(message = "权限类型不能为空")
    @Pattern(regexp = "^(MENU|BUTTON|API)$", message = "权限类型只能是MENU、BUTTON或API")
    private String permissionType;
    
    /**
     * 资源路径
     */
    @Schema(description = "资源路径", example = "/system/user/page")
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Schema(description = "HTTP方法", example = "GET")
    @Size(max = 10, message = "HTTP方法长度不能超过10个字符")
    @Pattern(regexp = "^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)?$", message = "HTTP方法不正确")
    private String method;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "用户管理相关权限")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
