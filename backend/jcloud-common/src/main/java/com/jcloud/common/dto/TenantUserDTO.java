package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户用户DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "租户用户信息")
public class TenantUserDTO {
    
    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    private Long id;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;
    
    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;
    
    /**
     * 用户在租户中的角色（1-管理员，2-普通用户）
     */
    @Schema(description = "用户角色")
    private Integer userRole;
    
    /**
     * 用户角色名称
     */
    @Schema(description = "用户角色名称")
    private String userRoleName;
    
    /**
     * 分配时间
     */
    @Schema(description = "分配时间")
    private LocalDateTime assignTime;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
