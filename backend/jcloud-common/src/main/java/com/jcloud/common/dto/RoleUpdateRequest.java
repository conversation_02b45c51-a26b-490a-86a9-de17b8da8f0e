package com.jcloud.common.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 角色更新请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "角色更新请求")
public class RoleUpdateRequest {
    
    /**
     * 角色ID
     */
    @Schema(description = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long id;
    
    /**
     * 角色名称
     */
    @Schema(description = "角色名称", example = "管理员")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;
    
    /**
     * 数据权限范围（ALL-全部，DEPT-部门，DEPT_AND_SUB-部门及子部门，SELF-仅本人，CUSTOM-自定义）
     */
    @Schema(description = "数据权限范围", example = "SELF")
    @Pattern(regexp = "^(ALL|DEPT|DEPT_AND_SUB|SELF|CUSTOM)$", message = "数据权限范围值不正确")
    private String dataScope;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态", example = "1")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder;
    
    /**
     * 权限ID列表
     */
    @Schema(description = "权限ID列表", example = "[1, 2, 3]")
    private List<Long> permissionIds;
    
    /**
     * 备注
     */
    @Schema(description = "备注", example = "系统管理员角色")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
