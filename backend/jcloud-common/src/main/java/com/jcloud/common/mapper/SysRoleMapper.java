package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysRole;

import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {
    
    /**
     * 根据角色编码查询角色（包含租户过滤）
     * 
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @return 角色信息
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND tenant_id = #{tenantId} AND deleted = 0")
    SysRole selectByRoleCode(@Param("roleCode") String roleCode, @Param("tenantId") Long tenantId);
    
    /**
     * 根据角色编码查询角色（不包含租户过滤，用于系统角色）
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND deleted = 0")
    SysRole selectByRoleCodeWithoutTenant(@Param("roleCode") String roleCode);
    
    /**
     * 根据用户ID查询角色列表
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} " +
            "AND r.status = 1 AND r.deleted = 0 " +
            "ORDER BY r.sort_order ASC, r.create_time ASC")
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
    
    // 注意：权限查询功能已整合到菜单权限服务中
    
    /**
     * 检查角色编码是否存在（排除指定角色ID）
     * 
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_role " +
            "WHERE role_code = #{roleCode} AND tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "AND (#{excludeRoleId} IS NULL OR id != #{excludeRoleId})")
    boolean existsByRoleCode(@Param("roleCode") String roleCode, 
                           @Param("excludeRoleId") Long excludeRoleId, 
                           @Param("tenantId") Long tenantId);
    
    /**
     * 检查角色名称是否存在（排除指定角色ID）
     * 
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_role " +
            "WHERE role_name = #{roleName} AND tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "AND (#{excludeRoleId} IS NULL OR id != #{excludeRoleId})")
    boolean existsByRoleName(@Param("roleName") String roleName, 
                           @Param("excludeRoleId") Long excludeRoleId, 
                           @Param("tenantId") Long tenantId);
    
    /**
     * 统计角色下的用户数量
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(1) FROM sys_user_role ur " +
            "INNER JOIN sys_user u ON ur.user_id = u.id " +
            "WHERE ur.role_id = #{roleId} AND ur.tenant_id = #{tenantId} " +
            "AND u.deleted = 0")
    int countUsersByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);
    
    /**
     * 根据租户ID查询所有启用的角色
     * 
     * @param tenantId 租户ID
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role " +
            "WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 " +
            "ORDER BY sort_order ASC, create_time ASC")
    List<SysRole> selectEnabledRolesByTenantId(@Param("tenantId") Long tenantId);
}
