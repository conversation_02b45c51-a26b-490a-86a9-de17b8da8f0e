package com.jcloud.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控工具
 * 用于监控API响应时间、数据库查询性能等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PerformanceMonitor {

    /**
     * 请求计数器
     */
    private final ConcurrentHashMap<String, LongAdder> requestCounters = new ConcurrentHashMap<>();
    
    /**
     * 响应时间统计
     */
    private final ConcurrentHashMap<String, ResponseTimeStats> responseTimeStats = new ConcurrentHashMap<>();
    
    /**
     * 错误计数器
     */
    private final ConcurrentHashMap<String, LongAdder> errorCounters = new ConcurrentHashMap<>();

    /**
     * 响应时间统计类
     */
    public static class ResponseTimeStats {
        private final AtomicLong totalTime = new AtomicLong(0);
        private final AtomicLong count = new AtomicLong(0);
        private final AtomicLong maxTime = new AtomicLong(0);
        private final AtomicLong minTime = new AtomicLong(Long.MAX_VALUE);

        public void addTime(long time) {
            totalTime.addAndGet(time);
            count.incrementAndGet();
            
            // 更新最大值
            long currentMax = maxTime.get();
            while (time > currentMax && !maxTime.compareAndSet(currentMax, time)) {
                currentMax = maxTime.get();
            }
            
            // 更新最小值
            long currentMin = minTime.get();
            while (time < currentMin && !minTime.compareAndSet(currentMin, time)) {
                currentMin = minTime.get();
            }
        }

        public double getAverageTime() {
            long c = count.get();
            return c == 0 ? 0 : (double) totalTime.get() / c;
        }

        public long getMaxTime() {
            return maxTime.get() == 0 ? 0 : maxTime.get();
        }

        public long getMinTime() {
            long min = minTime.get();
            return min == Long.MAX_VALUE ? 0 : min;
        }

        public long getCount() {
            return count.get();
        }

        public long getTotalTime() {
            return totalTime.get();
        }
    }

    /**
     * 记录请求开始
     */
    public long startRequest(String endpoint) {
        requestCounters.computeIfAbsent(endpoint, k -> new LongAdder()).increment();
        return System.currentTimeMillis();
    }

    /**
     * 记录请求结束
     */
    public void endRequest(String endpoint, long startTime) {
        long responseTime = System.currentTimeMillis() - startTime;
        responseTimeStats.computeIfAbsent(endpoint, k -> new ResponseTimeStats()).addTime(responseTime);
        
        // 记录慢请求
        if (responseTime > 1000) { // 超过1秒的请求
            log.warn("慢请求检测: {} 耗时 {}ms", endpoint, responseTime);
        } else if (responseTime > 500) { // 超过500ms的请求
            log.info("性能提醒: {} 耗时 {}ms", endpoint, responseTime);
        }
    }

    /**
     * 记录错误
     */
    public void recordError(String endpoint) {
        errorCounters.computeIfAbsent(endpoint, k -> new LongAdder()).increment();
    }

    /**
     * 获取请求统计
     */
    public RequestStats getRequestStats(String endpoint) {
        LongAdder counter = requestCounters.get(endpoint);
        ResponseTimeStats timeStats = responseTimeStats.get(endpoint);
        LongAdder errorCounter = errorCounters.get(endpoint);
        
        return new RequestStats(
            endpoint,
            counter != null ? counter.sum() : 0,
            timeStats != null ? timeStats.getAverageTime() : 0,
            timeStats != null ? timeStats.getMaxTime() : 0,
            timeStats != null ? timeStats.getMinTime() : 0,
            errorCounter != null ? errorCounter.sum() : 0
        );
    }

    /**
     * 获取所有统计信息
     */
    public void printAllStats() {
        log.info("=== 性能监控统计 ===");
        
        for (String endpoint : requestCounters.keySet()) {
            RequestStats stats = getRequestStats(endpoint);
            log.info("接口: {} | 请求数: {} | 平均响应时间: {:.2f}ms | 最大响应时间: {}ms | 最小响应时间: {}ms | 错误数: {}",
                stats.endpoint, stats.requestCount, stats.averageResponseTime, 
                stats.maxResponseTime, stats.minResponseTime, stats.errorCount);
        }
    }

    /**
     * 清空统计数据
     */
    public void clearStats() {
        requestCounters.clear();
        responseTimeStats.clear();
        errorCounters.clear();
        log.info("性能监控统计数据已清空");
    }

    /**
     * 请求统计数据类
     */
    public static class RequestStats {
        public final String endpoint;
        public final long requestCount;
        public final double averageResponseTime;
        public final long maxResponseTime;
        public final long minResponseTime;
        public final long errorCount;

        public RequestStats(String endpoint, long requestCount, double averageResponseTime, 
                          long maxResponseTime, long minResponseTime, long errorCount) {
            this.endpoint = endpoint;
            this.requestCount = requestCount;
            this.averageResponseTime = averageResponseTime;
            this.maxResponseTime = maxResponseTime;
            this.minResponseTime = minResponseTime;
            this.errorCount = errorCount;
        }
    }

    /**
     * 获取系统性能概览
     */
    public SystemPerformanceOverview getSystemOverview() {
        long totalRequests = requestCounters.values().stream().mapToLong(LongAdder::sum).sum();
        long totalErrors = errorCounters.values().stream().mapToLong(LongAdder::sum).sum();
        
        double overallAverageTime = responseTimeStats.values().stream()
            .mapToDouble(ResponseTimeStats::getAverageTime)
            .average()
            .orElse(0.0);
            
        long maxResponseTime = responseTimeStats.values().stream()
            .mapToLong(ResponseTimeStats::getMaxTime)
            .max()
            .orElse(0);

        return new SystemPerformanceOverview(totalRequests, totalErrors, overallAverageTime, maxResponseTime);
    }

    /**
     * 系统性能概览类
     */
    public static class SystemPerformanceOverview {
        public final long totalRequests;
        public final long totalErrors;
        public final double averageResponseTime;
        public final long maxResponseTime;

        public SystemPerformanceOverview(long totalRequests, long totalErrors, 
                                       double averageResponseTime, long maxResponseTime) {
            this.totalRequests = totalRequests;
            this.totalErrors = totalErrors;
            this.averageResponseTime = averageResponseTime;
            this.maxResponseTime = maxResponseTime;
        }
    }
}
