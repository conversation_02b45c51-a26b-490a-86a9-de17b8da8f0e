package com.jcloud.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户登录响应")
public class LoginResponse {
    
    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌")
    private String accessToken;
    
    /**
     * 令牌类型
     */
    @Schema(description = "令牌类型", example = "Bearer")
    @Builder.Default
    private String tokenType = "Bearer";
    
    /**
     * 令牌过期时间（秒）
     */
    @Schema(description = "令牌过期时间（秒）")
    private Long expiresIn;
    
    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserInfo userInfo;
    
    /**
     * 用户权限列表
     */
    @Schema(description = "用户权限列表")
    private List<String> permissions;
    
    /**
     * 用户角色列表
     */
    @Schema(description = "用户角色列表")
    private List<String> roles;
    
    /**
     * 用户信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户信息")
    public static class UserInfo {
        
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;
        
        /**
         * 用户名
         */
        @Schema(description = "用户名")
        private String username;
        
        /**
         * 昵称
         */
        @Schema(description = "昵称")
        private String nickname;
        
        /**
         * 真实姓名
         */
        @Schema(description = "真实姓名")
        private String realName;
        
        /**
         * 头像
         */
        @Schema(description = "头像")
        private String avatar;
        
        /**
         * 邮箱
         */
        @Schema(description = "邮箱")
        private String email;
        
        /**
         * 手机号
         */
        @Schema(description = "手机号")
        private String phone;
        
        /**
         * 租户ID
         */
        @Schema(description = "租户ID")
        private Long tenantId;
        
        /**
         * 租户名称
         */
        @Schema(description = "租户名称")
        private String tenantName;
        
        /**
         * 部门ID
         */
        @Schema(description = "部门ID")
        private Long deptId;
        
        /**
         * 部门名称
         */
        @Schema(description = "部门名称")
        private String deptName;
        
        /**
         * 是否管理员
         */
        @Schema(description = "是否管理员")
        private Boolean isAdmin;
        
        /**
         * 最后登录时间
         */
        @Schema(description = "最后登录时间")
        private LocalDateTime lastLoginTime;
        
        /**
         * 最后登录IP
         */
        @Schema(description = "最后登录IP")
        private String lastLoginIp;
    }
}
