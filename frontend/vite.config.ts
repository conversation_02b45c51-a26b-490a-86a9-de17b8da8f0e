import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import path from 'path'

// https://vite.dev/config/ - 基于Context7 Tailwind CSS v4最佳实践
export default defineConfig({
  plugins: [
    tailwindcss(), // Tailwind CSS v4 Vite插件
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // 生产环境构建优化
  build: {
    // 输出目录
    outDir: 'dist',
    // 生成源码映射文件（生产环境可选）
    sourcemap: false,
    // 构建后清理输出目录
    emptyOutDir: true,
    // 启用/禁用 CSS 代码拆分
    cssCodeSplit: true,
    // 构建后的静态资源基础路径
    assetsDir: 'assets',
    // 小于此阈值的导入或引用资源将内联为 base64 编码
    assetsInlineLimit: 4096,
    // Rollup 打包配置
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          // 将 React 相关库打包到一个 chunk
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // 将 UI 组件库打包到一个 chunk
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-select', '@radix-ui/react-tabs'],
          // 将工具库打包到一个 chunk
          'utils-vendor': ['axios', 'zustand', 'date-fns', 'clsx']
        },
        // 文件命名策略
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 生产环境移除 console
        drop_console: true,
        drop_debugger: true,
      },
    },
    // 启用 gzip 压缩大小报告
    reportCompressedSize: true,
    // chunk 大小警告的限制（以 kbs 为单位）
    chunkSizeWarningLimit: 1000,
  },
  server: {
    port: 5174,
    host: true,
    proxy: {
      // API代理配置 - 将 /api/* 请求代理到后端服务器
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        // 保持原始路径，不重写
        // rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  // 预览服务器配置（用于预览生产构建）
  preview: {
    port: 4173,
    host: true,
  },
  // 环境变量配置
  define: {
    // 在生产环境中定义全局常量
    __DEV__: JSON.stringify(false),
  },
})
