/**
 * 认证相关工具函数
 * 
 * 处理登录状态检查、token管理和自动跳转
 */

import { toast } from '@/hooks'

// 登录状态相关的错误码和消息
export const AUTH_ERROR_CODES = {
  UNAUTHORIZED: 401,
  TOKEN_EXPIRED: 403,
  LOGIN_REQUIRED: 'LOGIN_REQUIRED'
}

export const AUTH_ERROR_MESSAGES = [
  '用户未登录或登录已失效',
  '登录状态异常，请重新登录',
  '登录已过期，请重新登录',
  '未授权访问，请先登录',
  'Token已过期',
  '登录失效',
  'Unauthorized',
  'Token expired',
  'Authentication failed'
]

/**
 * 检查是否为认证错误
 */
export const isAuthError = (error: any): boolean => {
  // 检查HTTP状态码
  if (error.status === AUTH_ERROR_CODES.UNAUTHORIZED || 
      error.status === AUTH_ERROR_CODES.TOKEN_EXPIRED) {
    return true
  }

  // 检查错误消息
  const errorMessage = error.message || error.msg || error.data?.message || ''
  return AUTH_ERROR_MESSAGES.some(msg => 
    errorMessage.toLowerCase().includes(msg.toLowerCase())
  )
}

/**
 * 处理认证错误
 */
export const handleAuthError = (error: any) => {
  console.warn('检测到认证错误，准备跳转到登录页:', error)
  
  // 显示提示消息
  toast({
    title: '登录已失效',
    description: '您的登录状态已失效，请重新登录',
    variant: 'destructive',
    duration: 3000
  })
  
  // 清除本地存储的认证信息
  clearAuthData()
  
  // 延迟跳转，让用户看到提示消息
  setTimeout(() => {
    redirectToLogin()
  }, 1000)
}

/**
 * 清除认证数据
 */
export const clearAuthData = () => {
  // 导入STORAGE_KEYS以保持一致性
  const STORAGE_KEYS = {
    TOKEN: 'jcloud_token',
    USER_INFO: 'jcloud_user_info',
    PERMISSIONS: 'jcloud_permissions',
    ROLES: 'jcloud_roles',
    MENUS: 'jcloud_menus',
    TENANT_ID: 'jcloud_tenant_id'
  }

  // 清除localStorage中的认证信息
  localStorage.removeItem(STORAGE_KEYS.TOKEN)
  localStorage.removeItem('token') // 兼容旧版本
  localStorage.removeItem('refreshToken')
  localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  localStorage.removeItem('userInfo') // 兼容旧版本
  localStorage.removeItem(STORAGE_KEYS.PERMISSIONS)
  localStorage.removeItem('permissions') // 兼容旧版本

  // 清除sessionStorage中的认证信息
  sessionStorage.removeItem(STORAGE_KEYS.TOKEN)
  sessionStorage.removeItem('token') // 兼容旧版本
  sessionStorage.removeItem(STORAGE_KEYS.USER_INFO)
  sessionStorage.removeItem('userInfo') // 兼容旧版本

  // 清除cookies中的认证信息
  document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
}

/**
 * 跳转到登录页面
 */
export const redirectToLogin = () => {
  const currentPath = window.location.pathname + window.location.search
  
  // 如果当前不在登录页面，则跳转到登录页面
  if (!currentPath.includes('/login')) {
    // 保存当前页面路径，登录成功后可以跳转回来
    localStorage.setItem('redirectPath', currentPath)
    
    // 跳转到登录页面
    window.location.href = '/login'
  }
}

/**
 * 获取保存的重定向路径
 */
export const getRedirectPath = (): string => {
  const redirectPath = localStorage.getItem('redirectPath')
  localStorage.removeItem('redirectPath')
  return redirectPath || '/'
}

/**
 * 检查当前是否已登录
 */
export const isLoggedIn = (): boolean => {
  const STORAGE_KEYS = { TOKEN: 'jcloud_token' }
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN) ||
                sessionStorage.getItem(STORAGE_KEYS.TOKEN) ||
                localStorage.getItem('token') || // 兼容旧版本
                sessionStorage.getItem('token') // 兼容旧版本
  return !!token
}

/**
 * 获取当前用户token
 */
export const getToken = (): string | null => {
  const STORAGE_KEYS = { TOKEN: 'jcloud_token' }
  return localStorage.getItem(STORAGE_KEYS.TOKEN) ||
         sessionStorage.getItem(STORAGE_KEYS.TOKEN) ||
         localStorage.getItem('token') || // 兼容旧版本
         sessionStorage.getItem('token') // 兼容旧版本
}

/**
 * 设置用户token
 */
export const setToken = (token: string, remember: boolean = false) => {
  const STORAGE_KEYS = { TOKEN: 'jcloud_token' }
  if (remember) {
    localStorage.setItem(STORAGE_KEYS.TOKEN, token)
    // 清除旧版本的token
    localStorage.removeItem('token')
  } else {
    sessionStorage.setItem(STORAGE_KEYS.TOKEN, token)
    // 清除旧版本的token
    sessionStorage.removeItem('token')
  }
}

/**
 * 创建带有认证错误处理的fetch包装器
 */
export const authFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  // 添加认证头
  const token = getToken()
  if (token) {
    options.headers = {
      ...options.headers,
      'Authorization': `Bearer ${token}`
    }
  }

  try {
    const response = await fetch(url, options)
    
    // 检查响应状态
    if (response.status === AUTH_ERROR_CODES.UNAUTHORIZED || 
        response.status === AUTH_ERROR_CODES.TOKEN_EXPIRED) {
      handleAuthError({ status: response.status })
      throw new Error('Authentication failed')
    }
    
    // 检查响应内容中的认证错误
    if (response.ok) {
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        const data = await response.clone().json()
        if (data && !data.success && isAuthError(data)) {
          handleAuthError(data)
          throw new Error(data.message || 'Authentication failed')
        }
      }
    }
    
    return response
  } catch (error) {
    // 检查网络错误中的认证问题
    if (isAuthError(error)) {
      handleAuthError(error)
    }
    throw error
  }
}

/**
 * 创建带有认证错误处理的API调用函数
 */
export const apiCall = async <T = any>(
  url: string, 
  options: RequestInit = {}
): Promise<{ success: boolean; data: T; message: string }> => {
  try {
    const response = await authFetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
    
    const result = await response.json()
    
    // 再次检查结果中的认证错误
    if (!result.success && isAuthError(result)) {
      handleAuthError(result)
      throw new Error(result.message || 'Authentication failed')
    }
    
    return result
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}
