import React from 'react'
import * as LucideIcons from 'lucide-react'

/**
 * 图标工具类
 */
export class IconUtils {
  /**
   * 渲染Lucide图标
   * @param iconName 图标名称
   * @param size 图标大小
   * @param className 自定义类名
   * @returns React组件或null
   */
  static renderIcon(iconName?: string, size = 16, className?: string): React.ReactNode {
    if (!iconName) return null
    
    const IconComponent = LucideIcons[iconName as keyof typeof LucideIcons] as React.ComponentType<{
      size?: number
      className?: string
    }>
    
    if (!IconComponent || typeof IconComponent !== 'function') {
      return null
    }
    
    return <IconComponent size={size} className={className} />
  }

  /**
   * 检查图标是否存在
   * @param iconName 图标名称
   * @returns 是否存在
   */
  static iconExists(iconName: string): boolean {
    return iconName in LucideIcons && typeof LucideIcons[iconName as keyof typeof LucideIcons] === 'function'
  }

  /**
   * 获取所有可用图标名称
   * @returns 图标名称数组
   */
  static getAllIconNames(): string[] {
    return Object.keys(LucideIcons).filter(
      name => name !== 'createLucideIcon' && 
              name !== 'Icon' && 
              typeof LucideIcons[name as keyof typeof LucideIcons] === 'function'
    )
  }

  /**
   * 搜索图标
   * @param keyword 搜索关键词
   * @returns 匹配的图标名称数组
   */
  static searchIcons(keyword: string): string[] {
    if (!keyword) return this.getAllIconNames()
    
    const lowerKeyword = keyword.toLowerCase()
    return this.getAllIconNames().filter(iconName =>
      iconName.toLowerCase().includes(lowerKeyword)
    )
  }
}
