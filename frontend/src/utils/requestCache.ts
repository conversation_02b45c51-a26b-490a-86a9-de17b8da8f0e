/**
 * 请求缓存工具
 * 实现前端请求缓存，减少重复API调用
 */

interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
}

interface RequestCacheOptions {
  ttl?: number // 缓存时间（毫秒）
  key?: string // 自定义缓存键
  force?: boolean // 强制刷新缓存
}

class RequestCache {
  private cache = new Map<string, CacheItem>()
  private pendingRequests = new Map<string, Promise<any>>()

  /**
   * 生成缓存键
   */
  private generateKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${url}:${paramStr}`
  }

  /**
   * 检查缓存是否有效
   */
  private isValid(item: CacheItem): boolean {
    return Date.now() - item.timestamp < item.ttl
  }

  /**
   * 获取缓存数据
   */
  get<T = any>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (!this.isValid(item)) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }

  /**
   * 设置缓存数据
   */
  set<T = any>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 删除缓存
   */
  delete(key: string): void {
    this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
    this.pendingRequests.clear()
  }

  /**
   * 缓存请求
   */
  async request<T = any>(
    requestFn: () => Promise<T>,
    url: string,
    params?: any,
    options: RequestCacheOptions = {}
  ): Promise<T> {
    const {
      ttl = 5 * 60 * 1000, // 默认5分钟
      key: customKey,
      force = false
    } = options

    const cacheKey = customKey || this.generateKey(url, params)

    // 如果不强制刷新，先检查缓存
    if (!force) {
      const cached = this.get<T>(cacheKey)
      if (cached !== null) {
        console.log('🎯 使用缓存数据:', cacheKey)
        return cached
      }
    }

    // 检查是否有相同的请求正在进行
    const pendingRequest = this.pendingRequests.get(cacheKey)
    if (pendingRequest && !force) {
      console.log('⏳ 等待进行中的请求:', cacheKey)
      return pendingRequest
    }

    // 发起新请求
    console.log('🚀 发起新请求:', cacheKey)
    const requestPromise = requestFn()
      .then(data => {
        // 缓存成功的响应
        this.set(cacheKey, data, ttl)
        this.pendingRequests.delete(cacheKey)
        return data
      })
      .catch(error => {
        // 清理失败的请求
        this.pendingRequests.delete(cacheKey)
        throw error
      })

    this.pendingRequests.set(cacheKey, requestPromise)
    return requestPromise
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    // const currentTime = Date.now()
    let validCount = 0
    let expiredCount = 0

    for (const [key, item] of this.cache.entries()) {
      if (this.isValid(item)) {
        validCount++
      } else {
        expiredCount++
        this.cache.delete(key) // 清理过期缓存
      }
    }

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
      pending: this.pendingRequests.size
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    // const currentTime = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (!this.isValid(item)) {
        this.cache.delete(key)
      }
    }
  }
}

// 创建全局缓存实例
export const requestCache = new RequestCache()

// 定期清理过期缓存
setInterval(() => {
  requestCache.cleanup()
}, 60000) // 每分钟清理一次

// 导出类型
export type { RequestCacheOptions }
export { RequestCache }
