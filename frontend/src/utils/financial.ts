/**
 * 财务数据格式化工具函数
 * 提供货币、百分比、时间等数据的格式化功能
 */

import Decimal from 'decimal.js'
import type { TimeRange } from '@/types/financial'
import { TimeRangePreset } from '@/types/financial'

// 配置Decimal.js精度
Decimal.set({ precision: 28, rounding: 4 })

/**
 * 货币格式化选项
 */
export interface CurrencyFormatOptions {
  /** 货币符号 */
  symbol?: string
  /** 小数位数 */
  decimals?: number
  /** 是否显示千分位分隔符 */
  showThousandsSeparator?: boolean
  /** 千分位分隔符 */
  thousandsSeparator?: string
  /** 小数点分隔符 */
  decimalSeparator?: string
  /** 货币符号位置 */
  symbolPosition?: 'before' | 'after'
}

/**
 * 百分比格式化选项
 */
export interface PercentageFormatOptions {
  /** 小数位数 */
  decimals?: number
  /** 是否显示百分号 */
  showSymbol?: boolean
  /** 乘以100转换 */
  multiply?: boolean
}

/**
 * 时间格式化选项
 */
export interface TimeFormatOptions {
  /** 格式化模式 */
  format?: string
  /** 时区 */
  timezone?: string
  /** 是否显示相对时间 */
  relative?: boolean
}

/**
 * 格式化货币金额
 * @param value 数值
 * @param options 格式化选项
 * @returns 格式化后的货币字符串
 */
export const formatCurrency = (
  value: number | string | null | undefined,
  options: CurrencyFormatOptions = {}
): string => {
  const {
    symbol = '¥',
    decimals = 2,
    showThousandsSeparator = true,
    thousandsSeparator = ',',
    decimalSeparator = '.',
    symbolPosition = 'before'
  } = options

  // 处理空值
  if (value === null || value === undefined || value === '') {
    return symbolPosition === 'before' ? `${symbol}0` : `0${symbol}`
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  
  // 处理无效数字
  if (isNaN(numValue)) {
    return symbolPosition === 'before' ? `${symbol}0` : `0${symbol}`
  }

  // 格式化数字
  let formattedValue = Math.abs(numValue).toFixed(decimals)
  
  // 添加千分位分隔符
  if (showThousandsSeparator) {
    const parts = formattedValue.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator)
    formattedValue = parts.join(decimalSeparator)
  }

  // 处理负数
  const sign = numValue < 0 ? '-' : ''
  
  // 添加货币符号
  const result = symbolPosition === 'before' 
    ? `${sign}${symbol}${formattedValue}`
    : `${sign}${formattedValue}${symbol}`

  return result
}

/**
 * 格式化百分比
 * @param value 数值
 * @param options 格式化选项
 * @returns 格式化后的百分比字符串
 */
export const formatPercentage = (
  value: number | string | null | undefined,
  options: PercentageFormatOptions = {}
): string => {
  const {
    decimals = 2,
    showSymbol = true,
    multiply = true
  } = options

  // 处理空值
  if (value === null || value === undefined || value === '') {
    return showSymbol ? '0%' : '0'
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value

  // 处理无效数字
  if (isNaN(numValue)) {
    return showSymbol ? '0%' : '0'
  }

  // 计算百分比值
  const percentValue = multiply ? numValue * 1 : numValue

  // 格式化数字
  const formattedValue = percentValue.toFixed(decimals)

  return showSymbol ? `${formattedValue}%` : formattedValue
}

/**
 * 格式化返奖率（专用函数）
 * 后端已经计算为百分比，前端不需要再乘以100
 * @param value 返奖率数值（已经是百分比）
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的返奖率字符串
 */
export const formatReturnRate = (
  value: number | string | null | undefined,
  decimals: number = 2
): string => {
  return formatPercentage(value, {
    decimals,
    showSymbol: true,
    multiply: false // 关键：不再乘以100
  })
}

/**
 * 精确计算工具函数
 * 使用Decimal.js进行高精度计算
 */
export const preciseCalculation = {
  /**
   * 精确加法
   */
  add: (a: number | string, b: number | string): number => {
    return new Decimal(a).add(new Decimal(b)).toNumber()
  },

  /**
   * 精确减法
   */
  subtract: (a: number | string, b: number | string): number => {
    return new Decimal(a).sub(new Decimal(b)).toNumber()
  },

  /**
   * 精确乘法
   */
  multiply: (a: number | string, b: number | string): number => {
    return new Decimal(a).mul(new Decimal(b)).toNumber()
  },

  /**
   * 精确除法
   */
  divide: (a: number | string, b: number | string): number => {
    if (new Decimal(b).isZero()) {
      throw new Error('除数不能为零')
    }
    return new Decimal(a).div(new Decimal(b)).toNumber()
  },

  /**
   * 精确百分比计算
   */
  percentage: (part: number | string, total: number | string): number => {
    if (new Decimal(total).isZero()) {
      return 0
    }
    return new Decimal(part).div(new Decimal(total)).mul(100).toNumber()
  }
}

/**
 * 格式化数字（添加千分位分隔符）
 * @param value 数值
 * @param decimals 小数位数
 * @param separator 千分位分隔符
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (
  value: number | string | null | undefined,
  decimals: number = 0,
  separator: string = ','
): string => {
  // 处理空值
  if (value === null || value === undefined || value === '') {
    return '0'
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  
  // 处理无效数字
  if (isNaN(numValue)) {
    return '0'
  }

  // 格式化数字
  const formattedValue = numValue.toFixed(decimals)
  
  // 添加千分位分隔符
  const parts = formattedValue.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  
  return parts.join('.')
}

/**
 * 格式化时间
 * @param value 时间值
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  value: Date | string | number | null | undefined,
  options: TimeFormatOptions = {}
): string => {
  const {
    format = 'YYYY-MM-DD HH:mm:ss',
    relative = false
  } = options

  // 处理空值
  if (value === null || value === undefined) {
    return ''
  }

  // 转换为Date对象
  const date = new Date(value)
  
  // 处理无效日期
  if (isNaN(date.getTime())) {
    return ''
  }

  // 相对时间格式化
  if (relative) {
    return formatRelativeTime(date)
  }

  // 标准时间格式化
  return formatDateTime(date, format)
}

/**
 * 格式化相对时间
 * @param date 日期对象
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffSeconds < 60) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return formatDateTime(date, 'YYYY-MM-DD')
  }
}

/**
 * 格式化日期时间
 * @param date 日期对象
 * @param format 格式化模式
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: Date, format: string): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 验证时间范围
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 验证结果
 */
export const validateTimeRange = (startTime: string, endTime: string): {
  valid: boolean
  message?: string
} => {
  // 检查时间格式
  const startDate = new Date(startTime)
  const endDate = new Date(endTime)

  if (isNaN(startDate.getTime())) {
    return { valid: false, message: '开始时间格式无效' }
  }

  if (isNaN(endDate.getTime())) {
    return { valid: false, message: '结束时间格式无效' }
  }

  // 检查时间顺序
  if (startDate >= endDate) {
    return { valid: false, message: '开始时间必须早于结束时间' }
  }

  // 检查时间范围是否过大（超过1年）
  const diffMs = endDate.getTime() - startDate.getTime()
  const diffDays = diffMs / (1000 * 60 * 60 * 24)
  
  if (diffDays > 365) {
    return { valid: false, message: '查询时间范围不能超过1年' }
  }

  return { valid: true }
}

/**
 * 获取预设时间范围
 * @param preset 预设类型
 * @returns 时间范围对象
 */
export const getPresetTimeRange = (preset: TimeRangePreset): TimeRange => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  switch (preset) {
    case TimeRangePreset.TODAY:
      return {
        startTime: formatDateTime(today, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1), 'YYYY-MM-DD 23:59:59')
      }
      
    case TimeRangePreset.YESTERDAY: {
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      return {
        startTime: formatDateTime(yesterday, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1), 'YYYY-MM-DD 23:59:59')
      }
    }

    case TimeRangePreset.THIS_WEEK: {
      const thisWeekStart = new Date(today)
      thisWeekStart.setDate(today.getDate() - today.getDay() + 1) // 周一
      const thisWeekEnd = new Date(thisWeekStart)
      thisWeekEnd.setDate(thisWeekStart.getDate() + 6) // 周日
      return {
        startTime: formatDateTime(thisWeekStart, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(thisWeekEnd, 'YYYY-MM-DD 23:59:59')
      }
    }

    case TimeRangePreset.LAST_WEEK: {
      const lastWeekStart = new Date(today)
      lastWeekStart.setDate(today.getDate() - today.getDay() - 6) // 上周一
      const lastWeekEnd = new Date(lastWeekStart)
      lastWeekEnd.setDate(lastWeekStart.getDate() + 6) // 上周日
      return {
        startTime: formatDateTime(lastWeekStart, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(lastWeekEnd, 'YYYY-MM-DD 23:59:59')
      }
    }

    case TimeRangePreset.THIS_MONTH: {
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return {
        startTime: formatDateTime(thisMonthStart, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(thisMonthEnd, 'YYYY-MM-DD 23:59:59')
      }
    }

    case TimeRangePreset.LAST_MONTH: {
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)
      return {
        startTime: formatDateTime(lastMonthStart, 'YYYY-MM-DD 00:00:00'),
        endTime: formatDateTime(lastMonthEnd, 'YYYY-MM-DD 23:59:59')
      }
    }
      
    default:
      // 默认返回今天
      return getPresetTimeRange(TimeRangePreset.TODAY)
  }
}

/**
 * 格式化时间范围显示文本
 * @param timeRange 时间范围
 * @returns 显示文本
 */
export const formatTimeRangeText = (timeRange: TimeRange): string => {
  const startDate = new Date(timeRange.startTime)
  const endDate = new Date(timeRange.endTime)
  
  // 检查是否为同一天
  if (formatDateTime(startDate, 'YYYY-MM-DD') === formatDateTime(endDate, 'YYYY-MM-DD')) {
    return formatDateTime(startDate, 'YYYY-MM-DD')
  }
  
  return `${formatDateTime(startDate, 'YYYY-MM-DD')} 至 ${formatDateTime(endDate, 'YYYY-MM-DD')}`
}

/**
 * 转换时间格式为API所需格式
 * @param timeString 时间字符串
 * @returns API格式的时间字符串
 */
export const formatTimeForApi = (timeString: string): string => {
  const date = new Date(timeString)
  if (isNaN(date.getTime())) {
    throw new Error('无效的时间格式')
  }
  return formatDateTime(date, 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 智能格式化数值
 * 根据数值大小自动选择合适的格式化方式
 * @param value 数值
 * @param type 数据类型
 * @param statName 统计项名称，用于特殊处理
 * @returns 格式化后的字符串
 */
export const smartFormatValue = (
  value: number | string | null | undefined,
  type: 'currency' | 'percentage' | 'number' | 'integer' = 'number',
  statName?: string
): string => {
  if (value === null || value === undefined || value === '') {
    return '-'
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value

  if (isNaN(numValue)) {
    return '-'
  }

  // 特殊处理返奖率
  if (statName && statName.includes('返奖率')) {
    return formatReturnRate(numValue)
  }

  switch (type) {
    case 'currency':
      return formatCurrency(numValue)
    case 'percentage':
      return formatPercentage(numValue)
    case 'integer':
      return formatNumber(numValue, 0)
    case 'number':
    default:
      // 根据数值大小决定小数位数
      if (Math.abs(numValue) >= 1000000) {
        return formatNumber(numValue, 0) // 大数值不显示小数
      } else if (Math.abs(numValue) >= 1000) {
        return formatNumber(numValue, 1) // 中等数值显示1位小数
      } else {
        return formatNumber(numValue, 2) // 小数值显示2位小数
      }
  }
}