/**
 * 增强的错误处理工具
 * 
 * 为重构后的HTTP响应处理提供统一的错误处理机制
 */

import type { ApiError } from '../types/http'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  /**
   * 处理各种类型的错误，统一转换为ApiError
   */
  static handle(error: any): ApiError {
    // 如果已经是ApiError，直接返回
    if (this.isApiError(error)) {
      return error
    }

    // 处理网络错误
    if (this.isNetworkError(error)) {
      return this.createApiError(
        '网络连接失败，请检查网络设置',
        0,
        ErrorType.NETWORK_ERROR,
        error
      )
    }

    // 处理axios错误
    if (this.isAxiosError(error)) {
      return this.handleAxiosError(error)
    }

    // 处理业务错误（旧版响应格式）
    if (this.isLegacyBusinessError(error)) {
      return this.handleLegacyBusinessError(error)
    }

    // 处理普通Error对象
    if (error instanceof Error) {
      return this.createApiError(
        error.message || '未知错误',
        500,
        ErrorType.UNKNOWN_ERROR,
        error
      )
    }

    // 处理其他类型的错误
    return this.createApiError(
      '发生未知错误',
      500,
      ErrorType.UNKNOWN_ERROR,
      error
    )
  }

  /**
   * 检查是否为ApiError
   */
  static isApiError(error: any): error is ApiError {
    return (
      error instanceof Error &&
      'code' in error &&
      typeof error.code === 'number'
    )
  }

  /**
   * 检查是否为网络错误
   */
  static isNetworkError(error: any): boolean {
    return (
      error?.code === 'NETWORK_ERROR' ||
      error?.message?.includes('Network Error') ||
      error?.message?.includes('timeout') ||
      !navigator.onLine
    )
  }

  /**
   * 检查是否为axios错误
   */
  static isAxiosError(error: any): boolean {
    return error?.isAxiosError === true
  }

  /**
   * 检查是否为旧版业务错误
   */
  static isLegacyBusinessError(error: any): boolean {
    return (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      'message' in error &&
      'data' in error
    )
  }

  /**
   * 处理axios错误
   */
  static handleAxiosError(error: any): ApiError {
    const { response, request, message } = error

    if (response) {
      // 服务器响应了错误状态码
      const { status, data } = response
      
      switch (status) {
        case 400:
          return this.createApiError(
            data?.message || '请求参数错误',
            400,
            ErrorType.VALIDATION_ERROR,
            data
          )
        case 401:
          return this.createApiError(
            '登录已过期，请重新登录',
            401,
            ErrorType.AUTH_ERROR,
            data
          )
        case 403:
          return this.createApiError(
            '没有权限访问该资源',
            403,
            ErrorType.PERMISSION_ERROR,
            data
          )
        case 404:
          return this.createApiError(
            '请求的资源不存在',
            404,
            ErrorType.BUSINESS_ERROR,
            data
          )
        case 422:
          return this.createApiError(
            '数据验证失败',
            422,
            ErrorType.VALIDATION_ERROR,
            data
          )
        case 500:
        case 502:
        case 503:
        case 504:
          return this.createApiError(
            '服务器错误，请稍后重试',
            status,
            ErrorType.SERVER_ERROR,
            data
          )
        default:
          return this.createApiError(
            data?.message || `请求失败 (${status})`,
            status,
            ErrorType.BUSINESS_ERROR,
            data
          )
      }
    } else if (request) {
      // 请求已发送但没有收到响应
      return this.createApiError(
        '服务器无响应，请检查网络连接',
        0,
        ErrorType.NETWORK_ERROR,
        request
      )
    } else {
      // 请求配置错误
      return this.createApiError(
        message || '请求配置错误',
        0,
        ErrorType.UNKNOWN_ERROR,
        error
      )
    }
  }

  /**
   * 处理旧版业务错误
   */
  static handleLegacyBusinessError(error: any): ApiError {
    const { code, message, data } = error
    
    let errorType = ErrorType.BUSINESS_ERROR
    
    // 根据错误代码确定错误类型
    if (code === 401) {
      errorType = ErrorType.AUTH_ERROR
    } else if (code === 403) {
      errorType = ErrorType.PERMISSION_ERROR
    } else if (code >= 400 && code < 500) {
      errorType = ErrorType.VALIDATION_ERROR
    } else if (code >= 500) {
      errorType = ErrorType.SERVER_ERROR
    }

    return this.createApiError(
      message || '业务处理失败',
      code,
      errorType,
      data
    )
  }

  /**
   * 创建ApiError对象
   */
  static createApiError(
    message: string,
    code: number,
    type: ErrorType,
    originalData?: any
  ): ApiError {
    const error = new Error(message) as ApiError
    error.code = code
    error.data = originalData
    error.timestamp = new Date().toISOString()
    error.name = type
    return error
  }

  /**
   * 获取用户友好的错误消息
   */
  static getErrorMessage(error: any): string {
    if (this.isApiError(error)) {
      return error.message
    }

    if (this.isNetworkError(error)) {
      return '网络连接失败，请检查网络设置'
    }

    if (error instanceof Error) {
      return error.message
    }

    return '发生未知错误，请稍后重试'
  }

  /**
   * 获取错误类型
   */
  static getErrorType(error: any): ErrorType {
    if (this.isApiError(error)) {
      return error.name as ErrorType || ErrorType.UNKNOWN_ERROR
    }

    if (this.isNetworkError(error)) {
      return ErrorType.NETWORK_ERROR
    }

    return ErrorType.UNKNOWN_ERROR
  }

  /**
   * 判断是否为可重试的错误
   */
  static isRetryableError(error: any): boolean {
    if (!this.isApiError(error)) {
      return false
    }

    const retryableCodes = [0, 408, 429, 500, 502, 503, 504]
    return retryableCodes.includes(error.code)
  }

  /**
   * 判断是否需要重新登录
   */
  static shouldRelogin(error: any): boolean {
    return this.isApiError(error) && error.code === 401
  }

  /**
   * 判断是否为权限错误
   */
  static isPermissionError(error: any): boolean {
    return this.isApiError(error) && error.code === 403
  }

  /**
   * 格式化错误信息用于日志记录
   */
  static formatForLogging(error: any): object {
    if (this.isApiError(error)) {
      return {
        type: 'ApiError',
        message: error.message,
        code: error.code,
        timestamp: error.timestamp,
        stack: error.stack,
        data: error.data,
      }
    }

    if (error instanceof Error) {
      return {
        type: 'Error',
        message: error.message,
        name: error.name,
        stack: error.stack,
      }
    }

    return {
      type: 'Unknown',
      value: error,
    }
  }
}

/**
 * 便捷的错误处理函数
 */
export const handleError = ErrorHandler.handle
export const getErrorMessage = ErrorHandler.getErrorMessage
export const isApiError = ErrorHandler.isApiError
export const isRetryableError = ErrorHandler.isRetryableError
export const shouldRelogin = ErrorHandler.shouldRelogin
export const isPermissionError = ErrorHandler.isPermissionError

/**
 * 错误处理装饰器
 * 用于自动处理async方法中的错误
 */
export function withErrorHandler<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  customHandler?: (error: any) => void
): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args)
    } catch (error) {
      const apiError = ErrorHandler.handle(error)
      
      if (customHandler) {
        customHandler(apiError)
      } else {
        console.error('API调用失败:', ErrorHandler.formatForLogging(apiError))
      }
      
      throw apiError
    }
  }) as T
}
