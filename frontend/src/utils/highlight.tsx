/**
 * 高亮搜索关键词的工具函数
 * @param text 原始文本
 * @param keyword 搜索关键词
 * @param className 高亮样式类名
 * @returns 包含高亮的React元素
 */
export const highlightText = (
  text: string, 
  keyword: string, 
  className: string = 'bg-yellow-200 text-yellow-800 px-1 rounded'
): React.ReactNode => {
  if (!keyword || !text) {
    return text
  }

  // 转义特殊字符，避免正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  
  // 创建不区分大小写的正则表达式
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  
  // 分割文本并高亮匹配部分
  const parts = text.split(regex)
  
  return parts.map((part, index) => {
    if (part.toLowerCase() === keyword.toLowerCase()) {
      return (
        <span key={index} className={className}>
          {part}
        </span>
      )
    }
    return part
  })
}

/**
 * 多关键词高亮函数
 * @param text 原始文本
 * @param keywords 关键词数组
 * @param className 高亮样式类名
 * @returns 包含高亮的React元素
 */
export const highlightMultipleKeywords = (
  text: string,
  keywords: string[],
  className: string = 'bg-yellow-200 text-yellow-800 px-1 rounded'
): React.ReactNode => {
  if (!keywords.length || !text) {
    return text
  }

  // 过滤空关键词并转义
  const validKeywords = keywords
    .filter(keyword => keyword && keyword.trim())
    .map(keyword => keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))

  if (validKeywords.length === 0) {
    return text
  }

  // 创建联合正则表达式
  const regex = new RegExp(`(${validKeywords.join('|')})`, 'gi')
  
  // 分割文本并高亮匹配部分
  const parts = text.split(regex)
  
  return parts.map((part, index) => {
    // 检查是否匹配任何关键词
    const isMatch = keywords.some(keyword => 
      part.toLowerCase() === keyword.toLowerCase()
    )
    
    if (isMatch) {
      return (
        <span key={index} className={className}>
          {part}
        </span>
      )
    }
    return part
  })
}

/**
 * 检查文本是否包含搜索关键词
 * @param text 文本
 * @param keyword 关键词
 * @returns 是否匹配
 */
export const isTextMatch = (text: string, keyword: string): boolean => {
  if (!keyword || !text) {
    return false
  }
  
  return text.toLowerCase().includes(keyword.toLowerCase())
}

/**
 * 检查文本是否包含任一关键词
 * @param text 文本
 * @param keywords 关键词数组
 * @returns 是否匹配
 */
export const isTextMatchAny = (text: string, keywords: string[]): boolean => {
  if (!keywords.length || !text) {
    return false
  }
  
  return keywords.some(keyword => 
    keyword && isTextMatch(text, keyword)
  )
}