/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTH = 'AUTH',
  PERMISSION = 'PERMISSION',
  VALIDATION = 'VALIDATION',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 应用错误类
 */
export class AppError extends Error {
  public type: ErrorType
  public code?: string | number
  public details?: any

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string | number,
    details?: any
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.code = code
    this.details = details
  }
}

/**
 * 网络错误
 */
export class NetworkError extends AppError {
  constructor(message: string = '网络连接失败', code?: string | number, details?: any) {
    super(message, ErrorType.NETWORK, code, details)
    this.name = 'NetworkError'
  }
}

/**
 * 认证错误
 */
export class AuthError extends AppError {
  constructor(message: string = '认证失败', code?: string | number, details?: any) {
    super(message, ErrorType.AUTH, code, details)
    this.name = 'AuthError'
  }
}

/**
 * 权限错误
 */
export class PermissionError extends AppError {
  constructor(message: string = '没有权限', code?: string | number, details?: any) {
    super(message, ErrorType.PERMISSION, code, details)
    this.name = 'PermissionError'
  }
}

/**
 * 验证错误
 */
export class ValidationError extends AppError {
  constructor(message: string = '数据验证失败', code?: string | number, details?: any) {
    super(message, ErrorType.VALIDATION, code, details)
    this.name = 'ValidationError'
  }
}

/**
 * 业务错误
 */
export class BusinessError extends AppError {
  constructor(message: string = '业务处理失败', code?: string | number, details?: any) {
    super(message, ErrorType.BUSINESS, code, details)
    this.name = 'BusinessError'
  }
}

/**
 * 错误处理器
 */
export class ErrorHandler {
  /**
   * 处理错误
   */
  static handle(error: any): AppError {
    console.error('🚨 错误处理:', error)
    // 如果已经是AppError，直接返回
    if (error instanceof AppError) {
      return error
    }

    // 处理网络错误
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return new NetworkError('网络连接失败，请检查网络设置')
    }

    // 处理超时错误
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return new NetworkError('请求超时，请稍后重试')
    }

    // 处理HTTP状态码错误
    if (error.response?.status) {
      const status = error.response.status
      const message = error.response.data?.message || error.message

      switch (status) {
        case 400:
          return new ValidationError(message || '请求参数错误')
        case 401:
          return new AuthError(message || '未授权访问')
        case 403:
          return new PermissionError(message || '没有权限访问')
        case 404:
          return new BusinessError(message || '请求的资源不存在')
        case 422:
          return new ValidationError(message || '数据验证失败')
        case 500:
          return new BusinessError(message || '服务器内部错误')
        case 502:
          return new NetworkError('网关错误')
        case 503:
          return new NetworkError('服务不可用')
        case 504:
          return new NetworkError('网关超时')
        default:
          return new BusinessError(message || `请求失败 (${status})`)
      }
    }

    // 处理其他错误
    const message = error.message || '未知错误'
    return new AppError(message, ErrorType.UNKNOWN)
  }

  /**
   * 格式化错误消息
   */
  static formatMessage(error: AppError): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return `网络错误: ${error.message}`
      case ErrorType.AUTH:
        return `认证错误: ${error.message}`
      case ErrorType.PERMISSION:
        return `权限错误: ${error.message}`
      case ErrorType.VALIDATION:
        return `验证错误: ${error.message}`
      case ErrorType.BUSINESS:
        return `业务错误: ${error.message}`
      default:
        return error.message
    }
  }

  /**
   * 获取错误建议
   */
  static getSuggestion(error: AppError): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return '请检查网络连接或稍后重试'
      case ErrorType.AUTH:
        return '请重新登录'
      case ErrorType.PERMISSION:
        return '请联系管理员获取权限'
      case ErrorType.VALIDATION:
        return '请检查输入数据是否正确'
      case ErrorType.BUSINESS:
        return '请稍后重试或联系技术支持'
      default:
        return '请稍后重试'
    }
  }
}

/**
 * 全局错误处理函数
 */
export const handleGlobalError = (error: any): void => {
  const appError = ErrorHandler.handle(error)
  
  // 记录错误日志
  console.error('🚨 全局错误:', {
    type: appError.type,
    message: appError.message,
    code: appError.code,
    details: appError.details,
    stack: appError.stack,
  })

  // 可以在这里添加错误上报逻辑
  // reportError(appError)
}

/**
 * React错误边界Hook
 */
export const useErrorHandler = () => {
  const handleError = (error: any, errorInfo?: any) => {
    const appError = ErrorHandler.handle(error)
    
    console.error('🚨 React错误边界:', {
      error: appError,
      errorInfo,
    })

    // 可以在这里添加错误上报逻辑
    // reportError(appError, errorInfo)
  }

  return { handleError }
}
