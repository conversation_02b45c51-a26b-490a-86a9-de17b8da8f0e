/**
 * 数据导出工具函数
 * 支持Excel和CSV格式的财务数据导出
 */

import { formatCurrency, formatReturnRate, formatNumber } from './financial'
import type {
  FinancialStatsItem,
  GroupedFinancialStatsResponse,
  FinancialExportOptions
} from '@/types/financial'
import { FinancialStatsCategory } from '@/types/financial'

/**
 * CSV导出数据项
 */
export interface CSVExportItem {
  分类: string
  统计项: string
  数值: string
  单位: string
  格式化值: string
}

/**
 * Excel导出数据项
 */
export interface ExcelExportItem extends CSVExportItem {
  原始数值: number | string
}

/**
 * 格式化财务数据项用于导出
 * @param item 财务统计数据项
 * @param category 分类名称
 * @returns 格式化后的导出数据
 */
export const formatItemForExport = (
  item: FinancialStatsItem, 
  category: string
): CSVExportItem => {
  // 根据统计项名称和单位智能格式化
  let formattedValue = ''
  
  if (item.statName.includes('返奖率')) {
    formattedValue = formatReturnRate(item.statValue)
  } else if (item.unit === '元') {
    formattedValue = formatCurrency(item.statValue)
  } else if (item.unit === '%') {
    formattedValue = formatReturnRate(item.statValue)
  } else {
    formattedValue = formatNumber(item.statValue, 0)
  }

  return {
    分类: category,
    统计项: item.statName,
    数值: String(item.statValue),
    单位: item.unit || '',
    格式化值: formattedValue
  }
}

/**
 * 将分组财务数据转换为CSV导出格式
 * @param data 分组财务统计数据
 * @param options 导出选项
 * @returns CSV导出数据数组
 */
export const convertToCSVData = (
  data: GroupedFinancialStatsResponse,
  options: Partial<FinancialExportOptions> = {}
): CSVExportItem[] => {
  const { categories = [FinancialStatsCategory.USER, FinancialStatsCategory.ANCHOR, FinancialStatsCategory.TOTAL, FinancialStatsCategory.BUSINESS] } = options
  const csvData: CSVExportItem[] = []

  // 分类映射
  const categoryMap = {
    [FinancialStatsCategory.USER]: { data: data.userStats, name: '用户相关统计' },
    [FinancialStatsCategory.ANCHOR]: { data: data.anchorStats, name: '主播相关统计' },
    [FinancialStatsCategory.TOTAL]: { data: data.totalStats, name: '合计统计' },
    [FinancialStatsCategory.BUSINESS]: { data: data.businessStats, name: '其他业务统计' }
  }

  // 按指定分类导出数据
  categories.forEach(category => {
    const categoryInfo = categoryMap[category]
    if (categoryInfo && categoryInfo.data) {
      categoryInfo.data.forEach(item => {
        csvData.push(formatItemForExport(item, categoryInfo.name))
      })
    }
  })

  return csvData
}

/**
 * 生成导出文件名
 * @param format 导出格式
 * @param timeRange 时间范围描述
 * @returns 文件名
 */
export const generateExportFileName = (
  format: 'excel' | 'csv',
  timeRange?: string
): string => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
  const timeDesc = timeRange ? `_${timeRange}` : ''
  const extension = format === 'excel' ? 'xlsx' : 'csv'
  
  return `财务数据统计${timeDesc}_${timestamp}.${extension}`
}

/**
 * CSV导出配置
 */
export const getCSVConfig = (
  data: CSVExportItem[],
  filename: string
) => ({
  data,
  filename,
  headers: [
    { label: '分类', key: '分类' },
    { label: '统计项', key: '统计项' },
    { label: '数值', key: '数值' },
    { label: '单位', key: '单位' },
    { label: '格式化值', key: '格式化值' }
  ],
  separator: ',',
  enclosingCharacter: '"'
})

/**
 * 导出为CSV格式
 * @param data 分组财务统计数据
 * @param options 导出选项
 * @returns CSV配置对象
 */
export const exportToCSV = (
  data: GroupedFinancialStatsResponse,
  options: Partial<FinancialExportOptions> = {}
) => {
  const csvData = convertToCSVData(data, options)
  const filename = generateExportFileName('csv', options.filename)
  
  return getCSVConfig(csvData, filename)
}

/**
 * 创建并下载CSV文件
 * @param data 分组财务统计数据
 * @param options 导出选项
 */
export const downloadCSV = (
  data: GroupedFinancialStatsResponse,
  options: Partial<FinancialExportOptions> = {}
) => {
  const csvData = convertToCSVData(data, options)
  const filename = generateExportFileName('csv', options.filename)
  
  // 创建CSV内容
  const headers = ['分类', '统计项', '数值', '单位', '格式化值']
  const csvContent = [
    headers.join(','),
    ...csvData.map(row => [
      `"${row.分类}"`,
      `"${row.统计项}"`,
      `"${row.数值}"`,
      `"${row.单位}"`,
      `"${row.格式化值}"`
    ].join(','))
  ].join('\n')
  
  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  
  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 导出为Excel格式（使用CSV格式，Excel可以打开）
 * @param data 分组财务统计数据
 * @param options 导出选项
 */
export const exportToExcel = (
  data: GroupedFinancialStatsResponse,
  options: Partial<FinancialExportOptions> = {}
) => {
  // 使用CSV格式，但文件扩展名为xlsx，Excel可以正确打开
  const filename = generateExportFileName('excel', options.filename)

  downloadCSV(data, { ...options, filename: filename.replace('.xlsx', '.csv') })
}

/**
 * 验证导出数据
 * @param data 分组财务统计数据
 * @returns 验证结果
 */
export const validateExportData = (data: GroupedFinancialStatsResponse): {
  valid: boolean
  message?: string
  itemCount: number
} => {
  if (!data) {
    return { valid: false, message: '数据为空', itemCount: 0 }
  }

  const totalItems = [
    ...(data.userStats || []),
    ...(data.anchorStats || []),
    ...(data.totalStats || []),
    ...(data.businessStats || [])
  ].length

  if (totalItems === 0) {
    return { valid: false, message: '暂无数据可导出', itemCount: 0 }
  }

  return { valid: true, itemCount: totalItems }
}
