import React from 'react'

/**
 * 高亮显示工具函数
 * 用于在搜索结果中高亮显示匹配的关键词
 */

/**
 * 高亮显示文本中的关键词
 * @param text 原始文本
 * @param keyword 要高亮的关键词
 * @param className 高亮样式类名
 * @returns React元素或原始文本
 */
export function highlightText(
  text: string, 
  keyword: string, 
  className: string = 'bg-yellow-200 text-yellow-800 px-1 rounded'
): React.ReactNode {
  if (!keyword || !text) {
    return text
  }

  // 转义特殊字符，避免正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  
  // 创建不区分大小写的正则表达式
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  
  // 分割文本
  const parts = text.split(regex)
  
  return parts.map((part, index) => {
    // 如果部分匹配关键词（不区分大小写），则高亮显示
    if (part.toLowerCase() === keyword.toLowerCase()) {
      return React.createElement('span', { 
        key: index, 
        className 
      }, part)
    }
    return part
  })
}

/**
 * 检查文本是否包含关键词
 * @param text 文本
 * @param keyword 关键词
 * @returns 是否包含
 */
export function containsKeyword(text: string, keyword: string): boolean {
  if (!keyword || !text) {
    return false
  }
  
  return text.toLowerCase().includes(keyword.toLowerCase())
}

/**
 * 获取匹配关键词的文本片段
 * @param text 原始文本
 * @param keyword 关键词
 * @param contextLength 上下文长度
 * @returns 包含关键词的文本片段
 */
export function getMatchingSnippet(
  text: string, 
  keyword: string, 
  contextLength: number = 50
): string {
  if (!keyword || !text) {
    return text
  }

  const lowerText = text.toLowerCase()
  const lowerKeyword = keyword.toLowerCase()
  const index = lowerText.indexOf(lowerKeyword)
  
  if (index === -1) {
    return text.length > contextLength * 2 
      ? text.substring(0, contextLength * 2) + '...' 
      : text
  }

  const start = Math.max(0, index - contextLength)
  const end = Math.min(text.length, index + keyword.length + contextLength)
  
  let snippet = text.substring(start, end)
  
  if (start > 0) {
    snippet = '...' + snippet
  }
  
  if (end < text.length) {
    snippet = snippet + '...'
  }
  
  return snippet
}

/**
 * 多关键词高亮显示
 * @param text 原始文本
 * @param keywords 关键词数组
 * @param className 高亮样式类名
 * @returns React元素或原始文本
 */
export function highlightMultipleKeywords(
  text: string,
  keywords: string[],
  className: string = 'bg-yellow-200 text-yellow-800 px-1 rounded'
): React.ReactNode {
  if (!keywords || keywords.length === 0 || !text) {
    return text
  }

  // 过滤空关键词并转义特殊字符
  const validKeywords = keywords
    .filter(keyword => keyword && keyword.trim())
    .map(keyword => keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))

  if (validKeywords.length === 0) {
    return text
  }

  // 创建联合正则表达式
  const regex = new RegExp(`(${validKeywords.join('|')})`, 'gi')
  
  // 分割文本
  const parts = text.split(regex)
  
  return parts.map((part, index) => {
    // 检查是否匹配任何关键词
    const isMatch = validKeywords.some(keyword => 
      part.toLowerCase() === keyword.toLowerCase()
    )
    
    if (isMatch) {
      return React.createElement('span', { 
        key: index, 
        className 
      }, part)
    }
    return part
  }) 
}