import React, { Suspense } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { AuthGuard, PermissionGuard } from './guards.tsx'
import { routes } from './routes.ts'
import type { AppRoute } from '../types'
import { FadeIn, TypewriterText } from '../components/react-bits'

/**
 * 页面加载组件
 */
const PageLoading: React.FC = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <FadeIn direction="up" duration={600}>
      <div className="flex flex-col items-center space-y-4">
        {/* 主加载动画 */}
        <FadeIn delay={200}>
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20"></div>
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent absolute top-0 left-0"></div>
            <div className="absolute inset-0 bg-primary/10 rounded-full animate-ping"></div>
          </div>
        </FadeIn>
        {/* 加载文本 */}
        <FadeIn delay={400}>
          <div className="text-center">
            <TypewriterText 
              text="加载中" 
              speed={100}
              className="text-lg font-medium text-foreground"
            />
            <p className="text-sm text-muted-foreground animate-pulse">请稍候...</p>
          </div>
        </FadeIn>
      </div>
    </FadeIn>
  </div>
)

/**
 * 渲染路由组件 
 */
const renderRoute = (route: AppRoute, parentPath = ''): React.ReactElement => {
  const { path, component: Component, redirect, meta, children } = route

  // 构建完整路径
  const fullPath = path === '' ? parentPath : path.startsWith('/') ? path : `${parentPath}/${path}`

  // 如果是重定向路由
  if (redirect) {
    return <Route key={fullPath} path={path} element={<Navigate to={redirect} replace />} />
  }

  // 如果没有组件但有子路由，返回Outlet容器
  if (!Component && children) {
    return (
      <Route key={fullPath} path={path}>
        {children?.map(child => renderRoute(child, fullPath))}
      </Route>
    )
  }

  // 如果没有组件也没有子路由，跳过
  if (!Component) {
    return <Route key={fullPath} path={path} />
  }

  // 创建路由元素
  let element = (
    <Suspense fallback={<PageLoading />}>
      <Component />
    </Suspense>
  )

  // 如果有权限要求，包装权限守卫
  if (meta?.permissions || meta?.roles) {
    element = (
      <PermissionGuard
        permissions={meta.permissions}
        roles={meta.roles}
        fallback={
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-6xl mb-4">🔒</div>
              <h3 className="text-lg font-semibold mb-2">访问受限</h3>
              <p className="text-muted-foreground">您没有权限访问此页面</p>
            </div>
          </div>
        }
      >
        {element}
      </PermissionGuard>
    )
  }

  return (
    <Route key={fullPath} path={path} element={element}>
      {children?.map(child => renderRoute(child, fullPath))}
    </Route>
  )
}

/**
 * 应用路由组件
 */
export const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <AuthGuard>
        <Suspense fallback={<PageLoading />}>
          <Routes>
            {routes.map(route => renderRoute(route))}
          </Routes>
        </Suspense>
      </AuthGuard>
    </BrowserRouter>
  )
}

/**
 * 导出路由相关工具
 */
export { routes, findRouteByPath } from './routes.ts'
export { AuthGuard, PermissionGuard, useRouteGuard, usePageTitle, useRouteChange } from './guards.tsx'
export type { AppRoute } from '../types'
