// 导出所有API服务
export { AuthService } from './auth'
export { CaptchaService } from './captcha'
export { UserService } from './user'
export { RoleService } from './role'
export { PermissionService } from './permission'
export { MenuService } from './menu'
export { DeptService } from './dept'
export { TenantService } from './tenant'
export { FinancialService } from './financial'
export { OperationsService } from './operations'

// 导出HTTP客户端
export { httpClient, get, post, put, del as delete, upload } from './request'

// 导出类型定义
export type {
  UserQueryRequest,
  UserCreateRequest,
  UserUpdateRequest,
} from './user'

export type {
  RoleQueryRequest,
  RoleCreateRequest,
  RoleUpdateRequest,
} from './role'

export type {
  PermissionQueryRequest,
  PermissionCreateRequest,
  PermissionUpdateRequest,
} from './permission'

// 财务服务类型导出
export type {
  FinancialStatsRequest,
  GroupedFinancialStatsResponse,
  FinancialStatsItem,
  TimeRange,
} from '../types/financial'

// 运营服务类型导出
export type {
  PageRequest,
  PageResult,
  AnchorQueryRequest,
  AnchorListResponse,
  SubUserQueryRequest,
  SubUserResponse,
  RechargeQueryRequest,
  RechargeDetailResponse,
  ConsumeQueryRequest,
  ConsumeDetailResponse,
} from './operations'
