import { httpClient } from './request.ts'
import type {
  // ApiResponse,
  CaptchaResponse,
  CaptchaVerifyRequest,
  CaptchaVerifyResponse
} from '../types'

/**
 * 验证码相关API服务
 */
export class CaptchaService {
  /**
   * 生成验证码
   * @returns 验证码响应数据
   */
  static async generate(): Promise<CaptchaResponse> {
    return httpClient.get<CaptchaResponse>('/auth/captcha/generate', {
      skipAuth: true, // 验证码生成不需要认证
    })
  }

  /**
   * 验证验证码
   * @param data 验证码验证数据
   * @returns 验证结果
   */
  static async verify(data: CaptchaVerifyRequest): Promise<CaptchaVerifyResponse> {
    return httpClient.post<CaptchaVerifyResponse>('/auth/captcha/verify', data, {
      skipAuth: true, // 验证码验证不需要认证
    })
  }



  /**
   * 使验证码失效
   * @param captchaId 验证码ID
   * @returns 操作结果
   */
  static async invalidate(captchaId: string): Promise<void> {
    return httpClient.delete<void>(`/auth/captcha/${captchaId}`, {
      skipAuth: true, // 使验证码失效不需要认证
    })
  }
}
