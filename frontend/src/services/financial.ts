import { httpClient } from './request.ts'
import type { 
  FinancialStatsRequest, 
  GroupedFinancialStatsResponse
} from '../types/financial.ts'

/**
 * 财务数据API服务
 * 提供财务统计数据的查询接口
 */
export class FinancialService {
  /**
   * 获取财务统计数据
   * 根据时间范围和选项获取财务统计数据
   * @param request 查询请求参数
   * @returns 分组财务统计数据
   */
  static async getFinancialStats(request: FinancialStatsRequest): Promise<GroupedFinancialStatsResponse> {
    return httpClient.post<GroupedFinancialStatsResponse>('/financial/stats/query', request)
  }

  /**
   * 获取今日财务数据
   * 获取今日财务统计数据的快捷接口
   * @param includeAnchor 是否包含主播数据，默认为true
   * @returns 分组财务统计数据
   */
  static async getTodayStats(includeAnchor: boolean = true): Promise<GroupedFinancialStatsResponse> {
    return httpClient.get<GroupedFinancialStatsResponse>('/financial/stats/today', {
      params: { includeAnchor }
    })
  }

  /**
   * 验证时间范围参数
   * 客户端验证时间范围的有效性
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 验证结果
   */
  static validateTimeRange(startTime: string, endTime: string): {
    valid: boolean
    error?: string
  } {
    if (!startTime || !endTime) {
      return {
        valid: false,
        error: '开始时间和结束时间不能为空'
      }
    }

    const start = new Date(startTime)
    const end = new Date(endTime)

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return {
        valid: false,
        error: '时间格式不正确'
      }
    }

    if (start >= end) {
      return {
        valid: false,
        error: '结束时间必须晚于开始时间'
      }
    }

    // 限制查询范围不超过1年
    const maxDays = 365
    const diffTime = end.getTime() - start.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > maxDays) {
      return {
        valid: false,
        error: `查询时间范围不能超过${maxDays}天`
      }
    }

    return { valid: true }
  }

  /**
   * 格式化时间参数
   * 将时间字符串格式化为后端需要的格式
   * @param timeStr 时间字符串
   * @returns 格式化后的时间字符串
   */
  static formatTimeParam(timeStr: string): string {
    const date = new Date(timeStr)
    if (isNaN(date.getTime())) {
      throw new Error('无效的时间格式')
    }
    
    // 格式化为 YYYY-MM-DD HH:mm:ss 格式
    return date.toISOString().slice(0, 19).replace('T', ' ')
  }

  /**
   * 构建今日时间范围请求
   * 创建今日时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildTodayRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

    return {
      startTime: this.formatTimeParam(today.toISOString()),
      endTime: this.formatTimeParam(new Date(tomorrow.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建昨日时间范围请求
   * 创建昨日时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildYesterdayRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    return {
      startTime: this.formatTimeParam(yesterday.toISOString()),
      endTime: this.formatTimeParam(new Date(today.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建本周时间范围请求
   * 创建本周时间范围的查询请求（周一到周日）
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildThisWeekRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const dayOfWeek = now.getDay() || 7 // 将周日(0)转换为7
    const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1)
    const nextMonday = new Date(monday.getTime() + 7 * 24 * 60 * 60 * 1000)

    return {
      startTime: this.formatTimeParam(monday.toISOString()),
      endTime: this.formatTimeParam(new Date(nextMonday.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }

  /**
   * 构建本月时间范围请求
   * 创建本月时间范围的查询请求
   * @param includeAnchor 是否包含主播数据
   * @returns 财务统计请求参数
   */
  static buildThisMonthRequest(includeAnchor: boolean = true): FinancialStatsRequest {
    const now = new Date()
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)

    return {
      startTime: this.formatTimeParam(firstDay.toISOString()),
      endTime: this.formatTimeParam(new Date(nextMonth.getTime() - 1000).toISOString()),
      includeAnchor
    }
  }
}