import type {AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios'
import axios from 'axios'
import {API_CONFIG, STORAGE_KEYS} from '../constants'
import type {ApiResponse} from '../types'
import { toast } from '../hooks/useToast'
import { requestCache, type RequestCacheOptions } from '../utils/requestCache'

/**
 * 请求配置接口 - 基于context7最新axios文档优化
 */
interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean // 跳过认证
  skipErrorHandler?: boolean // 跳过错误处理
  skipToast?: boolean // 跳过Toast提示
  retryCount?: number // 重试次数
  useCache?: boolean // 是否使用缓存
  cacheOptions?: RequestCacheOptions // 缓存选项
}

/**
 * 创建axios实例
 */
const createAxiosInstance = (): AxiosInstance => {
  return axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
    // 启用请求/响应数据验证
    validateStatus: function (status) {
      return status >= 200 && status < 300 // 默认验证范围
    },
    // 启用自动解压缩
    decompress: true,
    // 设置最大内容长度
    maxContentLength: 10 * 1024 * 1024, // 10MB
    maxBodyLength: 10 * 1024 * 1024, // 10MB
  })
}

/**
 * 获取存储的Token
 */
const getToken = (): string | null => {
  return localStorage.getItem(STORAGE_KEYS.TOKEN)
}

/**
 * 清除Token和用户信息
 */
const clearAuthData = (): void => {
  localStorage.removeItem(STORAGE_KEYS.TOKEN)
  localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  localStorage.removeItem(STORAGE_KEYS.PERMISSIONS)
  localStorage.removeItem(STORAGE_KEYS.ROLES)
  sessionStorage.removeItem(STORAGE_KEYS.TOKEN)
  sessionStorage.removeItem(STORAGE_KEYS.USER_INFO)

  // 清除cookies中的认证信息
  document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
}

/**
 * 检查是否为认证错误
 */
const isAuthError = (error: any): boolean => {
  // 检查HTTP状态码
  if (error.status === 401 || error.status === 403) {
    return true
  }

  // 检查错误消息
  const errorMessage = error.message || error.msg || error.data?.message || ''
  const authErrorMessages = [
    '用户未登录或登录已失效',
    '登录状态异常，请重新登录',
    '登录已过期，请重新登录',
    '未授权访问，请先登录',
    'Token已过期',
    '登录失效',
    'Unauthorized',
    'Token expired',
    'Authentication failed'
  ]

  return authErrorMessages.some(msg =>
    errorMessage.toLowerCase().includes(msg.toLowerCase())
  )
}

/**
 * 跳转到登录页
 */
const redirectToLogin = (): void => {
  // 保存当前页面路径，登录成功后可以跳转回来
  const currentPath = window.location.pathname + window.location.search
  if (!currentPath.includes('/login')) {
    localStorage.setItem('redirectPath', currentPath)
  }

  // 清除认证数据
  clearAuthData()

  // 显示提示消息
  toast({
    title: '登录已失效',
    description: '您的登录状态已失效，请重新登录',
    variant: 'destructive',
    duration: 3000
  })

  // 延迟跳转，让用户看到提示消息
  setTimeout(() => {
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }, 1000)
}

/**
 * 创建HTTP客户端
 */
class HttpClient {
  instance: AxiosInstance
  private isRefreshing = false
  private failedQueue: Array<{
    resolve: (value?: any) => void
    reject: (reason?: any) => void
  }> = []

  constructor() {
    this.instance = createAxiosInstance()
    this.setupInterceptors()
  }

  /**
   * 判断是否应该显示成功提示
   * 只对写操作显示成功提示，避免查询操作也显示提示
   */
  private shouldShowSuccessToast(config: RequestConfig): boolean {
    const method = config.method?.toUpperCase()
    const url = config.url || ''

    // 如果明确设置了skipToast，则跳过
    if (config.skipToast) {
      return false
    }

    // GET请求通常是查询操作，不显示成功提示
    if (method === 'GET') {
      return false
    }

    // 特定的查询接口路径，即使是POST也不显示成功提示
    const queryPaths = [
      '/page',      // 分页查询
      '/list',      // 列表查询
      '/search',    // 搜索
      '/query',     // 查询
      '/export',    // 导出（通常不需要成功提示）
      '/download',  // 下载
      '/statistics',// 统计查询
      '/report',    // 报表查询
    ]

    // 检查URL是否包含查询相关的路径
    const isQueryRequest = queryPaths.some(path => url.includes(path))
    if (isQueryRequest) {
      return false
    }

    // 只对写操作（POST、PUT、DELETE、PATCH）显示成功提示
    return ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method || '')
  }

  /**
   * 判断是否应该显示错误提示
   * 对于某些查询请求的错误，可能不需要显示toast提示
   */
  private shouldShowErrorToast(config: RequestConfig, error?: any): boolean {
    // 如果明确设置了skipToast，则跳过
    if (config.skipToast) {
      return false
    }

    const url = config.url || ''

    // 对于某些特定的查询接口，404错误可能是正常的（如搜索无结果）
    const silentNotFoundPaths = [
      '/search',    // 搜索无结果
      '/query',     // 查询无结果
    ]

    // 如果是404错误且是静默处理的路径，则不显示提示
    if (error?.response?.status === 404 && silentNotFoundPaths.some(path => url.includes(path))) {
      return false
    }

    // 其他情况都显示错误提示
    return true
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: any) => {
        const token = getToken()
        
        // 如果不跳过认证且存在token，则添加到请求头
        if (!config.skipAuth && token) {
          config.headers[API_CONFIG.TOKEN_KEY] = `${API_CONFIG.TOKEN_PREFIX}${token}`
        }

        // 添加租户ID（如果存在）
        const tenantId = localStorage.getItem(STORAGE_KEYS.TENANT_ID)
        if (tenantId) {
          config.headers['Tenant-Id'] = tenantId
        }

        console.log('🚀 发送请求:', {
          url: config.url,
          method: config.method,
          headers: config.headers,
          data: config.data,
        })

        return config
      },
      (error: AxiosError) => {
        console.error('❌ 请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器 - 重构版本，自动解包响应结构
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log('✅ 收到响应:', {
          url: response.config.url,
          status: response.status,
          data: response.data,
        })

        const { data } = response
        const config = response.config as RequestConfig

        // 检查业务状态码
        if (data.code === 200) {
          // 显示成功提示（如果后端返回了消息且不跳过Toast）
          // 只对写操作（POST、PUT、DELETE）显示成功提示，避免查询操作也显示提示
          if (!config.skipToast && data.message && this.shouldShowSuccessToast(config)) {
            toast({
              title: data.message,
              variant: 'success',
              duration: 4000,
            })
          }
          
          // 重构：直接返回业务数据，而不是完整的响应结构
          // 这样调用方就可以直接使用 await service.method() 获取业务数据
          return data.data
        } else {
          // 业务错误 - 根据请求类型决定是否显示错误提示
          if (this.shouldShowErrorToast(config) && data.message) {
            toast({
              title: data.message,
              variant: 'destructive',
              duration: 4000,
            })
          }
          
          // 创建包含完整错误信息的错误对象
          const error = new Error(data.message || '请求失败') as any
          error.code = data.code
          error.data = data.data
          error.timestamp = data.timestamp
          return Promise.reject(error)
        }
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as RequestConfig

        console.error('❌ 响应错误:', {
          url: originalRequest?.url,
          status: error.response?.status,
          message: error.message,
        })

        // 基于context7文档的错误处理模式
        if (error.response) {
          // 服务器响应了状态码，但不在2xx范围内
          const { status } = error.response

          switch (status) {
            case 401: {
              // 检查是否为认证错误
              const authResponseData = error.response.data as ApiResponse
              if (isAuthError({ status, message: authResponseData?.message })) {
                redirectToLogin()
                return Promise.reject(new Error('登录已失效'))
              }
              return this.handle401Error(originalRequest, authResponseData)
            }
            case 403: {
              const forbiddenError = new Error('没有权限访问该资源')
              if (this.shouldShowErrorToast(originalRequest, error)) {
                toast({
                  title: '没有权限访问该资源',
                  variant: 'destructive',
                  duration: 4000,
                })
              }
              return Promise.reject(forbiddenError)
            }
            case 404: {
              const notFoundError = new Error('请求的资源不存在')
              if (this.shouldShowErrorToast(originalRequest, error)) {
                toast({
                  title: '请求的资源不存在',
                  variant: 'destructive',
                  duration: 4000,
                })
              }
              return Promise.reject(notFoundError)
            }
            case 422: {
              const validationError = new Error('数据验证失败')
              if (this.shouldShowErrorToast(originalRequest, error)) {
                toast({
                  title: '数据验证失败',
                  variant: 'destructive',
                  duration: 4000,
                })
              }
              return Promise.reject(validationError)
            }
            case 500:
            case 502:
            case 503:
            case 504: {
              const serverError = new Error('服务器错误，请稍后重试')
              if (this.shouldShowErrorToast(originalRequest, error)) {
                toast({
                  title: '服务器错误，请稍后重试',
                  variant: 'destructive',
                  duration: 4000,
                })
              }
              return Promise.reject(serverError)
            }
            default: {
              const defaultResponseData = error.response.data as ApiResponse
              const errorMessage = defaultResponseData?.message || `请求失败 (${status})`
              if (this.shouldShowErrorToast(originalRequest, error)) {
                toast({
                  title: errorMessage,
                  variant: 'destructive',
                  duration: 4000,
                })
              }
              return Promise.reject(new Error(errorMessage))
            }
          }
        } else if (error.request) {
          // 请求已发出，但没有收到响应
          const networkError = new Error('网络连接失败，请检查网络设置')
          if (this.shouldShowErrorToast(originalRequest, error)) {
            toast({
              title: '网络连接失败，请检查网络设置',
              variant: 'destructive',
              duration: 4000,
            })
          }
          return Promise.reject(networkError)
        } else {
          // 在设置请求时发生了错误
          const setupError = new Error(`请求配置错误: ${error.message}`)
          if (this.shouldShowErrorToast(originalRequest, error)) {
            toast({
              title: `请求配置错误: ${error.message}`,
              variant: 'destructive',
              duration: 4000,
            })
          }
          return Promise.reject(setupError)
        }
      }
    )
  }

  /**
   * 处理401错误（Token过期或被踢下线）
   */
  private async handle401Error(originalRequest: RequestConfig, responseData?: any): Promise<any> {
    if (originalRequest.skipAuth) {
      // 如果是跳过认证的请求，直接跳转登录
      redirectToLogin()
      return Promise.reject(new Error('未授权访问'))
    }

    // 检查是否是认证相关错误
    const message = responseData?.message || ''
    const isAuthenticationError = isAuthError({ status: 401, message })

    // 检查是否是被踢下线的情况
    const isKickedOut = message.includes('其他设备登录') ||
                       message.includes('被管理员强制下线') ||
                       message.includes('当前登录已失效') ||
                       message.includes('账号在其他地方登录')

    if (isKickedOut || isAuthenticationError) {
      // 被踢下线或认证失效，显示提示
      if (!originalRequest?.skipToast) {
        toast({
          title: isKickedOut ? '登录状态异常' : '登录已失效',
          description: message || '您的登录状态已失效，请重新登录',
          variant: 'destructive',
          duration: 6000,
        })
      }

      // 清除认证数据并跳转登录
      clearAuthData()

      // 保存当前页面路径
      const currentPath = window.location.pathname + window.location.search
      if (!currentPath.includes('/login')) {
        localStorage.setItem('redirectPath', currentPath)
      }

      setTimeout(() => {
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      }, 1000) // 延迟1秒让用户看到提示

      return Promise.reject(new Error(message || '登录已失效'))
    }

    // 如果正在刷新token，将请求加入队列
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject })
      }).then(() => {
        return this.instance(originalRequest)
      })
    }

    this.isRefreshing = true

    try {
      // 尝试刷新token（这里暂时直接跳转登录，后续可以实现token刷新逻辑）
      if (!originalRequest?.skipToast) {
        toast({
          title: '登录已过期',
          description: '您的登录状态已过期，请重新登录',
          variant: 'destructive',
          duration: 4000,
        })
      }

      // 保存当前页面路径
      const currentPath = window.location.pathname + window.location.search
      if (!currentPath.includes('/login')) {
        localStorage.setItem('redirectPath', currentPath)
      }

      // 延迟跳转
      setTimeout(() => {
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      }, 1000)

      return Promise.reject(new Error('登录已过期，请重新登录'))
    } catch (refreshError) {
      // 刷新失败，清除认证数据并跳转登录
      this.failedQueue.forEach(({ reject }) => {
        reject(new Error('登录已过期，请重新登录'))
      })
      this.failedQueue = []

      // 保存当前页面路径并跳转
      const currentPath = window.location.pathname + window.location.search
      if (!currentPath.includes('/login')) {
        localStorage.setItem('redirectPath', currentPath)
        setTimeout(() => {
          window.location.href = '/login'
        }, 500)
      }

      return Promise.reject(refreshError)
    } finally {
      this.isRefreshing = false
    }
  }

  /**
   * GET请求 - 重构版本，支持缓存，直接返回业务数据
   */
  get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    // 如果启用缓存且是GET请求
    if (config?.useCache && API_CONFIG.CACHE_ENABLED) {
      return requestCache.request(
        () => this.instance.get(url, config),
        url,
        config?.params,
        config?.cacheOptions
      )
    }

    return this.instance.get(url, config)
  }

  /**
   * POST请求 - 重构版本，直接返回业务数据
   */
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  /**
   * PUT请求 - 重构版本，直接返回业务数据
   */
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  /**
   * DELETE请求 - 重构版本，直接返回业务数据
   */
  delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  /**
   * 上传文件 - 重构版本，直接返回业务数据
   */
  upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<T> {
    return this.instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      // 上传进度回调
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`上传进度: ${percentCompleted}%`)
        }
      },
    })
  }

  /**
   * 创建带超时的请求 - 重构版本，直接返回业务数据
   */
  getWithTimeout<T = any>(url: string, timeoutMs: number, config?: RequestConfig): Promise<T> {
    return this.instance.get(url, {
      ...config,
      signal: AbortSignal.timeout(timeoutMs)
    })
  }

  /**
   * 创建可取消的请求 - 重构版本，直接返回业务数据
   */
  getCancelable<T = any>(url: string, config?: RequestConfig): {
    promise: Promise<T>,
    cancel: () => void
  } {
    const controller = new AbortController()

    const promise = this.instance.get(url, {
      ...config,
      signal: controller.signal
    })

    return {
      promise: promise as Promise<T>,
      cancel: () => controller.abort()
    }
  }
}

// 创建并导出HTTP客户端实例
export const httpClient = new HttpClient()

// 导出axios实例供直接访问
export const axiosInstance = httpClient.instance

// 导出便捷方法
export const { get, post, put, delete: del, upload } = httpClient
