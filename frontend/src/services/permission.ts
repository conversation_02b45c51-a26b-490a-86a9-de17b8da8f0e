/**
 * 权限管理服务
 *
 * 提供权限相关的API调用功能
 * 统一重构版本 - 完整的权限管理服务
 */

import type {
  Permission,
  PermissionQueryRequest,
  PermissionCreateRequest,
  PermissionUpdateRequest,
  PageResult
} from '../types/permission'
import {httpClient} from "@/services/request.ts";



/**
 * 权限管理API服务
 */
export class PermissionService {
  /**
   * 获取权限树形列表
   * @param params 查询参数
   * @returns 权限树形数据
   */
  static async getPermissionTree(params?: PermissionQueryRequest): Promise<Permission[]> {
    return httpClient.get<Permission[]>('/system/permission/tree', { params })
  }

  /**
   * 获取权限列表（平铺）
   * @param params 查询参数
   * @returns 权限列表
   */
  static async getPermissionList(params?: PermissionQueryRequest): Promise<PageResult<Permission>> {
    return httpClient.get<PageResult<Permission>>('/system/permission/page', { params })
  }

  /**
   * 根据ID获取权限详情
   * @param id 权限ID
   * @returns 权限详情
   */
  static async getPermissionById(id: number): Promise<Permission> {
    return httpClient.get<Permission>(`/system/permission/${id}`)
  }

  /**
   * 创建权限
   * @param permissionData 权限数据
   * @returns 创建结果
   */
  static async createPermission(permissionData: PermissionCreateRequest): Promise<void> {
    return httpClient.post<void>('/system/permission', permissionData)
  }

  /**
   * 更新权限
   * @param id 权限ID
   * @param permissionData 权限数据
   * @returns 更新结果
   */
  static async updatePermission(id: number, permissionData: PermissionUpdateRequest): Promise<void> {
    const requestData = { ...permissionData, id }
    return httpClient.put<void>('/system/permission', requestData)
  }

  /**
   * 删除权限
   * @param id 权限ID
   * @returns 删除结果
   */
  static async deletePermission(id: number): Promise<void> {
    return httpClient.delete<void>(`/system/permission/${id}`)
  }

  /**
   * 启用/禁用权限
   * @param id 权限ID
   * @param status 状态（0-禁用，1-启用）
   * @returns 操作结果
   */
  static async togglePermissionStatus(id: number, status: number): Promise<void> {
    return httpClient.put<void>(`/system/permission/${id}/status?status=${status}`)
  }

  /**
   * 更新权限状态
   * @param id 权限ID
   * @param status 状态（0-禁用，1-启用）
   * @returns 操作结果
   */
  static async updatePermissionStatus(id: number, status: number): Promise<void> {
    return httpClient.put<void>(`/system/permission/${id}/status?status=${status}`)
  }

  /**
   * 批量删除权限
   * @param ids 权限ID列表
   * @returns 删除结果
   */
  static async batchDeletePermissions(ids: number[]): Promise<void> {
    return httpClient.delete<void>('/system/permission/batch', { data: ids })
  }

  /**
   * 检查权限名称是否存在
   * @param permissionName 权限名称
   * @param excludeId 排除的权限ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkPermissionName(permissionName: string, excludeId?: number): Promise<boolean> {
    return httpClient.get<boolean>('/system/permission/check-name', {
      params: { permissionName, excludeId }
    })
  }

  /**
   * 检查权限编码是否存在
   * @param permissionCode 权限编码
   * @param excludeId 排除的权限ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkPermissionKey(permissionCode: string, excludeId?: number): Promise<boolean> {
    return httpClient.get<boolean>('/system/permission/check-code', {
      params: { permissionCode, excludeId }
    })
  }

  /**
   * 获取权限的子权限数量
   * @param parentId 父权限ID
   * @returns 子权限数量
   */
  static async getChildrenCount(parentId: number): Promise<number> {
    return httpClient.get<number>(`/system/permission/${parentId}/children-count`)
  }

  /**
   * 获取用户权限树（用于权限分配）
   * @returns 权限树
   */
  static async getUserPermissionTree(): Promise<Permission[]> {
    return httpClient.get<Permission[]>('/system/permission/user-tree')
  }

  /**
   * 获取角色权限树（用于角色权限分配）
   * @param roleId 角色ID
   * @returns 权限树
   */
  static async getRolePermissionTree(roleId?: number): Promise<Permission[]> {
    return httpClient.get<Permission[]>('/system/permission/role-tree', {
      params: { roleId }
    })
  }

  /**
   * 获取所有权限（用于角色权限分配）
   * @returns 所有权限列表
   */
  static async getAllPermissions(): Promise<Permission[]> {
    console.log('🔄 调用权限API: /system/permission/enabled')
    const response = await httpClient.get<Permission[]>('/system/permission/enabled')
    console.log('📡 权限API原始响应:', response)

    // httpClient已经处理了ApiResponse包装，直接返回data
    const permissions = Array.isArray(response) ? response : []
    console.log('✅ 解析后的权限数组:', permissions)
    return permissions
  }
}

// 导出类型定义
export type { PermissionQueryRequest, PermissionCreateRequest, PermissionUpdateRequest }
