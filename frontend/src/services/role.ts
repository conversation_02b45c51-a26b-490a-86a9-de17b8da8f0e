import { httpClient } from './request'
import type { 
  Role, 
  RoleQueryRequest, 
  RoleCreateRequest, 
  RoleUpdateRequest,
  RolePageResult,
  RoleListResponse,
  RoleDetailResponse,
  RolePermissionResponse
} from '../types/role'
import { RoleStatus } from '../types/role'
import type { Permission } from '../types/api'

/**
 * 角色管理API服务
 * 
 * 基于后端SysRoleController接口实现
 * 使用重构后的HTTP客户端，直接返回业务数据
 * 包含完整的错误处理和类型安全保障
 */
export class RoleService {
  
  // ==================== 基础CRUD操作 ====================
  
  /**
   * 分页查询角色列表
   * 对应后端: GET /system/role/page
   * @param params 查询参数
   * @returns 角色分页数据
   */
  static async pageRoles(params: RoleQueryRequest): Promise<RolePageResult> {
    try {
      return await httpClient.get<RolePageResult>('/system/role/page', { params })
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 根据ID获取角色详情
   * 对应后端: GET /system/role/{id}
   * @param id 角色ID
   * @returns 角色详情
   */
  static async getRoleById(id: number): Promise<RoleDetailResponse> {
    try {
      return await httpClient.get<RoleDetailResponse>(`/system/role/${id}`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 创建角色
   * 对应后端: POST /system/role
   * @param roleData 角色创建数据
   * @returns 创建结果
   */
  static async createRole(roleData: RoleCreateRequest): Promise<void> {
    try {
      // 创建前进行数据验证
      const validation = RoleServiceUtils.validateRoleData(roleData)
      if (!validation.isValid) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          validation.errors.join('; ')
        )
      }
      
      return await httpClient.post<void>('/system/role', roleData)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 更新角色
   * 对应后端: PUT /system/role
   * @param roleData 角色更新数据
   * @returns 更新结果
   */
  static async updateRole(roleData: RoleUpdateRequest): Promise<void> {
    try {
      // 更新前进行数据验证
      const validation = RoleServiceUtils.validateRoleData(roleData)
      if (!validation.isValid) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          validation.errors.join('; ')
        )
      }
      
      return await httpClient.put<void>('/system/role', roleData)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 删除角色
   * 对应后端: DELETE /system/role/{id}
   * @param id 角色ID
   * @returns 删除结果
   */
  static async deleteRole(id: number): Promise<void> {
    try {
      return await httpClient.delete<void>(`/system/role/${id}`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 批量删除角色
   * 对应后端: DELETE /system/role/batch
   * @param roleIds 角色ID数组
   * @returns 删除结果
   */
  static async batchDeleteRoles(roleIds: number[]): Promise<void> {
    try {
      if (!roleIds || roleIds.length === 0) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '请选择要删除的角色'
        )
      }
      
      return await httpClient.delete<void>('/system/role/batch', { 
        data: roleIds // 后端接收List<Long>参数
      })
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 状态管理 ====================

  /**
   * 更新角色状态
   * 对应后端: PUT /system/role/{id}/status
   * @param id 角色ID
   * @param status 状态（0-禁用，1-启用）
   * @returns 操作结果
   */
  static async updateRoleStatus(id: number, status: RoleStatus): Promise<void> {
    try {
      return await httpClient.put<void>(`/system/role/${id}/status`, null, {
        params: { status }
      })
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 批量更新角色状态
   * 对应后端: PUT /system/role/batch/status
   * @param roleIds 角色ID列表
   * @param status 状态（0-禁用，1-启用）
   * @returns 操作结果
   */
  static async batchUpdateRoleStatus(roleIds: number[], status: RoleStatus): Promise<void> {
    try {
      if (!roleIds || roleIds.length === 0) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '请选择要更新状态的角色'
        )
      }
      
      // 后端接收的是@RequestParam参数，需要通过URL参数传递
      const params = new URLSearchParams()
      roleIds.forEach(id => params.append('roleIds', id.toString()))
      params.append('status', status.toString())
      
      return await httpClient.put<void>(`/system/role/batch/status?${params.toString()}`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 权限管理 ====================

  /**
   * 分配角色权限
   * 对应后端: POST /system/role/{id}/permissions
   * @param roleId 角色ID
   * @param permissionIds 权限ID数组
   * @returns 分配结果
   */
  static async assignPermissions(roleId: number, permissionIds: number[]): Promise<void> {
    try {
      if (!permissionIds || !Array.isArray(permissionIds)) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '权限ID列表不能为空'
        )
      }
      
      return await httpClient.post<void>(`/system/role/${roleId}/permissions`, permissionIds)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 获取角色权限列表
   * 对应后端: GET /system/role/{id}/permissions
   * @param roleId 角色ID
   * @returns 权限列表
   */
  static async getRolePermissions(roleId: number): Promise<Permission[]> {
    try {
      return await httpClient.get<Permission[]>(`/system/role/${roleId}/permissions`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 获取角色权限ID列表
   * 对应后端: GET /system/role/{id}/permission-ids
   * @param roleId 角色ID
   * @returns 权限ID数组
   */
  static async getRolePermissionIds(roleId: number): Promise<number[]> {
    try {
      return await httpClient.get<number[]>(`/system/role/${roleId}/permission-ids`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 获取所有权限列表（用于权限分配）
   * 对应后端: GET /system/permission/enabled
   * @returns 权限列表
   */
  static async getPermissionList(): Promise<Permission[]> {
    try {
      return await httpClient.get<Permission[]>('/system/permission/enabled')
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 菜单管理 ====================

  /**
   * 分配角色菜单
   * 对应后端: POST /system/role/{id}/menus
   * @param roleId 角色ID
   * @param menuIds 菜单ID数组
   * @returns 分配结果
   */
  static async assignMenus(roleId: number, menuIds: number[]): Promise<void> {
    try {
      if (!menuIds || !Array.isArray(menuIds)) {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '菜单ID列表不能为空'
        )
      }

      return await httpClient.post<void>(`/system/role/${roleId}/menus`, menuIds)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 获取角色菜单列表
   * 对应后端: GET /system/role/{id}/menus
   * @param roleId 角色ID
   * @returns 菜单列表
   */
  static async getRoleMenus(roleId: number): Promise<any[]> {
    try {
      return await httpClient.get<any[]>(`/system/role/${roleId}/menus`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 获取角色菜单ID列表
   * 对应后端: GET /system/role/{id}/menu-ids
   * @param roleId 角色ID
   * @returns 菜单ID数组
   */
  static async getRoleMenuIds(roleId: number): Promise<number[]> {
    try {
      return await httpClient.get<number[]>(`/system/role/${roleId}/menu-ids`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 辅助功能 ====================

  /**
   * 获取所有启用的角色
   * 对应后端: GET /system/role/enabled
   * @returns 启用的角色列表
   */
  static async getAllEnabledRoles(): Promise<RoleListResponse> {
    try {
      return await httpClient.get<RoleListResponse>('/system/role/enabled')
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 统计角色用户数量
   * 对应后端: GET /system/role/{id}/user-count
   * @param roleId 角色ID
   * @returns 用户数量
   */
  static async countUsersByRoleId(roleId: number): Promise<number> {
    try {
      return await httpClient.get<number>(`/system/role/${roleId}/user-count`)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 检查角色编码是否存在
   * 对应后端: GET /system/role/check-code
   * @param roleCode 角色编码
   * @param excludeId 排除的角色ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkRoleCode(roleCode: string, excludeId?: number): Promise<boolean> {
    try {
      if (!roleCode || roleCode.trim() === '') {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '角色编码不能为空'
        )
      }
      
      return await httpClient.get<boolean>('/system/role/check-code', {
        params: { roleCode, excludeId }
      })
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 检查角色名称是否存在
   * 对应后端: GET /system/role/check-name
   * @param roleName 角色名称
   * @param excludeId 排除的角色ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkRoleName(roleName: string, excludeId?: number): Promise<boolean> {
    try {
      if (!roleName || roleName.trim() === '') {
        throw new RoleServiceException(
          RoleServiceError.VALIDATION_ERROR,
          '角色名称不能为空'
        )
      }
      
      return await httpClient.get<boolean>('/system/role/check-name', {
        params: { roleName, excludeId }
      })
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 扩展功能 ====================

  /**
   * 导出角色数据
   * 注意：导出功能需要直接使用axios实例，因为返回的是Blob数据
   * @param params 查询参数
   * @returns 导出文件Blob
   */
  static async exportRoles(params: RoleQueryRequest): Promise<Blob> {
    try {
      const response = await httpClient.instance.get('/system/role/export', {
        params,
        responseType: 'blob',
        timeout: 30000, // 导出可能需要更长时间
      })
      return response.data
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 安全删除角色（检查是否可删除后再删除）
   * @param roleId 角色ID
   * @returns 删除结果
   */
  static async safeDeleteRole(roleId: number): Promise<void> {
    try {
      const canDelete = await this.canDeleteRole(roleId)
      if (!canDelete) {
        throw new RoleServiceException(
          RoleServiceError.ROLE_IN_USE,
          '角色正在被用户使用，无法删除'
        )
      }
      return await this.deleteRole(roleId)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 安全批量删除角色（只删除可删除的角色）
   * @param roleIds 角色ID列表
   * @returns 删除结果和不可删除的角色ID列表
   */
  static async safeBatchDeleteRoles(roleIds: number[]): Promise<{
    deletedIds: number[]
    undeletableIds: number[]
  }> {
    try {
      const deletableIds = await this.batchCanDeleteRoles(roleIds)
      const undeletableIds = roleIds.filter(id => !deletableIds.includes(id))
      
      if (deletableIds.length > 0) {
        await this.batchDeleteRoles(deletableIds)
      }
      
      return {
        deletedIds: deletableIds,
        undeletableIds
      }
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 便捷方法 ====================

  /**
   * 切换角色状态（启用/禁用）
   * @param id 角色ID
   * @param currentStatus 当前状态
   * @returns 操作结果
   */
  static async toggleRoleStatus(id: number, currentStatus: RoleStatus): Promise<void> {
    try {
      const newStatus = currentStatus === RoleStatus.ENABLED ? RoleStatus.DISABLED : RoleStatus.ENABLED
      return await this.updateRoleStatus(id, newStatus)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 批量切换角色状态
   * @param roleIds 角色ID列表
   * @param targetStatus 目标状态
   * @returns 操作结果
   */
  static async batchToggleRoleStatus(roleIds: number[], targetStatus: RoleStatus): Promise<void> {
    try {
      return await this.batchUpdateRoleStatus(roleIds, targetStatus)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 检查角色是否可以删除（没有关联用户）
   * @param roleId 角色ID
   * @returns 是否可以删除
   */
  static async canDeleteRole(roleId: number): Promise<boolean> {
    try {
      const userCount = await this.countUsersByRoleId(roleId)
      return userCount === 0
    } catch (error) {
      console.error('检查角色是否可删除时出错:', error)
      return false
    }
  }

  /**
   * 批量检查角色是否可以删除
   * @param roleIds 角色ID列表
   * @returns 可删除的角色ID列表
   */
  static async batchCanDeleteRoles(roleIds: number[]): Promise<number[]> {
    try {
      const deletableRoles: number[] = []
      
      // 使用Promise.allSettled来并行检查，提高性能
      const results = await Promise.allSettled(
        roleIds.map(async (roleId) => {
          const canDelete = await this.canDeleteRole(roleId)
          return { roleId, canDelete }
        })
      )
      
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value.canDelete) {
          deletableRoles.push(result.value.roleId)
        }
      })
      
      return deletableRoles
    } catch (error) {
      console.error('批量检查角色是否可删除时出错:', error)
      return []
    }
  }

  /**
   * 获取角色的完整权限信息（包含权限详情）
   * @param roleId 角色ID
   * @returns 角色权限响应
   */
  static async getRolePermissionDetails(roleId: number): Promise<RolePermissionResponse> {
    try {
      const [permissionIds, permissions] = await Promise.all([
        this.getRolePermissionIds(roleId),
        this.getRolePermissions(roleId)
      ])
      
      return {
        permissionIds,
        permissions: permissions.map(p => ({
          id: p.id,
          permissionName: p.permissionName,
          permissionCode: p.permissionCode,
          permissionType: Number(p.permissionType),
          parentId: p.parentId
        }))
      }
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  // ==================== 高级功能方法 ====================

  /**
   * 复制角色（创建一个基于现有角色的新角色）
   * @param sourceRoleId 源角色ID
   * @param newRoleData 新角色的基本信息
   * @returns 创建结果
   */
  static async copyRole(sourceRoleId: number, newRoleData: {
    roleCode: string
    roleName: string
    remark?: string
  }): Promise<void> {
    try {
      // 获取源角色信息和权限
      const [sourceRole, permissionIds] = await Promise.all([
        this.getRoleById(sourceRoleId),
        this.getRolePermissionIds(sourceRoleId)
      ])
      
      // 创建新角色
      const createRequest: RoleCreateRequest = {
        roleCode: newRoleData.roleCode,
        roleName: newRoleData.roleName,
        roleType: sourceRole.roleType,
        dataScope: sourceRole.dataScope,
        status: RoleStatus.DISABLED, // 新角色默认禁用
        sortOrder: sourceRole.sortOrder,
        permissionIds: permissionIds,
        remark: newRoleData.remark || `复制自角色: ${sourceRole.roleName}`
      }
      
      await this.createRole(createRequest)
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }

  /**
   * 批量启用角色
   * @param roleIds 角色ID列表
   * @returns 操作结果
   */
  static async batchEnableRoles(roleIds: number[]): Promise<void> {
    return this.batchUpdateRoleStatus(roleIds, RoleStatus.ENABLED)
  }

  /**
   * 批量禁用角色
   * @param roleIds 角色ID列表
   * @returns 操作结果
   */
  static async batchDisableRoles(roleIds: number[]): Promise<void> {
    return this.batchUpdateRoleStatus(roleIds, RoleStatus.DISABLED)
  }

  /**
   * 获取角色统计信息
   * @returns 角色统计数据
   */
  static async getRoleStatistics(): Promise<{
    total: number
    enabled: number
    disabled: number
    system: number
    custom: number
  }> {
    try {
      // 获取所有角色（不分页）
      const allRoles = await this.pageRoles({
        pageNum: 1,
        pageSize: 1000 // 假设角色总数不会超过1000
      })
      
      const stats = {
        total: allRoles.total,
        enabled: 0,
        disabled: 0,
        system: 0,
        custom: 0
      }
      
      allRoles.records.forEach(role => {
        if (role.status === RoleStatus.ENABLED) {
          stats.enabled++
        } else {
          stats.disabled++
        }
        
        if (role.roleType === 'SYSTEM') {
          stats.system++
        } else {
          stats.custom++
        }
      })
      
      return stats
    } catch (error) {
      throw RoleServiceUtils.handleError(error)
    }
  }
}

// ==================== 错误处理和工具方法 ====================

/**
 * 角色服务错误类型
 */
export enum RoleServiceError {
  ROLE_NOT_FOUND = 'ROLE_NOT_FOUND',
  ROLE_CODE_EXISTS = 'ROLE_CODE_EXISTS',
  ROLE_NAME_EXISTS = 'ROLE_NAME_EXISTS',
  ROLE_IN_USE = 'ROLE_IN_USE',
  PERMISSION_ASSIGN_FAILED = 'PERMISSION_ASSIGN_FAILED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 角色服务异常类
 */
export class RoleServiceException extends Error {
  constructor(
    public readonly type: RoleServiceError,
    message: string,
    public readonly data?: any
  ) {
    super(message)
    this.name = 'RoleServiceException'
  }
}

/**
 * 角色服务工具类
 */
export class RoleServiceUtils {
  
  /**
   * 处理角色服务错误
   * @param error 原始错误
   * @returns 格式化的角色服务异常
   */
  static handleError(error: any): RoleServiceException {
    if (error instanceof RoleServiceException) {
      return error
    }

    // 根据错误信息判断错误类型
    const message = error.message || '未知错误'
    
    if (message.includes('角色不存在')) {
      return new RoleServiceException(RoleServiceError.ROLE_NOT_FOUND, message, error)
    }
    
    if (message.includes('角色编码已存在') || message.includes('roleCode')) {
      return new RoleServiceException(RoleServiceError.ROLE_CODE_EXISTS, message, error)
    }
    
    if (message.includes('角色名称已存在') || message.includes('roleName')) {
      return new RoleServiceException(RoleServiceError.ROLE_NAME_EXISTS, message, error)
    }
    
    if (message.includes('角色正在使用') || message.includes('用户关联')) {
      return new RoleServiceException(RoleServiceError.ROLE_IN_USE, message, error)
    }
    
    if (message.includes('权限分配') || message.includes('permission')) {
      return new RoleServiceException(RoleServiceError.PERMISSION_ASSIGN_FAILED, message, error)
    }
    
    if (message.includes('验证') || message.includes('validation')) {
      return new RoleServiceException(RoleServiceError.VALIDATION_ERROR, message, error)
    }
    
    if (message.includes('网络') || message.includes('network')) {
      return new RoleServiceException(RoleServiceError.NETWORK_ERROR, message, error)
    }
    
    return new RoleServiceException(RoleServiceError.UNKNOWN_ERROR, message, error)
  }

  /**
   * 验证角色数据
   * @param roleData 角色数据
   * @returns 验证结果
   */
  static validateRoleData(roleData: Partial<RoleCreateRequest | RoleUpdateRequest>): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 验证角色编码
    if ('roleCode' in roleData && roleData.roleCode) {
      if (!/^[A-Z][A-Z0-9_]*$/.test(roleData.roleCode)) {
        errors.push('角色编码只能包含大写字母、数字和下划线，且必须以大写字母开头')
      }
      if (roleData.roleCode.length > 50) {
        errors.push('角色编码长度不能超过50个字符')
      }
    }

    // 验证角色名称
    if ('roleName' in roleData && roleData.roleName) {
      if (roleData.roleName.length > 100) {
        errors.push('角色名称长度不能超过100个字符')
      }
    }

    // 验证备注
    if (roleData.remark && roleData.remark.length > 500) {
      errors.push('备注长度不能超过500个字符')
    }

    // 验证排序
    if ('sortOrder' in roleData && roleData.sortOrder !== undefined && roleData.sortOrder < 0) {
      errors.push('排序值不能小于0')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 格式化角色状态显示文本
   * @param status 角色状态
   * @returns 状态显示文本
   */
  static formatRoleStatus(status: RoleStatus): string {
    return status === RoleStatus.ENABLED ? '启用' : '禁用'
  }

  /**
   * 格式化角色类型显示文本
   * @param roleType 角色类型
   * @returns 类型显示文本
   */
  static formatRoleType(roleType: string): string {
    return roleType === 'SYSTEM' ? '系统角色' : '自定义角色'
  }

  /**
   * 格式化数据权限范围显示文本
   * @param dataScope 数据权限范围
   * @returns 权限范围显示文本
   */
  static formatDataScope(dataScope: string): string {
    const scopeMap: Record<string, string> = {
      'ALL': '全部数据权限',
      'DEPT': '部门数据权限',
      'DEPT_AND_SUB': '部门及子部门数据权限',
      'SELF': '仅本人数据权限',
      'CUSTOM': '自定义数据权限'
    }
    return scopeMap[dataScope] || dataScope
  }

  /**
   * 检查角色是否为系统角色
   * @param role 角色对象
   * @returns 是否为系统角色
   */
  static isSystemRole(role: Role): boolean {
    return role.roleType === 'SYSTEM'
  }

  /**
   * 检查角色是否启用
   * @param role 角色对象
   * @returns 是否启用
   */
  static isRoleEnabled(role: Role): boolean {
    return role.status === RoleStatus.ENABLED
  }

  /**
   * 生成角色显示名称
   * @param role 角色对象
   * @returns 显示名称
   */
  static getRoleDisplayName(role: Role): string {
    return `${role.roleName} (${role.roleCode})`
  }

  /**
   * 过滤可删除的角色（排除系统角色）
   * @param roles 角色列表
   * @returns 可删除的角色列表
   */
  static filterDeletableRoles(roles: Role[]): Role[] {
    return roles.filter(role => !this.isSystemRole(role))
  }

  /**
   * 按状态分组角色
   * @param roles 角色列表
   * @returns 按状态分组的角色
   */
  static groupRolesByStatus(roles: Role[]): {
    enabled: Role[]
    disabled: Role[]
  } {
    return roles.reduce(
      (groups, role) => {
        if (this.isRoleEnabled(role)) {
          groups.enabled.push(role)
        } else {
          groups.disabled.push(role)
        }
        return groups
      },
      { enabled: [] as Role[], disabled: [] as Role[] }
    )
  }

  /**
   * 按类型分组角色
   * @param roles 角色列表
   * @returns 按类型分组的角色
   */
  static groupRolesByType(roles: Role[]): {
    system: Role[]
    custom: Role[]
  } {
    return roles.reduce(
      (groups, role) => {
        if (this.isSystemRole(role)) {
          groups.system.push(role)
        } else {
          groups.custom.push(role)
        }
        return groups
      },
      { system: [] as Role[], custom: [] as Role[] }
    )
  }
}

// ==================== 导出 ====================

// 导出服务类为默认导出
export default RoleService

// 重新导出类型定义，方便其他模块使用
export type {
  Role,
  RoleQueryRequest,
  RoleCreateRequest,
  RoleUpdateRequest,
  RolePageResult,
  RoleListResponse,
  RoleDetailResponse,
  RolePermissionResponse,
  RoleStatus
} from '../types/role'