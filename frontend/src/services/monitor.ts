/**
 * 监控管理服务
 * 
 * 基于权限管理模块的成功架构模式
 */

import { httpClient } from './request'
import type {
  OperLog,
  LoginLog,
  OperLogQueryRequest,
  LoginLogQueryRequest,
  SystemInfo,
  SystemStats,
  RecentLogs,
  PageResult
} from '@/types/monitor'

/**
 * 监控管理服务类
 */
export class MonitorService {
  
  // ==================== 操作日志管理 ====================
  
  /**
   * 分页查询操作日志
   * @param params 查询参数
   * @returns 操作日志分页数据
   */
  static async getOperLogList(params: OperLogQueryRequest): Promise<PageResult<OperLog>> {
    return httpClient.get<PageResult<OperLog>>('/system/monitor/operlog/page', { params })
  }
  
  /**
   * 获取操作日志详情
   * @param id 日志ID
   * @returns 操作日志详情
   */
  static async getOperLogById(id: number): Promise<OperLog> {
    return httpClient.get<OperLog>(`/system/monitor/operlog/${id}`)
  }
  
  /**
   * 删除操作日志
   * @param id 日志ID
   * @returns 删除结果
   */
  static async deleteOperLog(id: number): Promise<void> {
    return httpClient.delete<void>(`/system/monitor/operlog/${id}`)
  }
  
  /**
   * 批量删除操作日志
   * @param ids 日志ID列表
   * @returns 删除结果
   */
  static async batchDeleteOperLogs(ids: number[]): Promise<void> {
    return httpClient.delete<void>('/system/monitor/operlog/batch', { data: ids })
  }
  
  /**
   * 清空操作日志
   * @returns 清空结果
   */
  static async clearOperLogs(): Promise<void> {
    return httpClient.delete<void>('/system/monitor/operlog/clear')
  }
  
  /**
   * 清理历史操作日志
   * @param days 保留天数
   * @returns 清理数量
   */
  static async cleanOldOperLogs(days: number): Promise<number> {
    return httpClient.delete<number>(`/system/monitor/operlog/clean?days=${days}`)
  }
  
  /**
   * 导出操作日志
   * @param params 查询参数
   * @returns 导出文件
   */
  static async exportOperLogs(params: OperLogQueryRequest): Promise<Blob> {
    return httpClient.get<Blob>('/system/monitor/operlog/export', { 
      params,
      responseType: 'blob'
    })
  }
  
  // ==================== 登录日志管理 ====================
  
  /**
   * 分页查询登录日志
   * @param params 查询参数
   * @returns 登录日志分页数据
   */
  static async getLoginLogList(params: LoginLogQueryRequest): Promise<PageResult<LoginLog>> {
    return httpClient.get<PageResult<LoginLog>>('/system/monitor/loginlog/page', { params })
  }
  
  /**
   * 获取登录日志详情
   * @param id 日志ID
   * @returns 登录日志详情
   */
  static async getLoginLogById(id: number): Promise<LoginLog> {
    return httpClient.get<LoginLog>(`/system/monitor/loginlog/${id}`)
  }
  
  /**
   * 删除登录日志
   * @param id 日志ID
   * @returns 删除结果
   */
  static async deleteLoginLog(id: number): Promise<void> {
    return httpClient.delete<void>(`/system/monitor/loginlog/${id}`)
  }
  
  /**
   * 批量删除登录日志
   * @param ids 日志ID列表
   * @returns 删除结果
   */
  static async batchDeleteLoginLogs(ids: number[]): Promise<void> {
    return httpClient.delete<void>('/system/monitor/loginlog/batch', { data: ids })
  }
  
  /**
   * 清空登录日志
   * @returns 清空结果
   */
  static async clearLoginLogs(): Promise<void> {
    return httpClient.delete<void>('/system/monitor/loginlog/clear')
  }
  
  /**
   * 清理历史登录日志
   * @param days 保留天数
   * @returns 清理数量
   */
  static async cleanOldLoginLogs(days: number): Promise<number> {
    return httpClient.delete<number>(`/system/monitor/loginlog/clean?days=${days}`)
  }
  
  /**
   * 导出登录日志
   * @param params 查询参数
   * @returns 导出文件
   */
  static async exportLoginLogs(params: LoginLogQueryRequest): Promise<Blob> {
    return httpClient.get<Blob>('/system/monitor/loginlog/export', { 
      params,
      responseType: 'blob'
    })
  }
  
  // ==================== 系统监控 ====================
  
  /**
   * 获取系统信息
   * @returns 系统信息
   */
  static async getSystemInfo(): Promise<SystemInfo> {
    return httpClient.get<SystemInfo>('/system/monitor/system/info')
  }
  
  /**
   * 获取系统统计信息
   * @returns 系统统计信息
   */
  static async getSystemStats(): Promise<SystemStats> {
    return httpClient.get<SystemStats>('/system/monitor/system/stats')
  }
  
  /**
   * 获取最近日志
   * @returns 最近日志
   */
  static async getRecentLogs(): Promise<RecentLogs> {
    return httpClient.get<RecentLogs>('/system/monitor/recent/logs')
  }
  
  // ==================== 在线用户管理 ====================
  
  /**
   * 获取在线用户列表
   * @returns 在线用户列表
   */
  static async getOnlineUsers(): Promise<any[]> {
    return httpClient.get<any[]>('/system/monitor/online/users')
  }
  
  /**
   * 强制下线用户
   * @param sessionId 会话ID
   * @returns 操作结果
   */
  static async forceLogout(sessionId: string): Promise<void> {
    return httpClient.delete<void>(`/system/monitor/online/users/${sessionId}`)
  }
  
  // ==================== 工具方法 ====================
  
  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  /**
   * 格式化运行时间
   * @param uptime 运行时间（毫秒）
   * @returns 格式化后的时间
   */
  static formatUptime(uptime: number): string {
    const seconds = Math.floor(uptime / 1000)
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟 ${remainingSeconds}秒`
    } else {
      return `${remainingSeconds}秒`
    }
  }
  
  /**
   * 格式化内存使用率
   * @param used 已使用内存
   * @param max 最大内存
   * @returns 使用率百分比
   */
  static formatMemoryUsage(used: number, max: number): number {
    if (max === 0) return 0
    return Math.round((used / max) * 100)
  }
  
  /**
   * 下载文件
   * @param blob 文件数据
   * @param filename 文件名
   */
  static downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
  
  /**
   * 生成导出文件名
   * @param prefix 前缀
   * @param extension 扩展名
   * @returns 文件名
   */
  static generateExportFilename(prefix: string, extension: string = 'xlsx'): string {
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '')
    return `${prefix}_${timestamp}.${extension}`
  }
}
