import type {ApiResponse} from '../types'
import {axiosInstance} from "./request.ts";

/**
 * 菜单项接口（匹配后端数据结构）
 */
export interface MenuItem {
  id: number
  parentId: number
  menuName: string        // 后端字段名
  menuType: number        // 后端字段名：0-目录，1-菜单，2-按钮
  path?: string
  component?: string
  icon?: string
  permissionCode?: string // 后端字段名
  sortOrder: number       // 后端字段名
  visible: number         // 后端字段名：0-隐藏，1-显示
  status: number          // 后端字段名：0-禁用，1-启用
  children?: MenuItem[]
  createTime?: string
  updateTime?: string
}

/**
 * 菜单查询请求
 */
export interface MenuQueryRequest {
  menuName?: string
  status?: number
  visible?: number
  menuType?: number
  parentId?: number
  current?: number
  size?: number
}

/**
 * 菜单创建请求
 */
export interface MenuCreateRequest {
  parentId: number
  menuName: string
  menuType: number
  path?: string
  component?: string
  icon?: string
  permissionCode?: string
  status: number
  visible: number
  sortOrder: number
  remark?: string
}

/**
 * 菜单更新请求
 */
export interface MenuUpdateRequest extends MenuCreateRequest {
  id: number
}


/**
 * 菜单服务
 */
export class MenuService {
  /**
   * 获取用户菜单树
   */
  static async getUserMenuTree(): Promise<ApiResponse<MenuItem[]>> {
    return axiosInstance.get('/system/menu/user-tree')
  }

  /**
   * 分页查询菜单列表
   */
  static async getMenuPage(params: MenuQueryRequest): Promise<ApiResponse<any>> {
    return axiosInstance.get('/system/menu/page', { params })
  }

  /**
   * 获取菜单列表（分页）
   */
  static async getMenuList(params?: MenuQueryRequest): Promise<ApiResponse<{
    records: MenuItem[]
    total: number
    pageNum: number
    pageSize: number
  }>> {
    return axiosInstance.get('/system/menu/page', { params })
  }

  /**
   * 获取菜单树
   */
  static async getMenuTree(): Promise<ApiResponse<MenuItem[]>> {
    return axiosInstance.get('/system/menu/tree')
  }

  /**
   * 根据ID获取菜单详情
   */
  static async getMenuById(id: number): Promise<ApiResponse<MenuItem>> {
    return axiosInstance.get(`/system/menu/${id}`)
  }

  /**
   * 创建菜单
   */
  static async createMenu(data: MenuCreateRequest): Promise<ApiResponse<void>> {
    return axiosInstance.post('/system/menu', data)
  }

  /**
   * 更新菜单
   */
  static async updateMenu(data: MenuUpdateRequest): Promise<ApiResponse<void>> {
    return axiosInstance.put('/system/menu', data)
  }

  /**
   * 删除菜单
   */
  static async deleteMenu(id: number): Promise<ApiResponse<void>> {
    return axiosInstance.delete(`/system/menu/${id}`)
  }

  /**
   * 批量删除菜单
   */
  static async batchDeleteMenus(ids: number[]): Promise<ApiResponse<void>> {
    return axiosInstance.delete('/system/menu/batch', { data: { ids } })
  }

  /**
   * 更新菜单状态
   */
  static async updateMenuStatus(id: number, status: number): Promise<ApiResponse<void>> {
    return axiosInstance.put(`/system/menu/${id}/status`, { status })
  }

  /**
   * 更新菜单显示状态
   */
  static async updateMenuVisible(id: number, visible: number): Promise<ApiResponse<void>> {
    return axiosInstance.put(`/system/menu/${id}/visible`, { visible })
  }

  /**
   * 获取子菜单列表
   */
  static async getChildMenus(parentId: number): Promise<ApiResponse<MenuItem[]>> {
    return axiosInstance.get(`/system/menu/children/${parentId}`)
  }

  /**
   * 检查菜单名称是否存在
   * 重构后直接返回boolean结果，无需嵌套访问
   */
  static async checkMenuName(menuName: string, parentId: number, excludeId?: number): Promise<boolean> {
    return axiosInstance.get('/system/menu/check-name', {
      params: { menuName, parentId, excludeId }
    })
  }

  /**
   * 检查路由路径是否存在
   * 重构后直接返回boolean结果，无需嵌套访问
   */
  static async checkPath(path: string, excludeId?: number): Promise<boolean> {
    return axiosInstance.get('/system/menu/check-path', {
      params: { path, excludeId }
    })
  }

  /**
   * 初始化系统菜单
   */
  static async initSystemMenus(): Promise<ApiResponse<void>> {
    return axiosInstance.post('/system/menu/init')
  }

  // ==================== 权限相关方法 ====================

  /**
   * 获取所有权限菜单（按钮类型）
   * @returns 权限菜单列表
   */
  static async getPermissionMenus(): Promise<MenuItem[]> {
    return axiosInstance.get('/system/menu/permissions')
  }

  /**
   * 获取权限菜单树
   * @returns 权限菜单树
   */
  static async getPermissionMenuTree(): Promise<MenuItem[]> {
    return axiosInstance.get('/system/menu/permission-tree')
  }

  /**
   * 根据父菜单ID获取权限菜单
   * @param parentId 父菜单ID
   * @returns 权限菜单列表
   */
  static async getPermissionMenusByParent(parentId: number): Promise<MenuItem[]> {
    return axiosInstance.get(`/system/menu/permissions/parent/${parentId}`)
  }

  /**
   * 根据权限编码获取权限菜单
   * @param permissionCode 权限编码
   * @returns 权限菜单
   */
  static async getPermissionMenuByCode(permissionCode: string): Promise<MenuItem> {
    return axiosInstance.get(`/system/menu/permission/${permissionCode}`)
  }

  /**
   * 验证权限编码是否唯一
   * @param permissionCode 权限编码
   * @param excludeId 排除的菜单ID（编辑时使用）
   * @returns 是否唯一
   */
  static async validatePermissionCode(permissionCode: string, excludeId?: number): Promise<boolean> {
    const params = excludeId ? { excludeId } : {}
    return axiosInstance.get(`/system/menu/validate-permission-code/${permissionCode}`, { params })
  }

  /**
   * 批量设置权限编码
   * @param items 权限编码设置项
   * @returns 设置结果
   */
  static async batchSetPermissionCodes(items: Array<{ id: number; permissionCode: string }>): Promise<ApiResponse<void>> {
    return axiosInstance.post('/system/menu/batch-set-permission-codes', items)
  }

  /**
   * 生成权限编码建议
   * @param menuName 菜单名称
   * @param parentPath 父级路径
   * @returns 权限编码建议
   */
  static async generatePermissionCodeSuggestion(menuName: string, parentPath?: string): Promise<string[]> {
    const params = { menuName, parentPath }
    return axiosInstance.get('/system/menu/generate-permission-code', { params })
  }
}
