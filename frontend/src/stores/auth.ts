import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthService } from '../services'
import { STORAGE_KEYS } from '../constants'
import type { User, LoginRequest, Menu } from '../types'



/**
 * 认证状态接口
 */
interface AuthState {
  // 状态
  isAuthenticated: boolean
  user: User | null
  token: string | null
  permissions: string[]
  roles: string[]
  menus: Menu[]
  loading: boolean
  error: string | null

  // 操作方法
  login: (loginData: LoginRequest) => Promise<void>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
  refreshUserInfo: () => Promise<void>
  refreshMenus: () => Promise<void>
  refreshPermissions: () => Promise<void>
  updateUserInfo: (userInfo: Partial<User>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  initializeAuth: () => void
}

/**
 * 创建认证状态管理
 */
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      user: null,
      token: null,
      permissions: [],
      roles: [],
      menus: [],
      loading: false,
      error: null,

      /**
       * 用户登录
       */
      login: async (loginData: LoginRequest) => {
        try {
          set({ loading: true, error: null })

          const data: any = await AuthService.login(loginData)
          if (!data) {
            throw new Error( '登录失败')
          }

          const { accessToken, userInfo, permissions, roles } = data

          // 构建符合前端User接口的用户对象
          const user: User = {
            id: userInfo.userId,
            username: userInfo.username,
            nickname: userInfo.nickname,
            realName: userInfo.realName,
            email: userInfo.email,
            phone: userInfo.phone,
            avatar: userInfo.avatar,
            gender: 0, // 默认值
            birthday: '', // 默认值
            status: 1, // 默认启用
            isAdmin: 0, // 默认非管理员
            remark: '', // 默认值
            tenantId: userInfo.tenantId,
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString(),
            createBy: userInfo.userId,
            updateBy: userInfo.userId,
            deleted: 0
          }

          // 保存token到localStorage
          localStorage.setItem(STORAGE_KEYS.TOKEN, accessToken)
          localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user))
          localStorage.setItem(STORAGE_KEYS.PERMISSIONS, JSON.stringify(permissions))
          localStorage.setItem(STORAGE_KEYS.ROLES, JSON.stringify(roles))

          // 保存租户ID - 添加空值检查
          if (userInfo && userInfo.tenantId) {
            localStorage.setItem(STORAGE_KEYS.TENANT_ID, userInfo.tenantId.toString())
          }

          // 获取用户菜单
          let userMenus: Menu[] = []
          try {
            console.log('开始获取用户菜单...')
            userMenus = await AuthService.getUserMenus()
            console.log('用户菜单获取成功:', userMenus)
            localStorage.setItem(STORAGE_KEYS.MENUS, JSON.stringify(userMenus))
          } catch (menuError) {
            console.error('获取用户菜单失败:', menuError)
            // 菜单获取失败不影响登录流程
          }

          console.log('设置 AuthStore 状态:', {
            isAuthenticated: true,
            user,
            permissions,
            roles,
            menus: userMenus,
            menusLength: userMenus?.length || 0
          })

          set({
            isAuthenticated: true,
            user,
            token: accessToken,
            permissions,
            roles,
            menus: userMenus,
            loading: false,
            error: null,
          })
        } catch (error) {

          let errorMessage = '登录失败'
          if (error instanceof Error) {
            errorMessage = error.message
          }
          set({
            loading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null,
            permissions: [],
            roles: [],
          })

          throw new Error(errorMessage)
        }
      },

      /**
       * 用户登出
       */
      logout: async () => {
        try {
          set({ loading: true })

          // 调用后端登出接口
          await AuthService.logout()
        } catch (error) {
          console.error('❌ 登出接口调用失败:', error)
          // 即使后端登出失败，也要清除本地状态
        } finally {
          // 清除本地存储
          localStorage.removeItem(STORAGE_KEYS.TOKEN)
          localStorage.removeItem(STORAGE_KEYS.USER_INFO)
          localStorage.removeItem(STORAGE_KEYS.PERMISSIONS)
          localStorage.removeItem(STORAGE_KEYS.ROLES)
          localStorage.removeItem(STORAGE_KEYS.MENUS)
          localStorage.removeItem(STORAGE_KEYS.TENANT_ID)

          // 重置状态
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            permissions: [],
            roles: [],
            menus: [],
            loading: false,
            error: null,
          })

          console.log('登出成功')
        }
      },

      /**
       * 检查认证状态
       */
      checkAuth: async () => {
        const state = get()

        // 多重防护：如果正在加载、已经认证、或者最近刚检查过，避免重复检查
        const now = Date.now()
        const lastCheckTime = (state as any).lastCheckTime || 0
        const CHECK_INTERVAL = 10000 // 增加到10秒内不重复检查

        if (state.loading || state.isAuthenticated || (now - lastCheckTime < CHECK_INTERVAL)) {
          console.log('跳过重复认证检查:', {
            loading: state.loading,
            authenticated: state.isAuthenticated,
            timeSinceLastCheck: now - lastCheckTime,
            interval: CHECK_INTERVAL
          })
          return
        }

        try {
          // 记录检查时间和设置加载状态
          set({
            lastCheckTime: now,
            loading: true
          } as any)

          const token = localStorage.getItem(STORAGE_KEYS.TOKEN)
          if (!token) {
            console.log('无token，设置未认证状态')
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              permissions: [],
              roles: [],
              loading: false
            })
            return
          }

          console.log('开始检查token有效性，时间:', new Date().toLocaleTimeString())

          // 检查token是否有效
          const isLogin = await AuthService.checkLogin()
          if (isLogin) {
            console.log('Token有效，获取用户信息')
            // 获取用户信息
            await get().refreshUserInfo()
          } else {
            console.log('Token无效，清除认证状态')
            // token无效，静默清除认证状态，避免调用logout导致循环
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              permissions: [],
              roles: [],
              menus: [],
              loading: false
            })
            // 清除本地存储
            localStorage.removeItem(STORAGE_KEYS.TOKEN)
            localStorage.removeItem(STORAGE_KEYS.USER_INFO)
            localStorage.removeItem(STORAGE_KEYS.PERMISSIONS)
            localStorage.removeItem(STORAGE_KEYS.ROLES)
            localStorage.removeItem(STORAGE_KEYS.MENUS)
          }
        } catch (error) {
          console.error('检查认证状态失败:', error)
          // 静默处理错误，避免循环调用logout
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            permissions: [],
            roles: [],
            menus: [],
            loading: false,
            error: '认证检查失败'
          })
          // 清除本地存储
          localStorage.removeItem(STORAGE_KEYS.TOKEN)
          localStorage.removeItem(STORAGE_KEYS.USER_INFO)
          localStorage.removeItem(STORAGE_KEYS.PERMISSIONS)
          localStorage.removeItem(STORAGE_KEYS.ROLES)
          localStorage.removeItem(STORAGE_KEYS.MENUS)
        } finally {
          // 确保loading状态被清除
          const currentState = get()
          if (currentState.loading) {
            set({ loading: false })
          }
        }
      },

      /**
       * 刷新用户信息
       */
      refreshUserInfo: async () => {
        try {
          set({ loading: true })

          // 并行获取用户信息、权限和角色
          const [userResponse, permissionsResponse, rolesResponse] = await Promise.all([
            AuthService.getCurrentUser(),
            AuthService.getUserPermissions(),
            AuthService.getUserRoles(),
          ])

          const user: any = userResponse
          const permissions: any = permissionsResponse
          const roles: any = rolesResponse

          // 更新localStorage
          localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user))
          localStorage.setItem(STORAGE_KEYS.PERMISSIONS, JSON.stringify(permissions))
          localStorage.setItem(STORAGE_KEYS.ROLES, JSON.stringify(roles))

          // 更新租户ID
          if (user.tenantId) {
            localStorage.setItem(STORAGE_KEYS.TENANT_ID, user.tenantId.toString())
          }

          set({
            user,
            permissions,
            roles,
            isAuthenticated: true,
            loading: false,
            error: null,
          })

          console.log('刷新用户信息成功:', { user: user.username, permissions, roles })
        } catch (error) {
          console.error('刷新用户信息失败:', error)
          const errorMessage = error instanceof Error ? error.message : '获取用户信息失败'
          set({ loading: false, error: errorMessage })
          throw error
        }
      },

      /**
       * 刷新用户菜单
       */
      refreshMenus: async () => {
        try {
          set({ loading: true })

          const menus = await AuthService.getUserMenus()

          // 更新状态和本地存储
          localStorage.setItem(STORAGE_KEYS.MENUS, JSON.stringify(menus))
          set({
            menus,
            loading: false,
            error: null,
          })

          console.log('刷新用户菜单成功:', { menuCount: menus.length })
        } catch (error) {
          console.error('刷新用户菜单失败:', error)
          const errorMessage = error instanceof Error ? error.message : '获取用户菜单失败'
          set({ loading: false, error: errorMessage })
          throw error
        }
      },

      /**
       * 刷新权限（用于角色权限分配后的权限更新）
       */
      refreshPermissions: async () => {
        try {
          set({ loading: true })

          // 并行获取权限和角色
          const [permissionsResponse, rolesResponse] = await Promise.all([
            AuthService.getUserPermissions(),
            AuthService.getUserRoles(),
          ])

          const permissions: any = permissionsResponse
          const roles: any = rolesResponse

          // 更新localStorage
          localStorage.setItem(STORAGE_KEYS.PERMISSIONS, JSON.stringify(permissions))
          localStorage.setItem(STORAGE_KEYS.ROLES, JSON.stringify(roles))

          set({
            permissions,
            roles,
            loading: false,
            error: null
          })

          console.log('权限刷新成功:', permissions)
        } catch (error) {
          console.error('刷新权限失败:', error)
          const errorMessage = error instanceof Error ? error.message : '刷新权限失败'
          set({ loading: false, error: errorMessage })
          throw error
        }
      },

      /**
       * 更新用户信息
       */
      updateUserInfo: (userInfo: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userInfo }
          localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(updatedUser))
          set({ user: updatedUser })
        }
      },

      /**
       * 清除错误信息
       */
      clearError: () => {
        set({ error: null })
      },

      /**
       * 设置加载状态
       */
      setLoading: (loading: boolean) => {
        set({ loading })
      },

      /**
       * 初始化认证状态 - 从localStorage恢复数据
       */
      initializeAuth: () => {
        try {
          const token = localStorage.getItem(STORAGE_KEYS.TOKEN)
          const userStr = localStorage.getItem(STORAGE_KEYS.USER_INFO)
          const permissionsStr = localStorage.getItem(STORAGE_KEYS.PERMISSIONS)
          const rolesStr = localStorage.getItem(STORAGE_KEYS.ROLES)
          const menusStr = localStorage.getItem(STORAGE_KEYS.MENUS)

          if (token && userStr && permissionsStr && rolesStr) {
            const user = JSON.parse(userStr)
            const permissions = JSON.parse(permissionsStr)
            const roles = JSON.parse(rolesStr)
            const menus = menusStr ? JSON.parse(menusStr) : []
            set({
              isAuthenticated: true,
              user,
              token,
              permissions,
              roles,
              menus,
              loading: false,
              error: null,
            })
          } else {
            console.log('localStorage中没有完整的认证数据，保持未认证状态')
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              permissions: [],
              roles: [],
              loading: false,
              error: null,
            })
          }
        } catch (error) {
          console.error('初始化认证状态失败:', error)
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            permissions: [],
            roles: [],
            loading: false,
            error: '认证状态初始化失败',
          })
        }
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        // 持久化认证相关的核心状态
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        permissions: state.permissions,
        roles: state.roles,
      }),
    }
  )
)

/**
 * 权限检查Hook - 支持超级管理员通配符权限，增强调试功能
 */
export const usePermission = () => {
  const { permissions, roles } = useAuthStore()

  /**
   * 检查是否为超级管理员（拥有通配符权限）
   */
  const isSuperAdmin = (): boolean => {
    const hasWildcardPermission = permissions.includes('*:*:*')
    const hasSuperAdminRole = roles.includes('SUPER_ADMIN')
    const result = hasWildcardPermission || hasSuperAdminRole
    return result
  }

  /**
   * 检查是否有指定权限 - 支持通配符权限，增强调试功能
   */
  const hasPermission = (permission: string): boolean => {
    // 超级管理员拥有所有权限
    const superAdmin = isSuperAdmin()
    if (superAdmin) {
      return true
    }

    // 精确匹配
    if (permissions.includes(permission)) {
      console.log(`🔍 [权限匹配] "${permission}" - 精确匹配成功`)
      return true
    }

    // 通配符匹配
    const parts = permission.split(':')
    for (const perm of permissions) {
      if (perm.includes('*')) {
        const permParts = perm.split(':')
        let match = true
        for (let i = 0; i < Math.max(parts.length, permParts.length); i++) {
          const permPart = permParts[i] || ''
          const reqPart = parts[i] || ''
          if (permPart !== '*' && permPart !== reqPart) {
            match = false
            break
          }
        }
        if (match) {
          console.log(`🔍 [权限匹配] "${permission}" - 通配符匹配成功，匹配权限: "${perm}"`)
          return true
        }
      }
    }

    console.log(`🔍 [权限匹配] "${permission}" - 匹配失败，用户权限:`, permissions)
    return false
  }


  /**
   * 检查是否有所有权限 - 增强调试功能
   */
  const hasAllPermissions = (permissionList: string[]): boolean => {
    // 超级管理员拥有所有权限
    const superAdmin = isSuperAdmin()
    if (superAdmin) {
      console.log('🔑 [权限检查] 超级管理员，拥有所有权限')
      return true
    }

    console.log('🔑 [权限检查] 开始检查权限:', {
      需要的权限: permissionList,
      用户拥有的权限: permissions,
      用户角色: roles
    })

    const result = permissionList.every(permission => {
      const hasThisPermission = hasPermission(permission)
      console.log(`🔑 [权限检查] 检查权限 "${permission}":`, hasThisPermission ? '✅ 有权限' : '❌ 无权限')
      return hasThisPermission
    })

    console.log('🔑 [权限检查] 最终结果:', result ? '✅ 通过' : '❌ 拒绝')
    return result
  }

  /**
   * 检查是否有任一权限 - 增强调试功能
   */
  const hasAnyPermission = (permissionList: string[]): boolean => {
    // 超级管理员拥有所有权限
    const superAdmin = isSuperAdmin()
    if (superAdmin) {
      console.log('🔑 [权限检查] 超级管理员，拥有所有权限')
      return true
    }

    console.log('🔑 [权限检查] 开始检查任一权限:', {
      需要的权限: permissionList,
      用户拥有的权限: permissions,
      用户角色: roles
    })

    const result = permissionList.some(permission => {
      const hasThisPermission = hasPermission(permission)
      console.log(`🔑 [权限检查] 检查权限 "${permission}":`, hasThisPermission ? '✅ 有权限' : '❌ 无权限')
      return hasThisPermission
    })

    console.log('🔑 [权限检查] 任一权限检查结果:', result ? '✅ 通过' : '❌ 拒绝')
    return result
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string): boolean => {
    return roles.includes(role)
  }

  /**
   * 检查是否有任一角色
   */
  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(role => roles.includes(role))
  }

  /**
   * 检查是否有所有角色
   */
  const hasAllRoles = (roleList: string[]): boolean => {
    return roleList.every(role => roles.includes(role))
  }

  return {
    permissions,
    roles,
    isSuperAdmin,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
  }
}
