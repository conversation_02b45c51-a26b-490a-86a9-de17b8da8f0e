// 导出所有状态管理
export { useAuthStore, usePermission } from './auth.ts'
export {
  useAppStore,
  useTheme,
  useSidebar,
  useBreadcrumbs,
  useGlobalState
} from './app.ts'
export { useCaptchaStore, useCaptcha } from './captcha.ts'

// 导入store以便在初始化函数中使用
import { useAppStore } from './app.ts'

// 基于context7 Zustand最佳实践的状态初始化函数
export const initializeStores = () => {
  // 延迟初始化，确保stores已经创建
  if (typeof window !== 'undefined') {
    // 在浏览器环境中初始化
    setTimeout(() => {
      try {
        // 安全地获取store状态
        const appStoreState = useAppStore.getState()
        const { theme, setTheme } = appStoreState

        // 初始化主题
        setTheme(theme)

        // 监听系统主题变化
        if (theme === 'system') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
          const handleThemeChange = (e: MediaQueryListEvent) => {
            const root = document.documentElement
            if (e.matches) {
              root.classList.add('dark')
            } else {
              root.classList.remove('dark')
            }
          }

          mediaQuery.addEventListener('change', handleThemeChange)

          // 初始设置
          handleThemeChange({ matches: mediaQuery.matches } as MediaQueryListEvent)
        }

        console.log('🏪 状态管理初始化完成')
      } catch (error) {
        console.error('❌ 状态管理初始化失败:', error)
      }
    }, 0)
  }
}
