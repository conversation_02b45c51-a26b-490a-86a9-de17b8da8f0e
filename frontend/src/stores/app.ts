import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { STORAGE_KEYS, THEME, LANGUAGE } from '../constants'

/**
 * 应用状态接口
 */
interface AppState {
  // 主题设置
  theme: string
  
  // 语言设置
  language: string
  
  // 侧边栏状态
  sidebarCollapsed: boolean
  
  // 面包屑导航
  breadcrumbs: Array<{
    title: string
    path?: string
  }>
  
  // 全局加载状态
  globalLoading: boolean
  
  // 全局错误信息
  globalError: string | null
  
  // 租户信息
  tenantId: string | null
  tenantName: string | null
  
  // 操作方法
  setTheme: (theme: string) => void
  setLanguage: (language: string) => void
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setBreadcrumbs: (breadcrumbs: Array<{ title: string; path?: string }>) => void
  setGlobalLoading: (loading: boolean) => void
  setGlobalError: (error: string | null) => void
  setTenantInfo: (tenantId: string | null, tenantName?: string | null) => void
  clearGlobalError: () => void
}

/**
 * 创建应用状态管理
 */
export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      theme: THEME.SYSTEM,
      language: LANGUAGE.ZH_CN,
      sidebarCollapsed: false,
      breadcrumbs: [],
      globalLoading: false,
      globalError: null,
      tenantId: null,
      tenantName: null,

      /**
       * 设置主题
       */
      setTheme: (theme: string) => {
        set({ theme })
        
        // 应用主题到DOM
        const root = document.documentElement
        if (theme === THEME.DARK) {
          root.classList.add('dark')
        } else if (theme === THEME.LIGHT) {
          root.classList.remove('dark')
        } else {
          // 系统主题
          const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          if (isDark) {
            root.classList.add('dark')
          } else {
            root.classList.remove('dark')
          }
        }
        
        console.log(' 主题已切换:', theme)
      },

      /**
       * 设置语言
       */
      setLanguage: (language: string) => {
        set({ language })
        
        // 设置HTML lang属性
        document.documentElement.lang = language
        
        console.log(' 语言已切换:', language)
      },

      /**
       * 切换侧边栏状态
       */
      toggleSidebar: () => {
        const collapsed = !get().sidebarCollapsed
        set({ sidebarCollapsed: collapsed })
        console.log('侧边栏状态:', collapsed ? '收起' : '展开')
      },

      /**
       * 设置侧边栏状态
       */
      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed })
      },

      /**
       * 设置面包屑导航
       */
      setBreadcrumbs: (breadcrumbs) => {
        const currentState = get()
        // 深度比较面包屑，避免不必要的更新
        const isSame = JSON.stringify(currentState.breadcrumbs) === JSON.stringify(breadcrumbs)
        if (!isSame) {
          set({ breadcrumbs })
        } else {
        }
      },

      /**
       * 设置全局加载状态
       */
      setGlobalLoading: (loading: boolean) => {
        const currentState = get()
        // 只有当状态真正改变时才更新，避免无限循环
        if (currentState.globalLoading !== loading) {
          set({ globalLoading: loading })
        }
      },

      /**
       * 设置全局错误信息
       */
      setGlobalError: (error: string | null) => {
        set({ globalError: error })
        if (error) {
          console.error(' 全局错误:', error)
        }
      },

      /**
       * 设置租户信息
       */
      setTenantInfo: (tenantId: string | null, tenantName?: string | null) => {
        set({ 
          tenantId, 
          tenantName: tenantName || null 
        })
        
        // 同步到localStorage
        if (tenantId) {
          localStorage.setItem(STORAGE_KEYS.TENANT_ID, tenantId)
        } else {
          localStorage.removeItem(STORAGE_KEYS.TENANT_ID)
        }
        
        console.log(' 租户信息已更新:', { tenantId, tenantName })
      },

      /**
       * 清除全局错误
       */
      clearGlobalError: () => {
        set({ globalError: null })
      },
    }),
    {
      name: 'app-store',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
        tenantId: state.tenantId,
        tenantName: state.tenantName,
      }),
    }
  )
)

/**
 * 主题Hook
 */
export const useTheme = () => {
  const { theme, setTheme } = useAppStore()
  
  /**
   * 切换主题
   */
  const toggleTheme = () => {
    const newTheme = theme === THEME.LIGHT ? THEME.DARK : THEME.LIGHT
    setTheme(newTheme)
  }
  
  /**
   * 获取当前实际主题（解析系统主题）
   */
  const getActualTheme = (): 'light' | 'dark' => {
    if (theme === THEME.SYSTEM) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return theme as 'light' | 'dark'
  }
  
  return {
    theme,
    actualTheme: getActualTheme(),
    setTheme,
    toggleTheme,
  }
}

/**
 * 侧边栏Hook
 */
export const useSidebar = () => {
  const { sidebarCollapsed, toggleSidebar, setSidebarCollapsed } = useAppStore()
  
  return {
    collapsed: sidebarCollapsed,
    toggle: toggleSidebar,
    setCollapsed: setSidebarCollapsed,
  }
}

/**
 * 面包屑Hook
 */
export const useBreadcrumbs = () => {
  const { breadcrumbs, setBreadcrumbs } = useAppStore()
  
  /**
   * 添加面包屑项
   */
  const addBreadcrumb = (item: { title: string; path?: string }) => {
    const newBreadcrumbs = [...breadcrumbs, item]
    setBreadcrumbs(newBreadcrumbs)
  }
  
  /**
   * 移除面包屑项
   */
  const removeBreadcrumb = (index: number) => {
    const newBreadcrumbs = breadcrumbs.filter((_, i) => i !== index)
    setBreadcrumbs(newBreadcrumbs)
  }
  
  /**
   * 清空面包屑
   */
  const clearBreadcrumbs = () => {
    setBreadcrumbs([])
  }
  
  return {
    breadcrumbs,
    setBreadcrumbs,
    addBreadcrumb,
    removeBreadcrumb,
    clearBreadcrumbs,
  }
}

/**
 * 全局状态Hook
 */
export const useGlobalState = () => {
  const { 
    globalLoading, 
    globalError, 
    setGlobalLoading, 
    setGlobalError, 
    clearGlobalError 
  } = useAppStore()
  
  return {
    loading: globalLoading,
    error: globalError,
    setLoading: setGlobalLoading,
    setError: setGlobalError,
    clearError: clearGlobalError,
  }
}
