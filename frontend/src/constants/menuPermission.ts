/**
 * 菜单权限相关常量
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 菜单类型常量（与后端保持一致）
 */
export const MENU_TYPE = {
  /** 目录 */
  DIRECTORY: 0,
  /** 菜单 */
  MENU: 1,
  /** 按钮(权限点) */
  BUTTON: 2
} as const

/**
 * 菜单类型标签
 */
export const MENU_TYPE_LABELS = {
  [MENU_TYPE.DIRECTORY]: '目录',
  [MENU_TYPE.MENU]: '菜单',
  [MENU_TYPE.BUTTON]: '按钮'
} as const

/**
 * 菜单类型图标
 */
export const MENU_TYPE_ICONS = {
  [MENU_TYPE.DIRECTORY]: 'Folder',
  [MENU_TYPE.MENU]: 'FileText',
  [MENU_TYPE.BUTTON]: 'MousePointer'
} as const

/**
 * 菜单类型颜色
 */
export const MENU_TYPE_COLORS = {
  [MENU_TYPE.DIRECTORY]: 'blue',
  [MENU_TYPE.MENU]: 'green',
  [MENU_TYPE.BUTTON]: 'orange'
} as const

/**
 * 导航菜单类型（在左侧菜单栏显示）
 */
export const NAVIGATION_MENU_TYPES = [
  MENU_TYPE.DIRECTORY,
  MENU_TYPE.MENU
] as const

/**
 * 权限菜单类型（作为权限点）
 */
export const PERMISSION_MENU_TYPES = [
  MENU_TYPE.BUTTON
] as const

/**
 * 常用权限编码前缀
 */
export const PERMISSION_CODE_PREFIXES = {
  SYSTEM: 'system',
  USER: 'system:user',
  ROLE: 'system:role',
  MENU: 'system:menu',
  DEPT: 'system:dept',
  TENANT: 'system:tenant',
  MONITOR: 'monitor',
  FINANCE: 'finance',
  OPERATION: 'operation'
} as const

/**
 * 常用权限操作后缀
 */
export const PERMISSION_ACTIONS = {
  VIEW: 'view',
  LIST: 'list',
  ADD: 'add',
  EDIT: 'edit',
  DELETE: 'delete',
  EXPORT: 'export',
  IMPORT: 'import',
  ASSIGN: 'assign',
  RESET: 'reset',
  ENABLE: 'enable',
  DISABLE: 'disable'
} as const

/**
 * 权限编码模板
 */
export const PERMISSION_CODE_TEMPLATES = {
  // 用户管理权限
  USER_VIEW: 'system:user:view',
  USER_LIST: 'system:user:list',
  USER_ADD: 'system:user:add',
  USER_EDIT: 'system:user:edit',
  USER_DELETE: 'system:user:delete',
  USER_RESET_PASSWORD: 'system:user:reset-password',
  USER_ASSIGN_ROLE: 'system:user:assign-role',
  
  // 角色管理权限
  ROLE_VIEW: 'system:role:view',
  ROLE_LIST: 'system:role:list',
  ROLE_ADD: 'system:role:add',
  ROLE_EDIT: 'system:role:edit',
  ROLE_DELETE: 'system:role:delete',
  ROLE_ASSIGN_MENU: 'system:role:assign-menu',
  
  // 菜单管理权限
  MENU_VIEW: 'system:menu:view',
  MENU_LIST: 'system:menu:list',
  MENU_ADD: 'system:menu:add',
  MENU_EDIT: 'system:menu:edit',
  MENU_DELETE: 'system:menu:delete',
  
  // 部门管理权限
  DEPT_VIEW: 'system:dept:view',
  DEPT_LIST: 'system:dept:list',
  DEPT_ADD: 'system:dept:add',
  DEPT_EDIT: 'system:dept:edit',
  DEPT_DELETE: 'system:dept:delete',
  
  // 租户管理权限
  TENANT_VIEW: 'system:tenant:view',
  TENANT_LIST: 'system:tenant:list',
  TENANT_ADD: 'system:tenant:add',
  TENANT_EDIT: 'system:tenant:edit',
  TENANT_DELETE: 'system:tenant:delete'
} as const

/**
 * 权限编码验证规则
 */
export const PERMISSION_CODE_RULES = {
  // 权限编码格式：模块:资源:操作
  PATTERN: /^[a-z][a-z0-9]*(?::[a-z][a-z0-9-]*)*$/,
  // 最小长度
  MIN_LENGTH: 3,
  // 最大长度
  MAX_LENGTH: 100,
  // 分隔符
  SEPARATOR: ':',
  // 允许的字符
  ALLOWED_CHARS: /^[a-z0-9:-]+$/
} as const

/**
 * 工具函数
 */

/**
 * 判断是否为导航菜单类型
 */
export const isNavigationMenuType = (type: number): boolean => {
  return NAVIGATION_MENU_TYPES.includes(type as any)
}

/**
 * 判断是否为权限菜单类型
 */
export const isPermissionMenuType = (type: number): boolean => {
  return PERMISSION_MENU_TYPES.includes(type as any)
}

/**
 * 获取菜单类型标签
 */
export const getMenuTypeLabel = (type: number): string => {
  return MENU_TYPE_LABELS[type as keyof typeof MENU_TYPE_LABELS] || '未知'
}

/**
 * 获取菜单类型图标
 */
export const getMenuTypeIcon = (type: number): string => {
  return MENU_TYPE_ICONS[type as keyof typeof MENU_TYPE_ICONS] || 'HelpCircle'
}

/**
 * 获取菜单类型颜色
 */
export const getMenuTypeColor = (type: number): string => {
  return MENU_TYPE_COLORS[type as keyof typeof MENU_TYPE_COLORS] || 'gray'
}

/**
 * 验证权限编码格式
 */
export const validatePermissionCode = (code: string): boolean => {
  if (!code || code.length < PERMISSION_CODE_RULES.MIN_LENGTH || code.length > PERMISSION_CODE_RULES.MAX_LENGTH) {
    return false
  }
  return PERMISSION_CODE_RULES.PATTERN.test(code)
}

/**
 * 生成权限编码建议
 */
export const generatePermissionCodeSuggestion = (
  module: string,
  resource: string,
  action: string
): string => {
  const parts = [module, resource, action].filter(Boolean).map(part => 
    part.toLowerCase().replace(/[^a-z0-9-]/g, '-')
  )
  return parts.join(PERMISSION_CODE_RULES.SEPARATOR)
}

/**
 * 解析权限编码
 */
export const parsePermissionCode = (code: string): {
  module?: string
  resource?: string
  action?: string
  parts: string[]
} => {
  const parts = code.split(PERMISSION_CODE_RULES.SEPARATOR)
  return {
    module: parts[0],
    resource: parts[1],
    action: parts[2],
    parts
  }
}

/**
 * 权限编码自动完成建议
 */
export const getPermissionCodeSuggestions = (input: string): string[] => {
  const suggestions: string[] = []
  const inputLower = input.toLowerCase()
  
  // 从模板中查找匹配的权限编码
  Object.values(PERMISSION_CODE_TEMPLATES).forEach(template => {
    if (template.toLowerCase().includes(inputLower)) {
      suggestions.push(template)
    }
  })
  
  // 根据输入生成建议
  if (input.includes(':')) {
    const parts = input.split(':')
    if (parts.length === 2) {
      // 添加常用操作建议
      Object.values(PERMISSION_ACTIONS).forEach(action => {
        suggestions.push(`${input}:${action}`)
      })
    }
  } else {
    // 添加模块前缀建议
    Object.values(PERMISSION_CODE_PREFIXES).forEach(prefix => {
      if (prefix.toLowerCase().includes(inputLower)) {
        suggestions.push(prefix)
      }
    })
  }
  
  return [...new Set(suggestions)].slice(0, 10) // 去重并限制数量
}
