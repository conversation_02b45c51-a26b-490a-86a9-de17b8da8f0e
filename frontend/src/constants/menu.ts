/**
 * 菜单相关常量定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 菜单类型常量
 * 与后端保持一致：0-目录，1-菜单，2-按钮
 */
export const MENU_TYPE = {
  /** 目录 */
  DIRECTORY: 0,
  /** 菜单 */
  MENU: 1,
  /** 按钮 */
  BUTTON: 2
} as const

/**
 * 菜单类型标签映射
 */
export const MENU_TYPE_LABELS = {
  [MENU_TYPE.DIRECTORY]: '目录',
  [MENU_TYPE.MENU]: '菜单',
  [MENU_TYPE.BUTTON]: '按钮'
} as const

/**
 * 菜单状态常量
 */
export const MENU_STATUS = {
  /** 禁用 */
  DISABLED: 0,
  /** 启用 */
  ENABLED: 1
} as const

/**
 * 菜单可见性常量
 */
export const MENU_VISIBLE = {
  /** 隐藏 */
  HIDDEN: 0,
  /** 显示 */
  VISIBLE: 1
} as const

/**
 * 导航菜单类型（用于左侧菜单栏显示）
 * 只包含目录和菜单类型，不包含按钮类型
 */
export const NAVIGATION_MENU_TYPES = [
  MENU_TYPE.DIRECTORY,
  MENU_TYPE.MENU
] as const

/**
 * 判断是否为导航菜单类型
 * @param menuType 菜单类型
 * @returns 是否为导航菜单类型
 */
export const isNavigationMenuType = (menuType: number): boolean => {
  return NAVIGATION_MENU_TYPES.includes(menuType as any)
}

/**
 * 获取菜单类型标签
 * @param menuType 菜单类型
 * @returns 菜单类型标签
 */
export const getMenuTypeLabel = (menuType: number): string => {
  return MENU_TYPE_LABELS[menuType as keyof typeof MENU_TYPE_LABELS] || '未知'
}

/**
 * 菜单图标映射
 */
export const MENU_TYPE_ICONS = {
  [MENU_TYPE.DIRECTORY]: 'Folder',
  [MENU_TYPE.MENU]: 'FileText',
  [MENU_TYPE.BUTTON]: 'MousePointer'
} as const

/**
 * 获取菜单类型图标
 * @param menuType 菜单类型
 * @returns 菜单类型图标
 */
export const getMenuTypeIcon = (menuType: number): string => {
  return MENU_TYPE_ICONS[menuType as keyof typeof MENU_TYPE_ICONS] || 'HelpCircle'
}
