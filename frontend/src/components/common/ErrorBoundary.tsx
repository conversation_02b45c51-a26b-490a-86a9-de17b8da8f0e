import React, { Component, type ErrorInfo, type ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AppError } from '@/utils/error'

/**
 * 错误边界Props
 */
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: (error: AppError, retry: () => void) => ReactNode
  onError?: (error: AppError, errorInfo: ErrorInfo) => void
}

/**
 * 错误边界State
 */
interface ErrorBoundaryState {
  hasError: boolean
  error: AppError | null
}

/**
 * 默认错误回退组件
 */
const DefaultErrorFallback: React.FC<{
  error: AppError
  retry: () => void
}> = ({ error, retry }) => (
  <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
    <div className="text-center max-w-md">
      <div className="mb-4">
        <svg
          className="mx-auto h-12 w-12 text-destructive"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
      
      <h3 className="text-lg font-semibold text-foreground mb-2">
        页面出现错误
      </h3>
      
      <p className="text-muted-foreground mb-4">
        {ErrorHandler.formatMessage(error)}
      </p>
      
      <p className="text-sm text-muted-foreground mb-6">
        {ErrorHandler.getSuggestion(error)}
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          onClick={retry}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          重试
        </button>
        
        <button
          onClick={() => window.history.back()}
          className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
        >
          返回上一页
        </button>
      </div>
      
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-6 text-left">
          <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
            错误详情 (开发模式)
          </summary>
          <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-h-40">
            {error.stack}
          </pre>
        </details>
      )}
    </div>
  </div>
)

/**
 * React错误边界组件
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
    }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    const appError = ErrorHandler.handle(error)
    return {
      hasError: true,
      error: appError,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const appError = ErrorHandler.handle(error)
    
    // 调用错误处理回调
    this.props.onError?.(appError, errorInfo)
    
    // 记录错误日志
    console.error('🚨 React错误边界捕获错误:', {
      error: appError,
      errorInfo,
      componentStack: errorInfo.componentStack,
    })
  }

  /**
   * 重试方法
   */
  retry = () => {
    this.setState({
      hasError: false,
      error: null,
    })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // 如果提供了自定义fallback，使用自定义组件
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.retry)
      }
      
      // 否则使用默认错误组件
      return <DefaultErrorFallback error={this.state.error} retry={this.retry} />
    }

    return this.props.children
  }
}

/**
 * 错误边界Hook
 */

