import React, { useEffect, useCallback } from 'react'
import { RefreshCw, AlertCircle } from 'lucide-react'
import { Button } from '../ui'
import { Input } from '../ui'
import { useCaptcha } from '@/stores'
import { cn } from '@/utils'

/**
 * 验证码组件Props
 */
interface CaptchaProps {
  value?: string
  captchaId?: string
  onChange?: (value: string, captchaId: string) => void
  onError?: (error: string) => void
  className?: string
  disabled?: boolean
  placeholder?: string
  required?: boolean
  error?: boolean
  helperText?: string
}

/**
 * 验证码组件
 * 提供验证码图片显示、输入框和刷新功能
 */
export const Captcha: React.FC<CaptchaProps> = ({
  value = '',
  captchaId = '',
  onChange,
  onError,
  className,
  disabled = false,
  placeholder = '请输入验证码',
  error = false,
  helperText,
}) => {
  const {
    captcha,
    loading,
    error: captchaError,
    retryCount,
    maxRetries,
    isNetworkError,
    generateCaptcha,
    clearError,
    resetRetryCount,
  } = useCaptcha()

  /**
   * 刷新验证码
   */
  const refreshCaptcha = useCallback(async () => {
    if (!disabled && !loading) {
      try {
        await generateCaptcha()
        clearError()
      } catch (err) {
        const error = err as Error
        onError?.(error.message || '验证码生成失败')
      }
    }
  }, [disabled, loading, generateCaptcha, clearError, onError])

  // 监听错误状态变化，自动刷新验证码
  useEffect(() => {
    if (error && helperText && helperText.includes('验证码')) {
      // 延迟刷新，避免频繁请求
      const timer = setTimeout(() => {
        refreshCaptcha()
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [error, helperText, refreshCaptcha])

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange?.(newValue, captcha?.captchaId || '')
  }, [onChange, captcha?.captchaId])

  /**
   * 处理图片点击
   */
  const handleImageClick = useCallback(() => {
    refreshCaptcha()
  }, [refreshCaptcha])

  /**
   * 组件挂载时生成验证码（只在挂载时执行一次）
   */
  useEffect(() => {
    if (!captcha && !loading && retryCount < maxRetries) {
      generateCaptcha().catch((err) => {
        const error = err as Error
        onError?.(error.message || '验证码生成失败')
      })
    }
  }, []) // 空依赖数组，只在组件挂载时执行一次

  /**
   * 当验证码数据变化时，通知父组件
   */
  useEffect(() => {
    if (captcha?.captchaId && captcha.captchaId !== captchaId) {
      onChange?.(value, captcha.captchaId)
    }
  }, [captcha?.captchaId, captchaId, value, onChange])

  /**
   * 处理验证码错误
   */
  useEffect(() => {
    if (captchaError) {
      onError?.(captchaError)
    }
  }, [captchaError, onError])

  const isLoading = loading
  const hasError = error || !!captchaError

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center space-x-2">
        {/* 验证码输入框 */}
        <div className="flex-1">
          <Input
            type="text"
            value={value}
            onChange={handleInputChange}
            placeholder={placeholder}
            disabled={disabled || isLoading}
            maxLength={6}
            autoComplete="off"
            className="text-center"
          />
        </div>
        {/* 验证码图片 */}
        <div className="flex items-center space-x-1">
          {captcha?.captchaImage ? (
            <div
              className={cn(
                'relative border rounded cursor-pointer transition-opacity',
                'hover:opacity-80 active:opacity-60',
                hasError ? 'border-destructive' : 'border-input',
                disabled && 'cursor-not-allowed opacity-50'
              )}
              onClick={handleImageClick}
              title="点击刷新验证码"
              style={{
                width: 120,
                height: 40,
              }}
            >
              <img
                src={captcha.captchaImage}
                alt="验证码"
                className="w-full h-full object-cover rounded"
                draggable={false}
              />
              {isLoading && (
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center rounded">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
              )}
            </div>
          ) : (
            <div
              className={cn(
                'border rounded flex items-center justify-center bg-gray-50',
                hasError ? 'border-destructive' : 'border-input'
              )}
              style={{
                width: 120,
                height: 40,
              }}
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
              ) : (
                <span className="text-xs text-gray-400">验证码</span>
              )}
            </div>
          )}

          {/* 刷新按钮 */}
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={refreshCaptcha}
            disabled={disabled || isLoading || (retryCount >= maxRetries && !isNetworkError)}
            title={retryCount >= maxRetries ? "已达到最大重试次数" : "刷新验证码"}
          >
            <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
          </Button>
        </div>
      </div>

      {/* 帮助文本或错误信息 */}
      {(helperText || captchaError || (retryCount >= maxRetries)) && (
        <div className="space-y-2">
          <div className="flex items-start space-x-1">
            {hasError && <AlertCircle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />}
            <p className={cn(
              'text-sm',
              hasError ? 'text-destructive' : 'text-muted-foreground'
            )}>
              {captchaError || helperText || '点击图片或刷新按钮可重新生成验证码'}
            </p>
          </div>

          {/* 重试次数提示和手动重试按钮 */}
          {retryCount > 0 && (
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>重试次数: {retryCount}/{maxRetries}</span>
              {retryCount >= maxRetries && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    resetRetryCount()
                    refreshCaptcha()
                  }}
                  className="h-6 px-2 text-xs"
                >
                  手动重试
                </Button>
              )}
            </div>
          )}

          {/* 网络错误特殊提示 */}
          {isNetworkError && (
            <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
              <p>⚠️ 无法连接到服务器</p>
              <p>请检查：</p>
              <ul className="list-disc list-inside ml-2 mt-1">
                <li>网络连接是否正常</li>
                <li>后端服务是否已启动</li>
              </ul>
            </div>
          )}
        </div>
      )}


    </div>
  )
}