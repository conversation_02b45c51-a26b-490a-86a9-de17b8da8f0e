/**
 * 统一的数据分页组件
 * 
 * 提供一致的分页样式和交互体验
 * 支持完整的分页功能：页码跳转、页面大小选择、信息显示
 */

import React from 'react'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'
import { cn } from '@/lib/utils'

export interface DataPaginationProps {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  pageSize: number
  /** 总记录数 */
  total: number
  /** 页码变化回调 */
  onPageChange: (page: number) => void
  /** 页面大小变化回调 */
  onPageSizeChange?: (pageSize: number) => void
  /** 可选的页面大小选项 */
  pageSizeOptions?: number[]
  /** 是否显示页面大小选择器 */
  showSizeChanger?: boolean
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示总数信息 */
  showTotal?: boolean
  /** 最多显示的页码数量 */
  maxPageNumbers?: number
  /** 自定义样式类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 统一的数据分页组件
 */
export const DataPagination: React.FC<DataPaginationProps> = ({
  current,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showSizeChanger = true,
  showTotal = true,
  maxPageNumbers = 7,
  className,
  disabled = false
}) => {
  // 计算总页数
  const totalPages = Math.ceil((total || 0) / (pageSize || 10))
  
  // 如果只有一页或没有数据，不显示分页
  if (totalPages <= 1) {
    return null
  }

  // 计算显示的页码范围
  const getPageNumbers = () => {
    const pages: (number | 'ellipsis')[] = []
    
    if (totalPages <= maxPageNumbers) {
      // 总页数不超过最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 总页数超过最大显示数，需要省略
      const sidePages = Math.floor((maxPageNumbers - 3) / 2) // 两侧显示的页码数
      
      if (current <= sidePages + 2) {
        // 当前页在前部
        for (let i = 1; i <= maxPageNumbers - 2; i++) {
          pages.push(i)
        }
        pages.push('ellipsis')
        pages.push(totalPages)
      } else if (current >= totalPages - sidePages - 1) {
        // 当前页在后部
        pages.push(1)
        pages.push('ellipsis')
        for (let i = totalPages - maxPageNumbers + 3; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 当前页在中部
        pages.push(1)
        pages.push('ellipsis')
        for (let i = current - sidePages; i <= current + sidePages; i++) {
          pages.push(i)
        }
        pages.push('ellipsis')
        pages.push(totalPages)
      }
    }
    
    return pages
  }

  const pageNumbers = getPageNumbers()

  // 处理页码点击
  const handlePageClick = (page: number) => {
    if (disabled || page === current || page < 1 || page > totalPages || !onPageChange) {
      return
    }
    onPageChange(page)
  }

  // 处理页面大小变化
  const handlePageSizeChange = (newPageSize: string) => {
    if (disabled || !onPageSizeChange) {
      return
    }
    const size = parseInt(newPageSize)
    onPageSizeChange(size)
  }

  return (
    <div className={cn('flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-2 py-4', className)}>
      {/* 左侧：总数信息和页面大小选择 */}
      <div className="flex items-center gap-4 flex-wrap">
        {showTotal && (
          <div className="text-sm text-muted-foreground whitespace-nowrap">
            共 <span className="font-medium text-foreground">{total || 0}</span> 条记录
          </div>
        )}

        {showSizeChanger && onPageSizeChange && (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <span className="text-sm text-muted-foreground">每页</span>
            <Select
              value={pageSize?.toString() || '10'}
              onValueChange={handlePageSizeChange}
              disabled={disabled}
            >
              <SelectTrigger className="w-16 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map(size => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">条</span>
          </div>
        )}
      </div>

      {/* 右侧：分页控件 */}
      <div className="flex items-center gap-3 flex-wrap">
        <div className="text-xs text-muted-foreground whitespace-nowrap">
          第 <span className="font-medium text-foreground">{current || 1}</span> 页 / 共 <span className="font-medium text-foreground">{totalPages}</span> 页
        </div>
        
        <Pagination>
          <PaginationContent>
            {/* 上一页 */}
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  handlePageClick(current - 1)
                }}
                className={cn(
                  'cursor-pointer',
                  (current <= 1 || disabled) && 'pointer-events-none opacity-50'
                )}
                size="sm"
              />
            </PaginationItem>
            
            {/* 页码 */}
            {pageNumbers.map((page, index) => (
              <PaginationItem key={index}>
                {page === 'ellipsis' ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      handlePageClick(page)
                    }}
                    isActive={page === current}
                    className={cn(
                      'cursor-pointer',
                      disabled && 'pointer-events-none opacity-50'
                    )}
                    size="sm"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}
            
            {/* 下一页 */}
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  handlePageClick(current + 1)
                }}
                className={cn(
                  'cursor-pointer',
                  (current >= totalPages || disabled) && 'pointer-events-none opacity-50'
                )}
                size="sm"
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}

export default DataPagination
