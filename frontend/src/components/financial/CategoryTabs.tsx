import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs'
import { StatsCardGrid } from './StatsCard'
import { cn } from '@/utils'
import { smartFormatValue } from '@/utils/financial'
import type { 
  GroupedFinancialStatsResponse, 
  FinancialStatsItem, 
  StatsCardData 
} from '@/types/financial'
import { FinancialStatsCategory } from '@/types/financial'

/**
 * 分类标签页组件属性
 */
export interface CategoryTabsProps {
  /** 分组财务统计数据 */
  data?: GroupedFinancialStatsResponse | null
  /** 当前激活的标签页 */
  activeTab?: FinancialStatsCategory
  /** 标签页切换回调 */
  onTabChange?: (category: FinancialStatsCategory) => void
  /** 是否显示加载状态 */
  loading?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 卡片网格列数 */
  gridColumns?: 1 | 2 | 3 | 4 | 6
  /** 卡片点击事件 */
  onCardClick?: (item: FinancialStatsItem, category: FinancialStatsCategory) => void
}

/**
 * 标签页配置
 */
const TAB_CONFIG = [
  {
    key: FinancialStatsCategory.USER,
    label: '用户相关',
    description: '用户注册、充值、消费等统计数据',
    icon: '👥'
  },
  {
    key: FinancialStatsCategory.ANCHOR,
    label: '主播相关', 
    description: '主播收入、提现、分成等统计数据',
    icon: '🎭'
  },
  {
    key: FinancialStatsCategory.TOTAL,
    label: '合计统计',
    description: '平台整体收入、支出、利润等汇总数据',
    icon: '📊'
  },
  {
    key: FinancialStatsCategory.BUSINESS,
    label: '其他业务',
    description: '发货金额、利润金额等其他业务统计',
    icon: '💼'
  }
]

/**
 * 将财务统计项转换为卡片数据
 */
const convertToStatsCardData = (items: FinancialStatsItem[]): StatsCardData[] => {
  return items.map(item => ({
    title: item.statName,
    value: item.statValue,
    unit: item.unit,
    formattedValue: smartFormatValue(item.statValue, getValueType(item.unit))
  }))
}

/**
 * 根据单位推断数据类型
 */
const getValueType = (unit: string): 'currency' | 'percentage' | 'number' | 'integer' => {
  if (unit.includes('元') || unit.includes('¥') || unit.includes('yuan')) {
    return 'currency'
  }
  if (unit.includes('%') || unit.includes('percent')) {
    return 'percentage'
  }
  if (unit.includes('人') || unit.includes('次') || unit.includes('个')) {
    return 'integer'
  }
  return 'number'
}

/**
 * 空数据占位符组件
 */
const EmptyPlaceholder: React.FC<{ 
  category: string
  description: string
  icon: string 
}> = ({ category, description, icon }) => (
  <div className="flex flex-col items-center justify-center py-12 text-center">
    <div className="text-6xl mb-4">{icon}</div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      暂无{category}数据
    </h3>
    <p className="text-sm text-gray-500 max-w-sm">
      {description}
    </p>
  </div>
)

/**
 * 加载状态组件
 */
const LoadingPlaceholder: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    {Array.from({ length: 8 }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="bg-gray-200 rounded-lg p-6">
          <div className="h-4 bg-gray-300 rounded mb-2"></div>
          <div className="h-8 bg-gray-300 rounded mb-1"></div>
          <div className="h-3 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    ))}
  </div>
)

/**
 * 单个标签页内容组件
 */
const TabContent: React.FC<{
  items: FinancialStatsItem[]
  category: FinancialStatsCategory
  config: typeof TAB_CONFIG[0]
  loading: boolean
  gridColumns: number
  onCardClick?: (item: FinancialStatsItem, category: FinancialStatsCategory) => void
}> = ({ items, category, config, loading, gridColumns, onCardClick }) => {
  if (loading) {
    return <LoadingPlaceholder />
  }

  if (!items || items.length === 0) {
    return (
      <EmptyPlaceholder 
        category={config.label}
        description={config.description}
        icon={config.icon}
      />
    )
  }

  const cardData = convertToStatsCardData(items)

  return (
    <StatsCardGrid
      cards={cardData}
      columns={gridColumns as 1 | 2 | 3 | 4 | 6}
      loading={loading}
      onCardClick={(_, index) => {
        const originalItem = items[index]
        if (originalItem) {
          onCardClick?.(originalItem, category)
        }
      }}
    />
  )
}

/**
 * 分类标签页组件
 * 按分类组织财务统计数据，支持用户相关、主播相关、合计统计、其他业务统计四个分类
 */
export const CategoryTabs: React.FC<CategoryTabsProps> = ({
  data,
  activeTab = FinancialStatsCategory.TOTAL,
  onTabChange,
  loading = false,
  className,
  gridColumns = 4,
  onCardClick
}) => {
  /**
   * 获取标签页数据统计
   */
  const getTabStats = (category: FinancialStatsCategory): { count: number; hasData: boolean } => {
    if (!data) return { count: 0, hasData: false }
    
    let items: FinancialStatsItem[] = []
    switch (category) {
      case FinancialStatsCategory.USER:
        items = data.userStats || []
        break
      case FinancialStatsCategory.ANCHOR:
        items = data.anchorStats || []
        break
      case FinancialStatsCategory.TOTAL:
        items = data.totalStats || []
        break
      case FinancialStatsCategory.BUSINESS:
        items = data.businessStats || []
        break
    }
    
    return {
      count: items.length,
      hasData: items.length > 0
    }
  }

  /**
   * 获取标签页数据
   */
  const getTabData = (category: FinancialStatsCategory): FinancialStatsItem[] => {
    if (!data) return []
    
    switch (category) {
      case FinancialStatsCategory.USER:
        return data.userStats || []
      case FinancialStatsCategory.ANCHOR:
        return data.anchorStats || []
      case FinancialStatsCategory.TOTAL:
        return data.totalStats || []
      case FinancialStatsCategory.BUSINESS:
        return data.businessStats || []
      default:
        return []
    }
  }

  return (
    <div className={cn('w-full', className)}>
      <Tabs 
        value={activeTab} 
        onValueChange={(value) => onTabChange?.(value as FinancialStatsCategory)}
      >
        <TabsList className="grid w-full grid-cols-4">
          {TAB_CONFIG.map((config) => {
            const stats = getTabStats(config.key)
            return (
              <TabsTrigger 
                key={config.key} 
                value={config.key}
                className="flex items-center space-x-2"
              >
                <span>{config.icon}</span>
                <span>{config.label}</span>
                {!loading && (
                  <span className={cn(
                    'ml-1 px-1.5 py-0.5 text-xs rounded-full',
                    stats.hasData 
                      ? 'bg-primary/10 text-primary' 
                      : 'bg-gray-100 text-gray-500'
                  )}>
                    {stats.count}
                  </span>
                )}
              </TabsTrigger>
            )
          })}
        </TabsList>

        {TAB_CONFIG.map((config) => (
          <TabsContent key={config.key} value={config.key} className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <span className="mr-2">{config.icon}</span>
                {config.label}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {config.description}
              </p>
            </div>
            
            <TabContent
              items={getTabData(config.key)}
              category={config.key}
              config={config}
              loading={loading}
              gridColumns={gridColumns}
              onCardClick={onCardClick}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

/**
 * 简化版分类标签页组件
 * 只显示标签页切换，不包含内容
 */
export interface SimpleCategoryTabsProps {
  /** 当前激活的标签页 */
  activeTab?: FinancialStatsCategory
  /** 标签页切换回调 */
  onTabChange?: (category: FinancialStatsCategory) => void
  /** 数据统计（用于显示徽章） */
  stats?: Record<FinancialStatsCategory, number>
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义样式类名 */
  className?: string
}

export const SimpleCategoryTabs: React.FC<SimpleCategoryTabsProps> = ({
  activeTab = FinancialStatsCategory.TOTAL,
  onTabChange,
  stats,
  disabled = false,
  className
}) => {
  return (
    <div className={cn('w-full', className)}>
      <Tabs value={activeTab} onValueChange={(value) => onTabChange?.(value as FinancialStatsCategory)}>
        <TabsList className="grid w-full grid-cols-4">
          {TAB_CONFIG.map((config) => {
            const count = stats?.[config.key] || 0
            return (
              <TabsTrigger 
                key={config.key} 
                value={config.key}
                disabled={disabled}
                className="flex items-center space-x-2"
              >
                <span>{config.icon}</span>
                <span>{config.label}</span>
                {stats && (
                  <span className={cn(
                    'ml-1 px-1.5 py-0.5 text-xs rounded-full',
                    count > 0 
                      ? 'bg-primary/10 text-primary' 
                      : 'bg-gray-100 text-gray-500'
                  )}>
                    {count}
                  </span>
                )}
              </TabsTrigger>
            )
          })}
        </TabsList>
      </Tabs>
    </div>
  )
}

export default CategoryTabs