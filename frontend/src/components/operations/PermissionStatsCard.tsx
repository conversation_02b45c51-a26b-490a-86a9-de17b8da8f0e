/**
 * 带权限控制的统计卡片组件
 * 
 * 基于用户权限控制统计卡片的显示
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import { Lock, Eye, EyeOff } from 'lucide-react'
import { usePermissionControl } from '@/hooks/usePermissionControl'
import { cn } from '@/lib/utils'

export interface PermissionStatsCardProps {
  /**
   * 卡片标题
   */
  title: string
  
  /**
   * 卡片值
   */
  value: string | number
  
  /**
   * 单位
   */
  unit?: string
  
  /**
   * 描述信息
   */
  description?: string
  
  /**
   * 图标
   */
  icon?: React.ReactNode
  
  /**
   * 趋势信息
   */
  trend?: {
    value: string | number
    label: string
    direction?: 'up' | 'down' | 'stable'
  }
  
  /**
   * 需要的权限列表
   */
  permissions?: string[]
  
  /**
   * 需要的角色列表
   */
  roles?: string[]
  
  /**
   * 无权限时的行为
   * - 'hide': 完全隐藏卡片（默认）
   * - 'mask': 显示卡片但遮罩数据
   * - 'placeholder': 显示占位符内容
   */
  noPermissionBehavior?: 'hide' | 'mask' | 'placeholder'
  
  /**
   * 无权限时的提示文本
   */
  noPermissionMessage?: string
  
  /**
   * 是否显示加载状态
   */
  loading?: boolean
  
  /**
   * 自定义样式类名
   */
  className?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
}

/**
 * 带权限控制的统计卡片组件
 */
export const PermissionStatsCard: React.FC<PermissionStatsCardProps> = ({
  title,
  value,
  unit,
  description,
  icon,
  trend,
  permissions = [],
  roles = [],
  noPermissionBehavior = 'hide',
  noPermissionMessage = '权限不足',
  loading = false,
  className,
  onClick
}) => {
  const { hasAccess } = usePermissionControl({
    permissions,
    roles
  })

  // 格式化显示值
  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      return val.toLocaleString()
    }
    return val.toString()
  }

  // 渲染趋势指示器
  const renderTrend = () => {
    if (!trend) return null

    const trendColor = trend.direction === 'up' 
      ? 'text-green-600' 
      : trend.direction === 'down' 
        ? 'text-red-600' 
        : 'text-gray-600'

    return (
      <div className={`text-xs ${trendColor} mt-1`}>
        {trend.label}: {formatValue(trend.value)}
      </div>
    )
  }

  // 无权限时的处理
  if (!hasAccess) {
    switch (noPermissionBehavior) {
      case 'hide':
        return null
        
      case 'mask':
        return (
          <Card className={cn('transition-all duration-200', className)}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {title}
              </CardTitle>
              <div className="text-muted-foreground">
                {icon || <Lock className="h-4 w-4" />}
              </div>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <div className="text-2xl font-bold blur-sm select-none">
                  ****
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
                    <EyeOff className="h-3 w-3" />
                    <span>{noPermissionMessage}</span>
                  </div>
                </div>
              </div>
              {description && (
                <p className="text-xs text-muted-foreground mt-1 blur-sm select-none">
                  {description}
                </p>
              )}
            </CardContent>
          </Card>
        )
        
      case 'placeholder':
        return (
          <Card className={cn('transition-all duration-200 border-dashed', className)}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {title}
              </CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-center py-4">
                <EyeOff className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  {noPermissionMessage}
                </p>
              </div>
            </CardContent>
          </Card>
        )
        
      default:
        return null
    }
  }

  // 有权限，正常渲染
  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md',
        onClick && 'cursor-pointer hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon || <Eye className="h-4 w-4" />}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          ) : (
            <>
              {formatValue(value)}
              {unit && <span className="text-sm font-normal text-muted-foreground ml-1">{unit}</span>}
            </>
          )}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
        {renderTrend()}
      </CardContent>
    </Card>
  )
}

/**
 * 权限统计卡片网格组件
 */
export interface PermissionStatsCardGridProps {
  /**
   * 卡片数据列表
   */
  cards: (PermissionStatsCardProps & { key?: string })[]
  
  /**
   * 网格列数
   */
  columns?: 1 | 2 | 3 | 4 | 6
  
  /**
   * 是否显示加载状态
   */
  loading?: boolean
  
  /**
   * 自定义样式类名
   */
  className?: string
  
  /**
   * 卡片点击事件
   */
  onCardClick?: (card: PermissionStatsCardProps, index: number) => void
}

export const PermissionStatsCardGrid: React.FC<PermissionStatsCardGridProps> = ({
  cards,
  columns = 4,
  loading = false,
  className,
  onCardClick
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  }

  return (
    <div className={cn('grid gap-4', gridCols[columns], className)}>
      {cards.map((card, index) => (
        <PermissionStatsCard
          key={card.key || `${card.title}-${index}`}
          {...card}
          loading={loading}
          onClick={() => onCardClick?.(card, index)}
        />
      ))}
    </div>
  )
}

export default PermissionStatsCard
