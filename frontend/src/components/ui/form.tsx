/**
 * Form组件
 *
 * 基于React Hook Form的表单组件
 * 支持完整的表单验证和状态管理
 */

import React, { createContext, useContext } from 'react'
import { cn } from '@/lib/utils'
import { Label } from './label'

// Form Context
interface FormContextValue {
  name?: string
}

const FormContext = createContext<FormContextValue>({})

// Form组件 - 支持React Hook Form
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
}

export const Form = React.forwardRef<HTMLFormElement, FormProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <FormContext.Provider value={{}}>
        <form
          ref={ref}
          className={cn('space-y-6', className)}
          {...props}
        >
          {children}
        </form>
      </FormContext.Provider>
    )
  }
)
Form.displayName = 'Form'

// FormField组件 - 支持控制器模式
export interface FormFieldProps {
  name?: string
  control?: any
  render?: (props: {
    field: {
      name: string
      value: any
      onChange: (value: any) => void
      onBlur: () => void
    }
    fieldState: {
      error?: { message?: string }
    }
  }) => React.ReactNode
  children?: React.ReactNode
}

const FormFieldContext = createContext<{
  name?: string
  error?: string
}>({})

export const FormField: React.FC<FormFieldProps> = ({
  name,
  control,
  render,
  children
}) => {
  // 如果有render函数，使用控制器模式
  if (render && name) {
    // 这里应该集成React Hook Form的Controller
    // 为了简化，我们先提供基础实现
    const field = {
      name,
      control, // 保留control以备将来使用
      value: '',
      onChange: () => {},
      onBlur: () => {}
    }
    const fieldState = { error: undefined }

    return (
      <FormFieldContext.Provider value={{ name }}>
        <div className="space-y-2">
          {render({ field, fieldState })}
        </div>
      </FormFieldContext.Provider>
    )
  }

  // 普通模式
  return (
    <FormFieldContext.Provider value={{ name }}>
      <div className="space-y-2">{children}</div>
    </FormFieldContext.Provider>
  )
}

export const useFormField = () => {
  const context = useContext(FormFieldContext)
  return context
}

// FormItem组件
export interface FormItemProps extends React.HTMLAttributes<HTMLDivElement> {}

export const FormItem = React.forwardRef<HTMLDivElement, FormItemProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('space-y-2', className)}
        {...props}
      />
    )
  }
)
FormItem.displayName = 'FormItem'

// FormLabel组件
export interface FormLabelProps extends React.ComponentProps<typeof Label> {}

export const FormLabel = React.forwardRef<
  React.ElementRef<typeof Label>,
  FormLabelProps
>(({ className, ...props }, ref) => {
  return (
    <Label
      ref={ref}
      className={cn('text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70', className)}
      {...props}
    />
  )
})
FormLabel.displayName = 'FormLabel'

// FormControl组件
export interface FormControlProps extends React.HTMLAttributes<HTMLDivElement> {}

export const FormControl = React.forwardRef<HTMLDivElement, FormControlProps>(
  ({ ...props }, ref) => {
    return <div ref={ref} {...props} />
  }
)
FormControl.displayName = 'FormControl'

// FormDescription组件
export interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const FormDescription = React.forwardRef<HTMLParagraphElement, FormDescriptionProps>(
  ({ className, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn('text-sm text-muted-foreground', className)}
        {...props}
      />
    )
  }
)
FormDescription.displayName = 'FormDescription'

// FormMessage组件
export interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const FormMessage = React.forwardRef<HTMLParagraphElement, FormMessageProps>(
  ({ className, children, ...props }, ref) => {
    if (!children) return null
    
    return (
      <p
        ref={ref}
        className={cn('text-sm font-medium text-destructive', className)}
        {...props}
      >
        {children}
      </p>
    )
  }
)
FormMessage.displayName = 'FormMessage'
