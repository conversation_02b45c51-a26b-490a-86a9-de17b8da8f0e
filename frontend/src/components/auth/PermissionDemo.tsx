/**
 * 权限控制演示组件
 * 
 * 展示统一重构后的权限控制功能
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import { 
  PagePermissionWrapper, 
  PermissionToolbar, 
  TableRowActions,
  ActionPermissionButton 
} from './PermissionWrapper'
import { Plus, Edit, Trash2, Shield, Users, Download } from 'lucide-react'

/**
 * 权限控制演示页面
 */
export const PermissionDemo: React.FC = () => {
  const mockUser = {
    id: 1,
    username: 'demo',
    name: '演示用户',
    status: 1
  }

  return (
    <PagePermissionWrapper module="user">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">权限控制演示</h1>
          <p className="text-muted-foreground">
            展示统一重构后的权限控制功能
          </p>
        </div>

        {/* 工具栏演示 */}
        <Card>
          <CardHeader>
            <CardTitle>工具栏权限控制</CardTitle>
          </CardHeader>
          <CardContent>
            <PermissionToolbar
              module="user"
              primaryActions={[
                {
                  action: 'add',
                  config: {
                    text: '新增用户',
                    icon: Plus,
                    onClick: () => console.log('新增用户'),
                  }
                }
              ]}
              secondaryActions={[
                {
                  action: 'export',
                  config: {
                    text: '导出',
                    icon: Download,
                    variant: 'outline',
                    onClick: () => console.log('导出数据'),
                  }
                }
              ]}
            />
          </CardContent>
        </Card>

        {/* 单个按钮演示 */}
        <Card>
          <CardHeader>
            <CardTitle>单个按钮权限控制</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <ActionPermissionButton
                module="user"
                action="add"
                config={{
                  text: '新增用户',
                  icon: Plus,
                  onClick: () => console.log('新增用户'),
                }}
              />
              
              <ActionPermissionButton
                module="user"
                action="edit"
                config={{
                  text: '编辑用户',
                  icon: Edit,
                  variant: 'outline',
                  onClick: () => console.log('编辑用户'),
                }}
              />
              
              <ActionPermissionButton
                module="user"
                action="delete"
                config={{
                  text: '删除用户',
                  icon: Trash2,
                  variant: 'destructive',
                  onClick: () => console.log('删除用户'),
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* 表格操作按钮演示 */}
        <Card>
          <CardHeader>
            <CardTitle>表格操作按钮（图标模式）</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">演示用户</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                </div>
                <TableRowActions
                  module="user"
                  item={mockUser}
                  actions={[
                    {
                      action: 'edit',
                      text: '编辑',
                      icon: Edit,
                      onClick: (item) => console.log('编辑用户:', item),
                    },
                    {
                      action: 'edit',
                      text: '分配角色',
                      icon: Shield,
                      onClick: (item) => console.log('分配角色:', item),
                    },
                    {
                      action: 'edit',
                      text: '管理权限',
                      icon: Users,
                      onClick: (item) => console.log('管理权限:', item),
                    },
                    {
                      action: 'delete',
                      text: '删除',
                      icon: Trash2,
                      variant: 'ghost',
                      onClick: (item) => console.log('删除用户:', item),
                    }
                  ]}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 权限说明 */}
        <Card>
          <CardHeader>
            <CardTitle>权限控制说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">页面级权限控制</h4>
              <p className="text-sm text-muted-foreground">
                使用 <code>PagePermissionWrapper</code> 包装整个页面，自动检查用户是否有访问权限。
                需要 <code>system:view</code> 和 <code>system:user:list</code> 权限。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">按钮级权限控制</h4>
              <p className="text-sm text-muted-foreground">
                每个操作按钮都会根据对应的权限自动显示或隐藏。例如：
              </p>
              <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                <li>• 新增按钮需要 <code>system:user:add</code> 权限</li>
                <li>• 编辑按钮需要 <code>system:user:edit</code> 权限</li>
                <li>• 删除按钮需要 <code>system:user:delete</code> 权限</li>
                <li>• 导出按钮需要 <code>system:user:export</code> 权限</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">表格操作按钮</h4>
              <p className="text-sm text-muted-foreground">
                表格中的操作按钮使用图标模式，悬停时显示文字提示。
                每个按钮都有对应的权限控制，无权限时自动隐藏。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">超级管理员</h4>
              <p className="text-sm text-muted-foreground">
                拥有 <code>*:*:*</code> 通配符权限或 <code>SUPER_ADMIN</code> 角色的用户
                可以访问所有功能，无需单独的权限检查。
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PagePermissionWrapper>
  )
}

export default PermissionDemo
