/**
 * 菜单权限树组件 - 简化版本
 * 用于角色管理中的菜单权限分配
 *
 * 注意：这是一个简化的占位符组件，主要用于解决编译错误
 * 实际功能建议使用 MenuTree 组件替代
 */

import React from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface MenuPermissionTreeProps {
  /** 已选中的菜单ID列表 */
  selectedMenuIds?: number[]
  /** 选择变化回调 */
  onSelectionChange?: (selectedMenuIds: number[], selectedPermissionCodes: string[]) => void
  /** 是否只显示权限菜单 */
  permissionOnly?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 高度 */
  height?: number
}

const MenuPermissionTree: React.FC<MenuPermissionTreeProps> = ({
  selectedMenuIds = [],
  height = 400
}) => {
  // 简化的占位符实现
  return (
    <div className="p-4" style={{ height }}>
      <Alert>
        <AlertDescription>
          菜单权限树组件已简化。建议使用 MenuTree 组件替代此功能。
          <br />
          当前选中的菜单ID: {selectedMenuIds.join(', ') || '无'}
        </AlertDescription>
      </Alert>
    </div>
  )
}

export default MenuPermissionTree
