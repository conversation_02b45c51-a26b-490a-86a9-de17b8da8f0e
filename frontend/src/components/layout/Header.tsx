import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Menu,
  Bell,
  Settings,
  User,
  LogOut,
  Moon,
  Sun,
  Monitor,
  ChevronDown,
  RefreshCw
} from 'lucide-react'
import { useAuthStore, useSidebar, useTheme } from '../../stores'
import { Button } from '../ui'
import { cn } from '../../utils'

/**
 * 用户下拉菜单组件
 */
const UserDropdown: React.FC = () => {
  const navigate = useNavigate()
  const { user, logout, refreshPermissions } = useAuthStore()
  const [isOpen, setIsOpen] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const handleLogout = async () => {
    try {
      await logout()
      navigate('/login')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  const handleRefreshPermissions = async () => {
    try {
      setRefreshing(true)
      await refreshPermissions()
      setIsOpen(false)
      // 可以添加成功提示
      console.log('权限刷新成功')
    } catch (error) {
      console.error('权限刷新失败:', error)
      // 可以添加错误提示
    } finally {
      setRefreshing(false)
    }
  }

  const menuItems = [
    {
      icon: User,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      icon: Settings,
      label: '系统设置',
      onClick: () => navigate('/settings'),
    },
    {
      icon: RefreshCw,
      label: '刷新权限',
      onClick: handleRefreshPermissions,
      disabled: refreshing,
      className: refreshing ? 'opacity-50 cursor-not-allowed' : '',
    },
    {
      type: 'divider' as const,
    },
    {
      icon: LogOut,
      label: '退出登录',
      onClick: handleLogout,
      className: 'text-red-600 hover:text-red-700 hover:bg-red-50',
    },
  ]

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
      >
        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
          <User className="w-4 h-4 text-primary" />
        </div>
        <div className="hidden md:block text-left">
          <div className="text-sm font-medium">{user?.nickname || user?.username}</div>
          <div className="text-xs text-muted-foreground">{user?.email}</div>
        </div>
        <ChevronDown className={cn(
          "w-4 h-4 transition-transform",
          isOpen && "rotate-180"
        )} />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-[100]"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-[110]">
            <div className="py-1">
              {menuItems.map((item, index) => {
                if (item.type === 'divider') {
                  return <div key={index} className="border-t border-border my-1" />
                }
                
                const Icon = item.icon
                return (
                  <button
                    key={index}
                    onClick={() => {
                      item.onClick()
                      setIsOpen(false)
                    }}
                    className={cn(
                      "w-full flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors",
                      item.className
                    )}
                  >
                    <Icon className="w-4 h-4 mr-3" />
                    {item.label}
                  </button>
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

/**
 * 主题切换组件
 */
const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)

  const themes = [
    { value: 'light', label: '浅色', icon: Sun },
    { value: 'dark', label: '深色', icon: Moon },
    { value: 'system', label: '跟随系统', icon: Monitor },
  ]

  const currentTheme = themes.find(t => t.value === theme)
  const CurrentIcon = currentTheme?.icon || Sun

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="w-9 h-9"
      >
        <CurrentIcon className="w-4 h-4" />
      </Button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-[100]"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-36 bg-popover border border-border rounded-md shadow-lg z-[110]">
            <div className="py-1">
              {themes.map((themeOption) => {
                const Icon = themeOption.icon
                return (
                  <button
                    key={themeOption.value}
                    onClick={() => {
                      setTheme(themeOption.value)
                      setIsOpen(false)
                    }}
                    className={cn(
                      "w-full flex items-center px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors",
                      theme === themeOption.value && "bg-accent text-accent-foreground"
                    )}
                  >
                    <Icon className="w-4 h-4 mr-3" />
                    {themeOption.label}
                  </button>
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

/**
 * 顶部导航栏组件
 */
export const Header: React.FC = () => {
  const { toggle } = useSidebar()

  return (
    <header className="h-16 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between px-6 relative z-30">
      {/* 左侧 */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggle}
          className="w-9 h-9"
        >
          <Menu className="w-4 h-4" />
        </Button>
      </div>

      {/* 右侧 */}
      <div className="flex items-center space-x-2">
        {/* 通知 */}
        <Button variant="ghost" size="icon" className="w-9 h-9 relative">
          <Bell className="w-4 h-4" />
          <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </Button>

        {/* 主题切换 */}
        <ThemeToggle />

        {/* 用户菜单 */}
        <UserDropdown />
      </div>
    </header>
  )
}
