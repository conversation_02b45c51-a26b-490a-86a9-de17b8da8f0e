import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { ChevronRight, Home } from 'lucide-react'
import { useBreadcrumbs } from '@/stores'

/**
 * 面包屑导航组件
 */
export const Breadcrumb: React.FC = () => {
  const { breadcrumbs } = useBreadcrumbs()


  if (breadcrumbs.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {/* 首页链接 */}
      <Link
        to="/dashboard"
        className="flex items-center hover:text-foreground transition-colors"
      >
        <Home className="w-4 h-4" />
      </Link>

      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1

        return (
          <React.Fragment key={index}>
            <ChevronRight className="w-4 h-4" />
            
            {isLast || !item.path ? (
              <span className="text-foreground font-medium">
                {item.title}
              </span>
            ) : (
              <Link
                to={item.path}
                className="hover:text-foreground transition-colors"
              >
                {item.title}
              </Link>
            )}
          </React.Fragment>
        )
      })}
    </nav>
  )
}
