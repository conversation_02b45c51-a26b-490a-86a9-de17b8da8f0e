import React, { useState } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import {
  LayoutDashboard,
  Users,
  UserCheck,
  Shield,
  Settings,
  Activity,
  FileText,
  LogIn,
  Server,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { useSidebar, useAuthStore } from '@/stores'
import { generateMenusFromBackend, routes } from '@/router/routes.ts'
import { cn } from '@/utils'
import type { AppRoute } from '@/types'

/**
 * 图标映射
 */
const iconMap = {
  LayoutDashboard,
  Users,
  UserCheck,
  Shield,
  Settings,
  Activity,
  FileText,
  LogIn,
  Server,
}

/**
 * 获取图标组件
 */
const getIcon = (iconName?: string) => {
  if (!iconName || !(iconName in iconMap)) {
    return LayoutDashboard
  }
  return iconMap[iconName as keyof typeof iconMap]
}

/**
 * 菜单项组件
 */
interface MenuItemProps {
  route: AppRoute
  collapsed: boolean
  level?: number
}

const MenuItem: React.FC<MenuItemProps> = ({ route, collapsed, level = 0 }) => {
  const location = useLocation()
  const [isExpanded, setIsExpanded] = useState(false)

  const hasChildren = route.children && route.children.length > 0
  const isActive = location.pathname === route.path
  const isParentActive = route.children?.some(child => location.pathname === child.path)

  const Icon = getIcon(route.meta?.icon)
  
  // 如果有子菜单
  if (hasChildren) {
    return (
      <div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={cn(
            "w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
            "hover:bg-accent hover:text-accent-foreground",
            (isActive || isParentActive) && "bg-accent text-accent-foreground",
            collapsed && "justify-center px-2"
          )}
          style={{ paddingLeft: collapsed ? undefined : `${12 + level * 16}px` }}
        >
          <Icon className={cn("w-5 h-5", !collapsed && "mr-3")} />
          {!collapsed && (
            <>
              <span className="flex-1 text-left">{route.meta?.title}</span>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </>
          )}
        </button>
        
        {/* 子菜单 */}
        {!collapsed && isExpanded && (
          <div className="mt-1 space-y-1">
            {route.children?.map((child) => (
              <MenuItem
                key={child.path}
                route={child}
                collapsed={collapsed}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    )
  }
  
  // 普通菜单项
  return (
    <NavLink
      to={route.path}
      className={({ isActive }) => cn(
        "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
        "hover:bg-accent hover:text-accent-foreground",
        isActive && "bg-accent text-accent-foreground",
        collapsed && "justify-center px-2"
      )}
      style={{ paddingLeft: collapsed ? undefined : `${12 + level * 16}px` }}
    >
      <Icon className={cn("w-5 h-5", !collapsed && "mr-3")} />
      {!collapsed && <span>{route.meta?.title}</span>}
    </NavLink>
  )
}

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC = () => {
  const { collapsed } = useSidebar()
  const { menus } = useAuthStore()

  // 基于后端菜单数据生成前端菜单
  const menuRoutes = generateMenusFromBackend(routes, menus)

  // 调试信息
  console.log('Sidebar 调试信息:', {
    menus: menus,
    menusLength: menus?.length || 0,
    menuRoutes: menuRoutes,
    menuRoutesLength: menuRoutes?.length || 0
  })

  return (
    <aside className={cn(
      "fixed left-0 top-0 z-40 h-screen bg-background border-r border-border transition-all duration-300",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Logo区域 */}
      <div className="h-16 flex items-center justify-center border-b border-border">
        {collapsed ? (
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">J</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">J</span>
            </div>
            <span className="text-xl font-bold">jCloud</span>
          </div>
        )}
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 overflow-y-auto py-4 px-3">
        <div className="space-y-1">
          {menuRoutes.map((route) => (
            <MenuItem
              key={route.path}
              route={route}
              collapsed={collapsed}
            />
          ))}
        </div>
      </nav>

      {/* 底部信息 */}
      {!collapsed && (
        <div className="p-4 border-t border-border">
          <div className="text-xs text-muted-foreground text-center">
            © 2024 jCloud
          </div>
        </div>
      )}
    </aside>
  )
}
