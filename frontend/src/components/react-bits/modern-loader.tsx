import React from 'react';
import { cn } from '../../lib/utils';

export interface ModernLoaderProps {
  progress?: number;
  message?: string;
  showProgress?: boolean;
  className?: string;
}

export const ModernLoader: React.FC<ModernLoaderProps> = ({
  progress = 0,
  message = "加载中...",
  showProgress = false,
  className
}) => {
  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center",
      "bg-white dark:bg-gray-900",
      className
    )}>
      {/* 主加载容器 */}
      <div className="flex flex-col items-center space-y-6">
        {/* 品牌标识 */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            jCloud
          </h1>
        </div>
        
        {/* 简约加载动画 */}
        <div className="relative">
          <div className="w-12 h-12 border-3 border-gray-200 dark:border-gray-700 rounded-full animate-spin">
            <div className="w-full h-full border-t-3 border-blue-500 rounded-full" />
          </div>
        </div>
        
        {/* 加载信息 */}
        <div className="text-center space-y-3">
          <p className="text-gray-600 dark:text-gray-300">
            {message}
          </p>
          
          {showProgress && (
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                <span>进度</span>
                <span className="font-mono">{Math.round(progress)}%</span>
              </div>
              <div className="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};