import React, { useState } from 'react';
import { cn } from '../../lib/utils';

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  animation?: 'bounce' | 'pulse' | 'scale' | 'slide' | 'glow';
}

const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ className, variant = 'default', size = 'default', animation = 'scale', children, ...props }, ref) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isPressed, setIsPressed] = useState(false);

    const baseClasses = cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
      {
        // Variants
        'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',
        'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',
        'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',
        'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
        'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',
        'text-primary underline-offset-4 hover:underline': variant === 'link',
        
        // Sizes
        'h-10 px-4 py-2': size === 'default',
        'h-9 rounded-md px-3': size === 'sm',
        'h-11 rounded-md px-8': size === 'lg',
        'h-10 w-10': size === 'icon',
        
        // Animations
        'hover:scale-105 active:scale-95': animation === 'scale',
        'hover:animate-bounce': animation === 'bounce',
        'hover:animate-pulse': animation === 'pulse',
        'hover:translate-x-1 hover:-translate-y-1 hover:shadow-lg': animation === 'slide',
        'hover:shadow-lg hover:shadow-primary/25': animation === 'glow' && variant === 'default',
        'hover:shadow-lg hover:shadow-destructive/25': animation === 'glow' && variant === 'destructive',
      },
      className
    );

    const animationClasses = cn({
      'transform transition-transform duration-200': true,
      'scale-105': isHovered && animation === 'scale',
      'scale-95': isPressed && animation === 'scale',
      'translate-x-1 -translate-y-1': isHovered && animation === 'slide',
    });

    return (
      <button
        className={cn(baseClasses, animationClasses)}
        ref={ref}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => {
          setIsHovered(false);
          setIsPressed(false);
        }}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        {...props}
      >
        {children}
      </button>
    );
  }
);

AnimatedButton.displayName = 'AnimatedButton';

export { AnimatedButton };
export type { AnimatedButtonProps };