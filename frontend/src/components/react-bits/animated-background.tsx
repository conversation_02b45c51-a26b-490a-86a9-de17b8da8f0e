import React, { useEffect, useRef } from 'react';
import { cn } from '../../lib/utils';

interface AnimatedBackgroundProps {
  children?: React.ReactNode;
  variant?: 'particles' | 'gradient' | 'dots' | 'waves';
  className?: string;
  particleCount?: number;
  speed?: 'slow' | 'medium' | 'fast';
  color?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  children,
  variant = 'gradient',
  className,
  particleCount = 50,
  speed = 'medium',
  color = 'primary',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const particlesRef = useRef<Array<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
  }>>([]);

  useEffect(() => {
    if (variant !== 'particles' || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    const initParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * (speed === 'slow' ? 0.5 : speed === 'fast' ? 2 : 1),
          vy: (Math.random() - 0.5) * (speed === 'slow' ? 0.5 : speed === 'fast' ? 2 : 1),
          size: Math.random() * 3 + 1,
          opacity: Math.random() * 0.5 + 0.2,
        });
      }
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach((particle) => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
        
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`; // Blue particles
        ctx.fill();
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    initParticles();
    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [variant, particleCount, speed]);

  const getBackgroundClasses = () => {
    // 使用color参数来自定义颜色主题
    const colorTheme = color === 'primary' ? 'blue' : color;

    switch (variant) {
      case 'gradient':
        return `bg-gradient-to-br from-${colorTheme}-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900`;
      case 'dots':
        return 'bg-white dark:bg-gray-900 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#374151_1px,transparent_1px)] [background-size:16px_16px]';
      case 'waves':
        return `bg-gradient-to-r from-${colorTheme}-400 via-purple-500 to-pink-500 animate-gradient-x`;
      default:
        return 'bg-white dark:bg-gray-900';
    }
  };

  return (
    <div className={cn('relative min-h-screen overflow-hidden', getBackgroundClasses(), className)}>
      {variant === 'particles' && (
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />
      )}
      
      {variant === 'waves' && (
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-pulse" />
        </div>
      )}
      
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export { AnimatedBackground };
export type { AnimatedBackgroundProps };