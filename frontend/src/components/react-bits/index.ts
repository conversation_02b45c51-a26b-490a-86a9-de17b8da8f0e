// React Bits Animation Components
export { AnimatedButton } from './animated-button';
export type { AnimatedButtonProps } from './animated-button';

export { TypewriterText } from './typewriter-text';
export type { TypewriterTextProps } from './typewriter-text';

export { FadeIn } from './fade-in';
export type { FadeInProps } from './fade-in';

export { AnimatedCard } from './animated-card';
export type { AnimatedCardProps } from './animated-card';

export { AnimatedBackground } from './animated-background';
export type { AnimatedBackgroundProps } from './animated-background';

export { ModernLoader } from './modern-loader';
export type { ModernLoaderProps } from './modern-loader';

// Import all components first
import { AnimatedButton } from './animated-button';
import { TypewriterText } from './typewriter-text';
import { FadeIn } from './fade-in';
import { AnimatedCard } from './animated-card';
import { AnimatedBackground } from './animated-background';
import { ModernLoader } from './modern-loader';

// Re-export all components as a single object for convenience
export const ReactBits = {
  AnimatedButton,
  TypewriterText,
  FadeIn,
  AnimatedCard,
  AnimatedBackground,
  ModernLoader,
};

// Default export
export default ReactBits;