import React, { useState, useRef } from 'react';
import { cn } from '../../lib/utils';

interface AnimatedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'hover-lift' | 'tilt' | 'glow' | 'border-glow';
  intensity?: 'subtle' | 'medium' | 'strong';
  className?: string;
}

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  variant = 'default',
  intensity = 'medium',
  className,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current || variant !== 'tilt') return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    setMousePosition({
      x: (x - centerX) / centerX,
      y: (y - centerY) / centerY,
    });
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const getIntensityValue = () => {
    switch (intensity) {
      case 'subtle': return 0.5;
      case 'medium': return 1;
      case 'strong': return 1.5;
      default: return 1;
    }
  };

  const getTiltTransform = () => {
    if (variant !== 'tilt' || !isHovered) return '';
    const intensityVal = getIntensityValue();
    const rotateX = mousePosition.y * -10 * intensityVal;
    const rotateY = mousePosition.x * 10 * intensityVal;
    return `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
  };

  const baseClasses = cn(
    'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300 ease-out',
    {
      // Default variant
      'hover:shadow-md': variant === 'default',
      
      // Hover lift variant
      'hover:shadow-lg hover:-translate-y-1': variant === 'hover-lift',
      
      // Tilt variant
      'transform-gpu': variant === 'tilt',
      
      // Glow variant
      'hover:shadow-lg hover:shadow-primary/20': variant === 'glow',
      
      // Border glow variant
      'relative overflow-hidden': variant === 'border-glow',
      'hover:shadow-lg': variant === 'border-glow',
      
      // Intensity modifiers
      'hover:shadow-sm': intensity === 'subtle' && variant === 'hover-lift',
      'hover:shadow-xl': intensity === 'strong' && variant === 'hover-lift',
      'hover:-translate-y-0.5': intensity === 'subtle' && variant === 'hover-lift',
      'hover:-translate-y-2': intensity === 'strong' && variant === 'hover-lift',
    },
    className
  );

  return (
    <div
      ref={cardRef}
      className={baseClasses}
      style={{
        transform: getTiltTransform(),
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      {variant === 'border-glow' && isHovered && (
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 via-transparent to-primary/20 opacity-50 blur-sm" />
      )}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export { AnimatedCard };
export type { AnimatedCardProps };