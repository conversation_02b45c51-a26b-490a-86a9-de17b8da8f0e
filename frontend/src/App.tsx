import { useMount } from 'ahooks'
import { AppRouter } from './router'
import { ErrorBoundary } from './components/common/ErrorBoundary.tsx'
import { useStoreInitialization } from './hooks'
import { useAppStore, useAuthStore } from './stores'
import { Toaster } from './components/ui/toaster'
import { useState, useEffect } from 'react'
import { ModernLoader } from './components/react-bits'

const GlobalLoading = () => {
  const [progress, setProgress] = useState(0);
  const [loadingMessages] = useState([
    "正在初始化应用",
    "正在加载核心模块",
    "正在连接服务器",
    "正在验证用户权限",
    "正在准备工作空间"
  ]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  
  // 模拟进度更新和消息切换
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + Math.random() * 8 + 2, 100);
        
        // 根据进度切换消息
        const messageIndex = Math.floor((newProgress / 100) * loadingMessages.length);
        setCurrentMessageIndex(Math.min(messageIndex, loadingMessages.length - 1));
        
        return newProgress;
      });
    }, 300);
    
    return () => clearInterval(interval);
  }, [loadingMessages.length]);
  
  return (
    <ModernLoader 
      progress={progress}
      message={loadingMessages[currentMessageIndex]}
      showProgress={true}
    />
  );
};



/**
 * 主应用组件 - 基于Context7最佳实践的Zustand和axios集成
 */
function App() {
  // 初始化stores
  const { initialized } = useStoreInitialization()
  const { globalLoading } = useAppStore()
  const { initializeAuth } = useAuthStore()

  // 在应用启动时初始化认证状态
  useMount(() => {
    initializeAuth()
  })

  // 确保stores初始化完成后再渲染应用
  if (!initialized) {
    return <GlobalLoading />
  }

  return (
    <ErrorBoundary>
      <AppRouter />
      {/* 全局加载状态 */}
      {globalLoading && <GlobalLoading />}
      {/* Toast通知组件 */}
      <Toaster />
    </ErrorBoundary>
  )
}

export default App
