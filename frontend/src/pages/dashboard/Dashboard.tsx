import React from 'react'
import { Users, UserCheck, Shield, Activity } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui'
import { useAuthStore } from '../../stores'
import { usePageTitle } from '../../router/guards.tsx'
import { FadeIn, AnimatedCard, TypewriterText } from '../../components/react-bits'

/**
 * 统计卡片组件
 */
interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  className 
}) => {
  return (
    <AnimatedCard variant="hover-lift" className={className}>
      <Card className="h-full border-0 shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value}</div>
          {trend && (
            <p className="text-xs text-muted-foreground">
              <span className={trend.isPositive ? 'text-green-600' : 'text-red-600'}>
                {trend.isPositive ? '+' : ''}{trend.value}%
              </span>
              {' '}较上月
            </p>
          )}
        </CardContent>
      </Card>
    </AnimatedCard>
  )
}

/**
 * 仪表板页面组件
 */
const Dashboard: React.FC = () => {
  const { user } = useAuthStore()
  
  // 设置页面标题
  usePageTitle('仪表板')

  // 模拟统计数据
  const stats = [
    {
      title: '总用户数',
      value: '1,234',
      icon: Users,
      trend: { value: 12, isPositive: true },
    },
    {
      title: '活跃用户',
      value: '856',
      icon: Activity,
      trend: { value: 8, isPositive: true },
    },
    {
      title: '角色数量',
      value: '12',
      icon: UserCheck,
      trend: { value: 2, isPositive: true },
    },
    {
      title: '权限数量',
      value: '48',
      icon: Shield,
      trend: { value: -3, isPositive: false },
    },
  ]

  return (
    <div className="space-y-6">
      {/* 欢迎信息 */}
      <FadeIn direction="down" delay={0.1}>
        <div className="flex items-center justify-between">
          <div>
            <TypewriterText
              text={`欢迎回来，${user?.nickname || user?.username}！`}
              speed={50}
              delay={500}
              className="text-3xl font-bold tracking-tight"
            />
            <FadeIn direction="up" delay={0.8}>
              <p className="text-muted-foreground mt-2">
                这里是您的系统概览，查看最新的数据和活动。
              </p>
            </FadeIn>
          </div>
          <FadeIn direction="left" delay={0.6}>
            <div className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long',
              })}
            </div>
          </FadeIn>
        </div>
      </FadeIn>

      {/* 统计卡片 */}
      <FadeIn direction="up" delay={1.0}>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <FadeIn key={index} direction="up" delay={1.2 + index * 0.1}>
              <StatCard {...stat} />
            </FadeIn>
          ))}
        </div>
      </FadeIn>

      {/* 图表区域 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <FadeIn direction="left" delay={1.6}>
          <AnimatedCard variant="hover-lift" className="col-span-4">
            <Card className="h-full border-0 shadow-none">
              <CardHeader>
                <CardTitle>用户增长趋势</CardTitle>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                  图表组件待实现
                </div>
              </CardContent>
            </Card>
          </AnimatedCard>
        </FadeIn>
        
        <FadeIn direction="right" delay={1.8}>
          <AnimatedCard variant="hover-lift" className="col-span-3">
            <Card className="h-full border-0 shadow-none">
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { user: 'admin', action: '创建了新用户', time: '2分钟前' },
                    { user: 'manager', action: '修改了角色权限', time: '5分钟前' },
                    { user: 'user1', action: '登录系统', time: '10分钟前' },
                    { user: 'admin', action: '导出了用户数据', time: '15分钟前' },
                  ].map((activity, index) => (
                    <FadeIn key={index} direction="up" delay={2.0 + index * 0.1}>
                      <div className="flex items-center space-x-4">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {activity.user} {activity.action}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {activity.time}
                          </p>
                        </div>
                      </div>
                    </FadeIn>
                  ))}
                </div>
              </CardContent>
            </Card>
          </AnimatedCard>
        </FadeIn>
      </div>

      {/* 快捷操作 */}
      <FadeIn direction="up" delay={2.4}>
        <AnimatedCard variant="hover-lift">
          <Card className="border-0 shadow-none">
            <CardHeader>
              <CardTitle>快捷操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {[
                  { title: '用户管理', description: '管理系统用户', path: '/system/user' },
                  { title: '角色管理', description: '配置用户角色', path: '/system/role' },
                  { title: '权限管理', description: '设置系统权限', path: '/system/permission' },
                  { title: '系统设置', description: '系统配置管理', path: '/settings' },
                ].map((item, index) => (
                  <FadeIn key={index} direction="up" delay={2.6 + index * 0.1}>
                    <AnimatedCard variant="hover-lift" intensity="medium">
                      <div className="p-4 border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors cursor-pointer h-full">
                        <h3 className="font-medium">{item.title}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {item.description}
                        </p>
                      </div>
                    </AnimatedCard>
                  </FadeIn>
                ))}
              </div>
            </CardContent>
          </Card>
        </AnimatedCard>
      </FadeIn>
    </div>
  )
}

export default Dashboard
