import {create} from 'zustand'
import {devtools} from 'zustand/middleware'
import {MenuService} from '@/services'
import type {
  MenuAction,
  MenuCreateRequest,
  MenuFormState,
  MenuItem,
  MenuQueryRequest,
  MenuTreeNode, 
  MenuTreeState,
  MenuUpdateRequest
} from '../types'
/**
 * 菜单状态管理接口
 */
interface MenuState extends MenuTreeState, MenuFormState {
  // 数据操作方法
  fetchMenuTree: () => Promise<void>
  fetchMenuPage: (params: MenuQueryRequest) => Promise<void>
  createMenu: (data: MenuCreateRequest) => Promise<boolean>
  updateMenu: (data: MenuUpdateRequest) => Promise<boolean>
  deleteMenu: (id: number) => Promise<boolean>
  moveMenu: (id: number, newParentId: number) => Promise<boolean>
  updateMenuStatus: (id: number, status: number) => Promise<boolean>
  updateMenuVisible: (id: number, visible: number) => Promise<boolean>
  
  // 树形操作方法
  expandNode: (nodeId: number) => void
  collapseNode: (nodeId: number) => void
  selectNode: (nodeId: number) => void
  searchMenus: (keyword: string) => void
  filterMenus: (filters: Partial<MenuQueryRequest>) => void
  
  // 表单操作方法
  openForm: (action: MenuAction, menu?: MenuItem, parentId?: number) => void
  closeForm: () => void
  setFormLoading: (loading: boolean) => void
  
  // 工具方法
  findMenuById: (id: number) => MenuItem | undefined
  getMenuPath: (id: number) => MenuItem[]
  validateMenuName: (name: string, parentId: number, excludeId?: number) => Promise<boolean>
  validateMenuPath: (path: string, excludeId?: number) => Promise<boolean>
  
  // 重置方法
  reset: () => void
}

/**
 * 初始状态
 */
const initialState: MenuTreeState & MenuFormState = {
  // 树形状态
  treeData: [],
  expandedKeys: [],
  selectedKeys: [],
  loading: false,
  searchKeyword: '',
  filters: {},

  // 表单状态
  action: 'view' as MenuAction,
  visible: false,
  currentMenu: undefined,
  parentId: undefined,
}

/**
 * 菜单状态管理
 */
export const useMenuStore = create<MenuState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      /**
       * 获取菜单树数据
       */
      fetchMenuTree: async () => {
        try {
          set({ loading: true })

          // 使用重构后的HTTP响应处理，直接返回业务数据
          const data = await MenuService.getMenuTree()
          // 确保data是数组类型
          const menuArray = Array.isArray(data) ? data : []
          const treeData = convertToTreeNodes(menuArray)
          set({
            treeData,
            loading: false
          })
        } catch (error) {
          console.error('获取菜单树异常:', error)
          set({ loading: false })
        }
      },

      /**
       * 创建菜单
       */
      createMenu: async (data: MenuCreateRequest): Promise<boolean> => {
        try {
          set({ loading: true })

          // 使用重构后的HTTP响应处理，直接返回业务数据
          await MenuService.createMenu(data)

          // 重新获取菜单树数据
          await get().fetchMenuTree()
          return true
        } catch (error) {
          console.error('创建菜单失败:', error)
          return false
        } finally {
          set({ loading: false })
        }
      },

      /**
       * 更新菜单
       */
      updateMenu: async (data: MenuUpdateRequest): Promise<boolean> => {
        try {
          set({ loading: true })

          // 使用重构后的HTTP响应处理，直接返回业务数据
          await MenuService.updateMenu(data)

          // 重新获取菜单树数据
          await get().fetchMenuTree()
          return true
        } catch (error) {
          console.error('更新菜单失败:', error)
          return false
        } finally {
          set({ loading: false })
        }
      },

      /**
       * 删除菜单
       */
      deleteMenu: async (id: number): Promise<boolean> => {
        try {
          set({ loading: true })

          // 使用重构后的HTTP响应处理，直接返回业务数据
          await MenuService.deleteMenu(id)

          // 重新获取菜单树数据
          await get().fetchMenuTree()
          return true
        } catch (error) {
          console.error('删除菜单失败:', error)
          return false
        } finally {
          set({ loading: false })
        }
      },

      /**
       * 更新菜单状态
       */
      updateMenuStatus: async (id: number, status: number): Promise<boolean> => {
        try {

          const response = await MenuService.updateMenuStatus(id, status)
          
          if (response.success) {
            // 重新获取菜单树数据
            await get().fetchMenuTree()
            return true
          } else {
            return false
          }
        } catch (error) {
          return false
        }
      },

      /**
       * 更新菜单可见性
       */
      updateMenuVisible: async (id: number, visible: number): Promise<boolean> => {
        try {

          const response = await MenuService.updateMenuVisible(id, visible)
          
          if (response.success) {
            // 重新获取菜单树数据
            await get().fetchMenuTree()
            return true
          } else {
            return false
          }
        } catch (error) {
          return false
        }
      },

      /**
       * 展开节点
       */
      expandNode: (nodeId: number) => {
        const { expandedKeys } = get()
        if (!expandedKeys.includes(nodeId)) {
          set({ expandedKeys: [...expandedKeys, nodeId] })
        }
      },

      /**
       * 折叠节点
       */
      collapseNode: (nodeId: number) => {
        const { expandedKeys } = get()
        set({ expandedKeys: expandedKeys.filter(key => key !== nodeId) })
      },

      /**
       * 选中节点
       */
      selectNode: (nodeId: number) => {
        set({ selectedKeys: [nodeId] })
      },

      /**
       * 搜索菜单
       */
      searchMenus: (keyword: string) => {
        set({ searchKeyword: keyword })
        // 这里可以实现搜索逻辑，过滤树形数据
      },

      /**
       * 筛选菜单
       */
      filterMenus: (filters: Partial<MenuQueryRequest>) => {
        set({ filters })
        // 这里可以实现筛选逻辑
      },

      /**
       * 打开表单
       */
      openForm: (action: MenuAction, menu?: MenuItem, parentId?: number) => {
        set({
          action,
          visible: true,
          currentMenu: menu,
          parentId,
          loading: false
        })
      },

      /**
       * 关闭表单
       */
      closeForm: () => {
        set({
          visible: false,
          currentMenu: undefined,
          parentId: undefined,
          loading: false
        })
      },

      /**
       * 设置表单加载状态
       */
      setFormLoading: (loading: boolean) => {
        set({ loading })
      },

      /**
       * 根据ID查找菜单
       */
      findMenuById: (id: number): MenuItem | undefined => {
        const { treeData } = get()
        return findMenuInTree(treeData, id)
      },

      /**
       * 获取菜单路径
       */
      getMenuPath: (id: number): MenuItem[] => {
        const { treeData } = get()
        return getMenuPathInTree(treeData, id)
      },

      /**
       * 验证菜单名称
       * 重构后：直接使用返回的boolean值，无需嵌套访问
       */
      validateMenuName: async (name: string, parentId: number, excludeId?: number): Promise<boolean> => {
        try {
          const isExists = await MenuService.checkMenuName(name, parentId, excludeId)
          return !isExists // 返回true表示名称可用（不存在）
        } catch (error) {
          console.error('验证菜单名称异常:', error)
          return false // 发生错误时返回false（不可用）
        }
      },

      /**
       * 验证菜单路径
       * 重构后：直接使用返回的boolean值，无需嵌套访问
       */
      validateMenuPath: async (path: string, excludeId?: number): Promise<boolean> => {
        try {
          const isExists = await MenuService.checkPath(path, excludeId)
          return !isExists // 返回true表示路径可用（不存在）
        } catch (error) {
          console.error('验证菜单路径异常:', error)
          return false // 发生错误时返回false（不可用）
        }
      },

      /**
       * 重置状态
       */
      reset: () => {
        set(initialState)
      }
    }),
    {
      name: 'menu-store'
    }
  )
)

/**
 * 将菜单数据转换为树形节点
 * 处理后端数据结构到前端数据结构的字段映射
 */
function convertToTreeNodes(menus: any[]): MenuTreeNode[] {
  if (!menus || !Array.isArray(menus)) {
    return []
  }
  return menus.map(menu => {
    return {
      // 基础字段映射（后端 -> 前端）
      id: menu.id,
      parentId: menu.parentId || 0,
      name: menu.menuName,              // menuName -> name
      title: menu.menuName,             // menuName -> title
      type: menu.menuType,              // menuType -> type
      path: menu.path || '',
      component: menu.component || '',
      icon: menu.icon || '',
      permission: menu.permissionCode || '',  // permissionCode -> permission
      sort: menu.sortOrder || 0,        // sortOrder -> sort
      visible: menu.visible === 1,      // 数字转布尔值
      enabled: menu.status === 1,       // status -> enabled，数字转布尔值
      createTime: menu.createTime,
      updateTime: menu.updateTime,

      // 树形节点扩展字段
      level: 0,
      expanded: false,
      selected: false,
      draggable: true,
      childrenCount: menu.children?.length || 0,
      children: menu.children ? convertToTreeNodes(menu.children) : undefined
    }
  })
}

/**
 * 在树中查找菜单
 */
function findMenuInTree(treeData: MenuTreeNode[], id: number): MenuItem | undefined {
  for (const node of treeData) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const found = findMenuInTree(node.children, id)
      if (found) return found
    }
  }
  return undefined
}

/**
 * 获取菜单在树中的路径
 */
function getMenuPathInTree(treeData: MenuTreeNode[], id: number, path: MenuItem[] = []): MenuItem[] {
  for (const node of treeData) {
    const currentPath = [...path, node]
    if (node.id === id) {
      return currentPath
    }
    if (node.children) {
      const found = getMenuPathInTree(node.children, id, currentPath)
      if (found.length > 0) return found
    }
  }
  return []
}
