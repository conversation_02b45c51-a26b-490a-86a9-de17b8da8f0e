import React, { useState, useMemo } from 'react'
import { ChevronDown, ChevronRight, Folder, FolderOpen, FileText, Square, Search, X } from 'lucide-react'
import * as LucideIcons from 'lucide-react'
import { Button, Input, Popover, PopoverContent, PopoverTrigger, Badge } from '@/components/ui'
import { useMenuStore } from '../hooks'
import { MenuType, type MenuTreeNode } from '../types'
import { cn } from '@/utils'

/**
 * 上级菜单选择器组件属性
 */
interface MenuParentSelectorProps {
  /** 当前选中的父菜单ID */
  value?: number
  /** 父菜单变化回调 */
  onChange?: (parentId: number) => void
  /** 当前编辑的菜单ID（用于防止循环引用） */
  currentMenuId?: number
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 渲染图标
 */
const renderIcon = (iconName?: string, size = 16) => {
  if (!iconName) return null
  const IconComponent = LucideIcons[iconName as keyof typeof LucideIcons] as React.ComponentType<{ size?: number; className?: string }>
  if (!IconComponent) return null
  return <IconComponent size={size} className="shrink-0" />
}

/**
 * 获取菜单类型图标
 */
const getMenuTypeIcon = (menuType: MenuType) => {
  switch (menuType) {
    case MenuType.DIRECTORY:
      return <Folder className="h-4 w-4 text-blue-500" />
    case MenuType.PAGE:
      return <FileText className="h-4 w-4 text-green-500" />
    case MenuType.BUTTON:
      return <Square className="h-4 w-4 text-orange-500" />
    default:
      return <FileText className="h-4 w-4 text-gray-500" />
  }
}

/**
 * 菜单树节点组件
 */
interface MenuTreeNodeProps {
  node: MenuTreeNode
  level: number
  expandedKeys: Set<number>
  onToggleExpand: (nodeId: number) => void
  onSelect: (nodeId: number) => void
  selectedId?: number
  disabledIds: Set<number>
}

const MenuTreeNodeComponent: React.FC<MenuTreeNodeProps> = ({
  node,
  level,
  expandedKeys,
  onToggleExpand,
  onSelect,
  selectedId,
  disabledIds
}) => {
  const hasChildren = node.children && node.children.length > 0
  const isExpanded = expandedKeys.has(node.id)
  const isSelected = selectedId === node.id
  const isDisabled = disabledIds.has(node.id)

  return (
    <div>
      <div
        className={cn(
          "flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer hover:bg-accent rounded-sm",
          isSelected && "bg-accent",
          isDisabled && "opacity-50 cursor-not-allowed"
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => !isDisabled && onSelect(node.id)}
      >
        {/* 展开/收起按钮 */}
        <div className="w-4 h-4 flex items-center justify-center">
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={(e) => {
                e.stopPropagation()
                onToggleExpand(node.id)
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>

        {/* 菜单图标 */}
        <div className="w-4 h-4 flex items-center justify-center">
          {node.icon ? renderIcon(node.icon, 14) : getMenuTypeIcon(node.type)}
        </div>

        {/* 菜单名称 */}
        <span className="flex-1 truncate">
          {node.title || node.name}
        </span>

        {/* 菜单类型标识 */}
        <Badge variant="outline" className="text-xs px-1 py-0">
          {node.type === MenuType.DIRECTORY ? '目录' : 
           node.type === MenuType.PAGE ? '页面' : '按钮'}
        </Badge>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map((child) => (
            <MenuTreeNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
              expandedKeys={expandedKeys}
              onToggleExpand={onToggleExpand}
              onSelect={onSelect}
              selectedId={selectedId}
              disabledIds={disabledIds}
            />
          ))}
        </div>
      )}
    </div>
  )
}

/**
 * 上级菜单选择器组件
 */
export const MenuParentSelector: React.FC<MenuParentSelectorProps> = ({
  value,
  onChange,
  currentMenuId,
  placeholder: _placeholder = '选择上级菜单',
  disabled = false,
  className
}) => {
  const { treeData } = useMenuStore()
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<Set<number>>(new Set())

  // 获取选中菜单的显示文本
  const selectedMenuText = useMemo(() => {
    if (!value || value === 0) return '根目录'
    
    const findMenu = (nodes: MenuTreeNode[]): MenuTreeNode | null => {
      for (const node of nodes) {
        if (node.id === value) return node
        if (node.children) {
          const found = findMenu(node.children)
          if (found) return found
        }
      }
      return null
    }
    
    const selectedMenu = findMenu(treeData)
    return selectedMenu ? (selectedMenu.title || selectedMenu.name) : '未知菜单'
  }, [value, treeData])

  // 获取禁用的菜单ID集合（防止循环引用）
  const disabledIds = useMemo(() => {
    const disabled = new Set<number>()
    
    if (!currentMenuId) return disabled
    
    // 递归获取所有子菜单ID
    const getChildrenIds = (nodes: MenuTreeNode[]): void => {
      for (const node of nodes) {
        if (node.id === currentMenuId) {
          // 添加当前菜单及其所有子菜单
          const addNodeAndChildren = (n: MenuTreeNode) => {
            disabled.add(n.id)
            if (n.children) {
              n.children.forEach(addNodeAndChildren)
            }
          }
          addNodeAndChildren(node)
          return
        }
        if (node.children) {
          getChildrenIds(node.children)
        }
      }
    }
    
    getChildrenIds(treeData)
    return disabled
  }, [currentMenuId, treeData])

  // 过滤菜单树
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData

    const matchedNodeIds = new Set<number>()

    const filterNodes = (nodes: MenuTreeNode[]): MenuTreeNode[] => {
      return nodes.reduce<MenuTreeNode[]>((acc, node) => {
        const matchesSearch = (node.title || node.name).toLowerCase().includes(searchValue.toLowerCase())
        const filteredChildren = node.children ? filterNodes(node.children) : []

        if (matchesSearch || filteredChildren.length > 0) {
          // 记录匹配的节点ID，用于自动展开
          if (matchesSearch) {
            matchedNodeIds.add(node.id)
          }
          if (filteredChildren.length > 0) {
            matchedNodeIds.add(node.id) // 有匹配子节点的父节点也要展开
          }

          acc.push({
            ...node,
            children: filteredChildren
          })
        }

        return acc
      }, [])
    }

    const filtered = filterNodes(treeData)

    // 搜索时自动展开匹配的节点
    if (searchValue && matchedNodeIds.size > 0) {
      setExpandedKeys(prev => new Set([...prev, ...matchedNodeIds]))
    }

    return filtered
  }, [treeData, searchValue])

  /**
   * 切换节点展开状态
   */
  const handleToggleExpand = (nodeId: number) => {
    const newExpandedKeys = new Set(expandedKeys)
    if (newExpandedKeys.has(nodeId)) {
      newExpandedKeys.delete(nodeId)
    } else {
      newExpandedKeys.add(nodeId)
    }
    setExpandedKeys(newExpandedKeys)
  }

  /**
   * 选择菜单
   */
  const handleSelect = (nodeId: number) => {
    onChange?.(nodeId)
    setOpen(false)
    setSearchValue('')
  }

  /**
   * 选择根目录
   */
  const handleSelectRoot = () => {
    onChange?.(0)
    setOpen(false)
    setSearchValue('')
  }

  /**
   * 清除选择
   */
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange?.(0)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between h-10 px-3 py-2",
            !value && "text-muted-foreground",
            className
          )}
        >
          <div className="flex items-center gap-2">
            <Folder className="h-4 w-4 text-blue-500" />
            <span className="truncate">{selectedMenuText}</span>
          </div>
          <div className="flex items-center gap-1">
            {value && value !== 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={handleClear}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="flex flex-col h-96">
          {/* 搜索框 */}
          <div className="p-3 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索菜单..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* 菜单树 */}
          <div className="flex-1 overflow-y-auto p-2">
            {/* 根目录选项 */}
            <div
              className={cn(
                "flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer hover:bg-accent rounded-sm mb-1",
                (value === 0 || !value) && "bg-accent"
              )}
              onClick={handleSelectRoot}
            >
              <div className="w-4 h-4" />
              <FolderOpen className="h-4 w-4 text-blue-500" />
              <span className="flex-1">根目录</span>
              <Badge variant="outline" className="text-xs px-1 py-0">
                根
              </Badge>
            </div>

            {/* 菜单树节点 */}
            {filteredTreeData.map((node) => (
              <MenuTreeNodeComponent
                key={node.id}
                node={node}
                level={0}
                expandedKeys={expandedKeys}
                onToggleExpand={handleToggleExpand}
                onSelect={handleSelect}
                selectedId={value}
                disabledIds={disabledIds}
              />
            ))}

            {filteredTreeData.length === 0 && searchValue && (
              <div className="text-center text-muted-foreground py-8">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">未找到匹配的菜单</p>
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
