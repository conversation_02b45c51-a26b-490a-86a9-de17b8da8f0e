import React from 'react'
import { AlertTriangle } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../../components/ui'
import type { MenuTreeNode } from '../types'

/**
 * 删除确认对话框组件属性
 */
interface DeleteConfirmDialogProps {
  /** 是否显示对话框 */
  open: boolean
  /** 要删除的菜单节点 */
  menu: MenuTreeNode | null
  /** 是否正在删除 */
  loading?: boolean
  /** 确认删除回调 */
  onConfirm: () => void
  /** 取消删除回调 */
  onCancel: () => void
}

/**
 * 删除确认对话框组件
 */
export const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  open,
  menu,
  loading = false,
  onConfirm,
  onCancel
}) => {
  if (!menu) return null

  const hasChildren = menu.children && menu.children.length > 0

  return (
    <AlertDialog open={open} onOpenChange={(open) => !open && onCancel()}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            确认删除菜单
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <div>
              您即将删除菜单 <span className="font-semibold text-foreground">"{menu.name}"</span>
            </div>
            {hasChildren && (
              <div className="text-destructive">
                ⚠️ 该菜单包含 <span className="font-semibold">{menu.children?.length}</span> 个子菜单，删除后所有子菜单也将被删除。
              </div>
            )}
            <div className="text-muted-foreground">
              此操作不可恢复，请谨慎操作。
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={onCancel}
            disabled={loading}
          >
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={loading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {loading ? '删除中...' : '确认删除'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
