import React, { useState, useMemo } from 'react'
import { Search, X } from 'lucide-react'
import * as LucideIcons from 'lucide-react'
import { Button, Input, Popover, PopoverContent, PopoverTrigger, Badge } from '../../../../components/ui'
import { cn } from '../../../../utils'

/**
 * 图标选择器组件属性
 */
interface IconSelectorProps {
  /** 当前选中的图标名称 */
  value?: string
  /** 图标变化回调 */
  onChange?: (iconName: string) => void
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 常用图标分类
 */
const ICON_CATEGORIES = {
  '系统管理': [
    'Settings', 'Users', 'Shield', 'Key', 'Lock', 'Unlock', 'UserCheck', 'UserX',
    'Database', 'Server', 'Monitor', 'Cpu', 'HardDrive', 'Network'
  ],
  '导航菜单': [
    'Home', 'LayoutDashboard', 'Menu', 'Navigation', 'Compass', 'Map',
    'Bookmark', 'Star', 'Heart', 'Flag', 'Target', 'Zap'
  ],
  '文件操作': [
    'File', 'FileText', 'Folder', 'FolderOpen', 'Upload', 'Download',
    'Save', 'Copy', 'Cut', 'Paste', 'Trash2', 'Archive'
  ],
  '编辑操作': [
    'Edit', 'Edit2', 'Edit3', 'Plus', 'Minus', 'X', 'Check', 'CheckCircle',
    'XCircle', 'AlertCircle', 'Info', 'HelpCircle'
  ],
  '箭头方向': [
    'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ChevronUp', 'ChevronDown',
    'ChevronLeft', 'ChevronRight', 'CornerDownLeft', 'CornerDownRight', 'Move', 'RotateCcw'
  ],
  '商业办公': [
    'Briefcase', 'Building', 'Calendar', 'Clock', 'Mail', 'Phone',
    'MessageSquare', 'Bell', 'Search', 'Filter', 'BarChart3', 'PieChart'
  ]
}

/**
 * 获取所有可用图标
 */
const getAllIcons = () => {
  const allIconNames = Object.keys(LucideIcons).filter(
    name => name !== 'createLucideIcon' && name !== 'Icon' && typeof LucideIcons[name as keyof typeof LucideIcons] === 'function'
  )
  return allIconNames
}

/**
 * 渲染图标组件
 */
const renderIcon = (iconName: string, size = 16) => {
  const IconComponent = LucideIcons[iconName as keyof typeof LucideIcons] as React.ComponentType<{ size?: number; className?: string }>
  if (!IconComponent) return null
  return <IconComponent size={size} className="shrink-0" />
}

/**
 * 图标选择器组件
 */
export const IconSelector: React.FC<IconSelectorProps> = ({
  value,
  onChange,
  placeholder = '选择图标',
  disabled = false,
  className
}) => {
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('系统管理')

  // 获取所有图标
  const allIcons = useMemo(() => getAllIcons(), [])

  // 过滤图标
  const filteredIcons = useMemo(() => {
    if (!searchValue) {
      return ICON_CATEGORIES[selectedCategory as keyof typeof ICON_CATEGORIES] || []
    }
    
    return allIcons.filter(iconName =>
      iconName.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [searchValue, selectedCategory, allIcons])

  /**
   * 选择图标
   */
  const handleSelectIcon = (iconName: string) => {
    onChange?.(iconName)
    setOpen(false)
    setSearchValue('')
  }

  /**
   * 清除选择
   */
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange?.('')
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between h-10 px-3 py-2",
            !value && "text-muted-foreground",
            className
          )}
        >
          <div className="flex items-center gap-2">
            {value ? (
              <>
                {renderIcon(value, 16)}
                <span className="truncate">{value}</span>
              </>
            ) : (
              <span>{placeholder}</span>
            )}
          </div>
          <div className="flex items-center gap-1">
            {value && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={handleClear}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <Search className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="flex flex-col h-96">
          {/* 搜索框 */}
          <div className="p-3 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索图标..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          <div className="flex flex-1 min-h-0">
            {/* 分类侧边栏 */}
            {!searchValue && (
              <div className="w-24 border-r bg-muted/30">
                <div className="p-2 space-y-1">
                  {Object.keys(ICON_CATEGORIES).map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "secondary" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-xs h-8 px-2"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* 图标网格 */}
            <div className="flex-1 p-3">
              {searchValue && (
                <div className="mb-3">
                  <Badge variant="secondary" className="text-xs">
                    找到 {filteredIcons.length} 个图标
                  </Badge>
                </div>
              )}
              
              <div className="grid grid-cols-6 gap-2 max-h-64 overflow-y-auto">
                {filteredIcons.map((iconName) => (
                  <Button
                    key={iconName}
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-10 w-10 p-0 hover:bg-accent",
                      value === iconName && "bg-accent"
                    )}
                    onClick={() => handleSelectIcon(iconName)}
                    title={iconName}
                  >
                    {renderIcon(iconName, 18)}
                  </Button>
                ))}
              </div>

              {filteredIcons.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">未找到匹配的图标</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
