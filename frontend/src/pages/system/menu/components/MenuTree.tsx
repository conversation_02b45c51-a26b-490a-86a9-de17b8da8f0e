import React, { useMemo } from 'react'
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FileText,
  Square,
  Eye,
  EyeOff,
  MoreHorizontal,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'
import { Button } from '../../../../components/ui'
import { usePermission } from '../../../../stores'
import { useMenuStore } from '../hooks'
import { PERMISSIONS } from '../../../../constants'
import { IconUtils } from '../../../../utils'
import {MenuAction, type MenuTreeNode, type MenuType} from '../types'

/**
 * 菜单树组件属性
 */
interface MenuTreeProps {
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否可拖拽 */
  draggable?: boolean
  /** 节点点击回调 */
  onNodeClick?: (node: MenuTreeNode) => void
  /** 节点操作回调 */
  onNodeAction?: (action: MenuAction, node: MenuTreeNode) => void
}

/**
 * 菜单树组件
 */
export const MenuTree: React.FC<MenuTreeProps> = ({
  showActions = true,
  onNodeClick,
  onNodeAction
}) => {
  const { hasPermission } = usePermission()
  const {
    treeData,
    expandedKeys,
    selectedKeys,
    loading,
    searchKeyword,
    filters,
    expandNode,
    collapseNode,
    selectNode
  } = useMenuStore()

  /**
   * 根据筛选条件过滤节点
   */
  const filterNodeByConditions = (node: MenuTreeNode): boolean => {
    // 菜单类型筛选
    if (filters.menuType !== undefined && node.type !== filters.menuType) {
      return false
    }

    // 状态筛选
    if (filters.status !== undefined && node.enabled !== (filters.status === 1)) {
      return false
    }

    // 可见性筛选
    if (filters.visible !== undefined && node.visible !== (filters.visible === 1)) {
      return false
    }

    return true
  }

  /**
   * 递归过滤树形数据（支持高级筛选）
   */
  const filterTreeByConditions = (nodes: MenuTreeNode[]): MenuTreeNode[] => {
    return nodes.reduce<MenuTreeNode[]>((acc, node) => {
      // 递归处理子节点
      const filteredChildren = node.children ? filterTreeByConditions(node.children) : undefined

      // 检查当前节点是否符合筛选条件
      const nodeMatches = filterNodeByConditions(node)

      // 如果当前节点符合条件，或者有符合条件的子节点，则保留
      if (nodeMatches || (filteredChildren && filteredChildren.length > 0)) {
        acc.push({
          ...node,
          children: filteredChildren
        })
      }

      return acc
    }, [])
  }

  /**
   * 过滤后的树数据
   */
  const filteredTreeData = useMemo(() => {

    let result = treeData

    // 先应用高级筛选条件
    const hasFilters = filters.menuType !== undefined ||
                      filters.status !== undefined ||
                      filters.visible !== undefined

    if (hasFilters) {
      result = filterTreeByConditions(result)
    }

    // 再应用搜索关键词筛选
    if (searchKeyword) {
      result = filterTreeByKeyword(result, searchKeyword)
    }

    return result
  }, [treeData, searchKeyword, filters])

  /**
   * 处理节点展开/折叠
   */
  const handleToggleExpand = (node: MenuTreeNode) => {
    if (expandedKeys.includes(node.id)) {
      collapseNode(node.id)
    } else {
      expandNode(node.id)
    }
  }

  /**
   * 处理节点选择
   */
  const handleNodeSelect = (node: MenuTreeNode) => {
    selectNode(node.id)
    onNodeClick?.(node)
  }

  /**
   * 处理节点操作
   */
  const handleNodeAction = (action: MenuAction, node: MenuTreeNode) => {
    onNodeAction?.(action, node)
  }

  /**
   * 获取菜单类型图标
   */
  const getMenuTypeIcon = (menuType: MenuType) => {
    switch (menuType) {
      case 0: // 目录
        return <Folder className="w-4 h-4 text-blue-500" />
      case 1: // 页面
        return <FileText className="w-4 h-4 text-green-500" />
      case 2: // 按钮
        return <Square className="w-4 h-4 text-orange-500" />
      default:
        return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  /**
   * 获取菜单状态标签
   */
  const getStatusBadge = (enabled: boolean, visible: boolean) => {
    const badges = []

    if (!enabled) {
      badges.push(
        <span key="status" className="px-1.5 py-0.5 text-xs bg-red-100 text-red-600 rounded">
          禁用
        </span>
      )
    }

    if (!visible) {
      badges.push(
        <span key="visible" className="px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
          隐藏
        </span>
      )
    }

    return badges
  }

  /**
   * 渲染树节点
   */
  const renderTreeNode = (node: MenuTreeNode, level: number = 0) => {
    const isExpanded = expandedKeys.includes(node.id)
    const isSelected = selectedKeys.includes(node.id)
    const hasChildren = node.children && node.children.length > 0
    const indent = level * 24

    return (
      <div key={node.id} className="select-none">
        {/* 节点内容 */}
        <div
          className={`
            group flex items-center py-2 px-3 hover:bg-gray-50 cursor-pointer
            ${isSelected ? 'bg-blue-50 border-r-2 border-blue-500' : ''}
          `}
          style={{ paddingLeft: `${12 + indent}px` }}
          onClick={() => handleNodeSelect(node)}
        >
          {/* 展开/折叠按钮 */}
          <div className="w-4 h-4 mr-1 flex items-center justify-center">
            {hasChildren ? (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  handleToggleExpand(node)
                }}
                className="hover:bg-gray-200 rounded p-0.5"
              >
                {isExpanded ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </button>
            ) : null}
          </div>

          {/* 菜单图标 */}
          <div className="mr-2">
            {node.icon ? (
              <span className="w-4 h-4 flex items-center justify-center">
                {IconUtils.renderIcon(node.icon, 16, "text-gray-600") || getMenuTypeIcon(node.type)}
              </span>
            ) : (
              getMenuTypeIcon(node.type)
            )}
          </div>

          {/* 菜单名称 */}
          <span className="flex-1 text-sm font-medium text-gray-900">
            {node.title}
          </span>

          {/* 状态标签 */}
          <div className="flex items-center space-x-1 mr-2">
            {getStatusBadge(node.enabled, node.visible)}
          </div>

          {/* 可见性图标 */}
          <div className="mr-2">
            {node.visible ? (
              <Eye className="w-4 h-4 text-gray-400" />
            ) : (
              <EyeOff className="w-4 h-4 text-gray-400" />
            )}
          </div>

          {/* 操作按钮 */}
          {showActions && (
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {/* 新增子菜单 */}
              {hasPermission(PERMISSIONS.AUTH.MENU.ADD) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleNodeAction(MenuAction.ADD, node)
                  }}
                  className="h-6 w-6 p-0"
                >
                  <Plus className="w-3 h-3" />
                </Button>
              )}

              {/* 编辑 */}
              {hasPermission(PERMISSIONS.AUTH.MENU.EDIT) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleNodeAction(MenuAction.EDIT, node)
                  }}
                  className="h-6 w-6 p-0"
                >
                  <Edit className="w-3 h-3" />
                </Button>
              )}

              {/* 删除 */}
              {hasPermission(PERMISSIONS.AUTH.MENU.DELETE) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleNodeAction(MenuAction.DELETE, node)
                  }}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              )}

              {/* 更多操作 */}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <MoreHorizontal className="w-3 h-3" />
              </Button>
            </div>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-sm text-gray-500">加载中...</div>
      </div>
    )
  }

  if (!filteredTreeData || filteredTreeData.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center space-y-2">
          <Folder className="w-12 h-12 mx-auto text-gray-300" />
          <p className="text-sm text-gray-500">
            {searchKeyword ? '没有找到匹配的菜单' : '暂无菜单数据'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="menu-tree">
      {filteredTreeData.map(node => renderTreeNode(node))}
    </div>
  )
}

/**
 * 根据关键词过滤树数据
 */
function filterTreeByKeyword(treeData: MenuTreeNode[], keyword: string): MenuTreeNode[] {
  const filtered: MenuTreeNode[] = []

  for (const node of treeData) {
    const matchesKeyword = node.title.toLowerCase().includes(keyword.toLowerCase()) ||
                          node.name.toLowerCase().includes(keyword.toLowerCase()) ||
                          (node.path && node.path.toLowerCase().includes(keyword.toLowerCase())) ||
                          (node.permission && node.permission.toLowerCase().includes(keyword.toLowerCase()))

    let filteredChildren: MenuTreeNode[] = []
    if (node.children) {
      filteredChildren = filterTreeByKeyword(node.children, keyword)
    }

    if (matchesKeyword || filteredChildren.length > 0) {
      filtered.push({
        ...node,
        children: filteredChildren.length > 0 ? filteredChildren : node.children
      })
    }
  }

  return filtered
}
