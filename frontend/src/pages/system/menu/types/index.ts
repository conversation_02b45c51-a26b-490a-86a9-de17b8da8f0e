/**
 * 菜单管理模块类型定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 菜单类型枚举
 */
export enum MenuType {
  /** 目录 */
  DIRECTORY = 0,
  /** 页面 */
  PAGE = 1,
  /** 按钮 */
  BUTTON = 2
}

/**
 * 菜单状态枚举
 */
export enum MenuStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1
}

/**
 * 菜单可见性枚举
 */
export enum MenuVisible {
  /** 隐藏 */
  HIDDEN = 0,
  /** 显示 */
  VISIBLE = 1
}

/**
 * 菜单项接口（匹配后端数据结构）
 */
export interface MenuItem {
  /** 菜单ID */
  id: number
  /** 父菜单ID */
  parentId: number
  /** 菜单名称（后端字段） */
  name: string
  /** 菜单标题（后端字段） */
  title: string
  /** 菜单类型（后端字段：type） */
  type: MenuType
  /** 路由路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单图标 */
  icon?: string
  /** 权限标识（后端字段：permission） */
  permission?: string
  /** 排序（后端字段：sort） */
  sort: number
  /** 是否可见（后端字段：visible） */
  visible: boolean
  /** 是否启用（后端字段：enabled） */
  enabled: boolean
  /** 子菜单 */
  children?: MenuItem[]
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
}

/**
 * 菜单项接口（前端表单使用，匹配后端API）
 */
export interface MenuFormData {
  /** 菜单ID */
  id?: number
  /** 父菜单ID */
  parentId: number
  /** 菜单名称 */
  menuName: string
  /** 菜单类型 */
  menuType: MenuType
  /** 路由路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单图标 */
  icon?: string
  /** 权限标识 */
  permissionCode?: string
  /** 状态 */
  status: MenuStatus
  /** 是否显示 */
  visible: MenuVisible
  /** 排序 */
  sortOrder: number
  /** 备注 */
  remark?: string
}

/**
 * 菜单查询请求
 */
export interface MenuQueryRequest {
  /** 菜单名称 */
  menuName?: string
  /** 状态 */
  status?: MenuStatus
  /** 是否显示 */
  visible?: MenuVisible
  /** 菜单类型 */
  menuType?: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 当前页 */
  current?: number
  /** 页面大小 */
  size?: number
}

/**
 * 菜单创建请求
 */
export interface MenuCreateRequest {
  /** 父菜单ID */
  parentId: number
  /** 菜单名称 */
  menuName: string
  /** 菜单类型 */
  menuType: MenuType
  /** 路由路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单图标 */
  icon?: string
  /** 权限标识 */
  permissionCode?: string
  /** 状态 */
  status: MenuStatus
  /** 是否显示 */
  visible: MenuVisible
  /** 排序 */
  sortOrder: number
  /** 备注 */
  remark?: string
}

/**
 * 菜单更新请求
 */
export interface MenuUpdateRequest extends MenuCreateRequest {
  /** 菜单ID */
  id: number
}

/**
 * 菜单树节点
 */
export interface MenuTreeNode extends MenuItem {
  /** 节点层级 */
  level?: number
  /** 是否展开 */
  expanded?: boolean
  /** 是否选中 */
  selected?: boolean
  /** 是否可拖拽 */
  draggable?: boolean
  /** 子节点数量 */
  childrenCount?: number
}

/**
 * 菜单操作类型
 */
export enum MenuAction {
  /** 查看 */
  VIEW = 'view',
  /** 新增 */
  ADD = 'add',
  /** 编辑 */
  EDIT = 'edit',
  /** 删除 */
  DELETE = 'delete',
  /** 移动 */
  MOVE = 'move',
  /** 启用/禁用 */
  TOGGLE_STATUS = 'toggle_status',
  /** 显示/隐藏 */
  TOGGLE_VISIBLE = 'toggle_visible'
}

/**
 * 菜单表单状态
 */
export interface MenuFormState {
  /** 操作类型 */
  action: MenuAction
  /** 是否显示表单 */
  visible: boolean
  /** 当前编辑的菜单 */
  currentMenu?: MenuItem
  /** 父菜单ID */
  parentId?: number
  /** 加载状态 */
  loading: boolean
}

/**
 * 菜单树状态
 */
export interface MenuTreeState {
  /** 菜单树数据 */
  treeData: MenuTreeNode[]
  /** 展开的节点 */
  expandedKeys: number[]
  /** 选中的节点 */
  selectedKeys: number[]
  /** 加载状态 */
  loading: boolean
  /** 搜索关键词 */
  searchKeyword: string
  /** 筛选条件 */
  filters: Partial<MenuQueryRequest>
}

/**
 * 图标选择器选项
 */
export interface IconOption {
  /** 图标名称 */
  name: string
  /** 图标组件 */
  component: React.ComponentType<any>
  /** 图标分类 */
  category: string
  /** 搜索关键词 */
  keywords: string[]
}

/**
 * 菜单类型选项
 */
export interface MenuTypeOption {
  /** 类型值 */
  value: MenuType
  /** 显示标签 */
  label: string
  /** 图标 */
  icon: string
  /** 描述 */
  description: string
  /** 是否可选 */
  disabled?: boolean
}
