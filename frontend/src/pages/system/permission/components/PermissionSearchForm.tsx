/**
 * 权限搜索表单组件
 * 
 * 统一重构版本 - 完整的权限搜索功能
 */

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label
} from '@/components/ui'
import { Search, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react'
import { PERMISSION_TYPE_LABELS, PERMISSION_STATUS_LABELS } from '@/types/permission'
import type { PermissionQueryRequest } from '@/types/permission'

/**
 * 权限搜索表单属性
 */
export interface PermissionSearchFormProps {
  /** 搜索参数 */
  searchParams: PermissionQueryRequest
  /** 搜索参数变化回调 */
  onSearchParamsChange: (params: PermissionQueryRequest) => void
  /** 搜索回调 */
  onSearch: () => void
  /** 重置回调 */
  onReset: () => void
  /** 加载状态 */
  loading?: boolean
}

/**
 * 权限搜索表单组件
 */
const PermissionSearchForm: React.FC<PermissionSearchFormProps> = ({
  searchParams,
  onSearchParamsChange,
  onSearch,
  onReset,
  loading = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false)

  /**
   * 处理基础搜索字段变化
   */
  const handleBasicFieldChange = (field: keyof PermissionQueryRequest, value: any) => {
    onSearchParamsChange({
      ...searchParams,
      [field]: value
    })
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    onSearchParamsChange({
      pageNum: 1,
      pageSize: 20
    })
    onReset()
  }

  /**
   * 处理回车搜索
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSearch()
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 基础搜索 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="permissionCode">权限编码</Label>
              <Input
                id="permissionCode"
                placeholder="请输入权限编码"
                value={searchParams.permissionCode || ''}
                onChange={(e) => handleBasicFieldChange('permissionCode', e.target.value)}
                onKeyUp={handleKeyPress}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="permissionName">权限名称</Label>
              <Input
                id="permissionName"
                placeholder="请输入权限名称"
                value={searchParams.permissionName || ''}
                onChange={(e) => handleBasicFieldChange('permissionName', e.target.value)}
                onKeyUp={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="permissionType">权限类型</Label>
              <Select
                value={searchParams.permissionType || 'all'}
                onValueChange={(value) => handleBasicFieldChange('permissionType', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择权限类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {Object.entries(PERMISSION_TYPE_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select
                value={searchParams.status?.toString() || 'all'}
                onValueChange={(value) => handleBasicFieldChange('status', value === 'all' ? undefined : Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  {Object.entries(PERMISSION_STATUS_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 高级搜索 */}
          {showAdvanced && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Label htmlFor="createTimeStart">创建时间（开始）</Label>
                <Input
                  id="createTimeStart"
                  type="datetime-local"
                  value={searchParams.createTimeStart || ''}
                  onChange={(e) => handleBasicFieldChange('createTimeStart', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="createTimeEnd">创建时间（结束）</Label>
                <Input
                  id="createTimeEnd"
                  type="datetime-local"
                  value={searchParams.createTimeEnd || ''}
                  onChange={(e) => handleBasicFieldChange('createTimeEnd', e.target.value)}
                />
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-muted-foreground"
            >
              {showAdvanced ? (
                <>
                  <ChevronUp className="w-4 h-4 mr-1" />
                  收起高级搜索
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4 mr-1" />
                  展开高级搜索
                </>
              )}
            </Button>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={loading}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                重置
              </Button>
              <Button
                onClick={onSearch}
                disabled={loading}
              >
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default PermissionSearchForm
