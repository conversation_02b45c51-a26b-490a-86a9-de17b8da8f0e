/**
 * 权限表单组件
 * 
 * 统一重构版本 - 完整的权限新增和编辑功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Switch
} from '@/components/ui'
import { Loader2 } from 'lucide-react'
import type { Permission, PermissionCreateRequest, PermissionUpdateRequest, PermissionType, PermissionStatus } from '@/types/permission'
import { PERMISSION_TYPE_LABELS, HTTP_METHODS, PermissionStatus as Status } from '@/types/permission'
import PermissionParentSelector from './PermissionParentSelector'

/**
 * 权限表单属性
 */
export interface PermissionFormProps {
  /** 是否显示 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 提交回调 */
  onSubmit: (data: PermissionCreateRequest | PermissionUpdateRequest) => Promise<void>
  /** 编辑的权限数据 */
  permission?: Permission
  /** 父权限ID */
  parentId?: number
  /** 提交状态 */
  loading?: boolean
}

/**
 * 表单数据接口
 */
interface FormData {
  permissionCode: string
  permissionName: string
  permissionType: PermissionType
  resourcePath: string
  method: string
  parentId?: number
  sortOrder: number
  status: PermissionStatus
  remark: string
}

/**
 * 权限表单组件
 */
const PermissionForm: React.FC<PermissionFormProps> = ({
  open,
  onClose,
  onSubmit,
  permission,
  parentId,
  loading = false
}) => {
  const isEdit = !!permission
  const [formData, setFormData] = useState<FormData>({
    permissionCode: '',
    permissionName: '',
    permissionType: 'API' as PermissionType,
    resourcePath: '',
    method: 'GET',
    parentId: parentId,
    sortOrder: 0,
    status: Status.ENABLED,
    remark: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (open) {
      if (isEdit && permission) {
        setFormData({
          permissionCode: permission.permissionCode,
          permissionName: permission.permissionName,
          permissionType: permission.permissionType,
          resourcePath: permission.resourcePath || '',
          method: permission.method || 'GET',
          parentId: permission.parentId,
          sortOrder: permission.sortOrder,
          status: permission.status,
          remark: permission.remark || ''
        })
      } else {
        setFormData({
          permissionCode: '',
          permissionName: '',
          permissionType: 'API' as PermissionType,
          resourcePath: '',
          method: 'GET',
          parentId: parentId,
          sortOrder: 0,
          status: Status.ENABLED,
          remark: ''
        })
      }
      setErrors({})
    }
  }, [open, isEdit, permission, parentId])

  /**
   * 处理字段变化
   */
  const handleFieldChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.permissionCode.trim()) {
      newErrors.permissionCode = '权限编码不能为空'
    } else if (!/^[a-zA-Z][a-zA-Z0-9:_-]*$/.test(formData.permissionCode)) {
      newErrors.permissionCode = '权限编码格式不正确，只能包含字母、数字、冒号、下划线和连字符，且必须以字母开头'
    }

    if (!formData.permissionName.trim()) {
      newErrors.permissionName = '权限名称不能为空'
    }

    if (formData.permissionType === 'API' && !formData.resourcePath.trim()) {
      newErrors.resourcePath = 'API权限必须指定资源路径'
    }

    if (formData.sortOrder < 0) {
      newErrors.sortOrder = '排序值不能为负数'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  /**
   * 处理提交
   */
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      const submitData = {
        ...formData,
        parentId: formData.parentId || undefined
      }

      if (isEdit && permission) {
        await onSubmit({
          id: permission.id,
          ...submitData
        } as PermissionUpdateRequest)
      } else {
        await onSubmit(submitData as PermissionCreateRequest)
      }
    } catch (error) {
      console.error('提交权限表单失败:', error)
    }
  }

  /**
   * 处理权限类型变化
   */
  const handleTypeChange = (type: PermissionType) => {
    handleFieldChange('permissionType', type)
    
    // 根据权限类型设置默认值
    if (type === 'MENU') {
      handleFieldChange('method', '')
      handleFieldChange('resourcePath', '')
    } else if (type === 'API') {
      if (!formData.method) {
        handleFieldChange('method', 'GET')
      }
    } else if (type === 'BUTTON') {
      handleFieldChange('method', '')
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? '编辑权限' : '新增权限'}
          </DialogTitle>
          <DialogDescription>
            {isEdit ? '修改权限信息' : '创建新的权限'}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* 基础信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="permissionCode">权限编码 *</Label>
              <Input
                id="permissionCode"
                placeholder="如：system:user:list"
                value={formData.permissionCode}
                onChange={(e) => handleFieldChange('permissionCode', e.target.value)}
                className={errors.permissionCode ? 'border-red-500' : ''}
              />
              {errors.permissionCode && (
                <p className="text-sm text-red-500">{errors.permissionCode}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="permissionName">权限名称 *</Label>
              <Input
                id="permissionName"
                placeholder="如：用户列表"
                value={formData.permissionName}
                onChange={(e) => handleFieldChange('permissionName', e.target.value)}
                className={errors.permissionName ? 'border-red-500' : ''}
              />
              {errors.permissionName && (
                <p className="text-sm text-red-500">{errors.permissionName}</p>
              )}
            </div>
          </div>

          {/* 权限类型和父权限 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="permissionType">权限类型 *</Label>
              <Select
                value={formData.permissionType}
                onValueChange={handleTypeChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(PERMISSION_TYPE_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentId">父权限</Label>
              <PermissionParentSelector
                value={formData.parentId}
                onChange={(value) => handleFieldChange('parentId', value)}
                excludeId={permission?.id}
              />
            </div>
          </div>

          {/* 资源路径和HTTP方法 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="resourcePath">
                资源路径 {formData.permissionType === 'API' && '*'}
              </Label>
              <Input
                id="resourcePath"
                placeholder="如：/api/system/user"
                value={formData.resourcePath}
                onChange={(e) => handleFieldChange('resourcePath', e.target.value)}
                className={errors.resourcePath ? 'border-red-500' : ''}
                disabled={formData.permissionType === 'MENU'}
              />
              {errors.resourcePath && (
                <p className="text-sm text-red-500">{errors.resourcePath}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="method">HTTP方法</Label>
              <Select
                value={formData.method || 'none'}
                onValueChange={(value) => handleFieldChange('method', value === 'none' ? '' : value)}
                disabled={formData.permissionType !== 'API'}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择HTTP方法" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无</SelectItem>
                  {HTTP_METHODS.map(method => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 排序和状态 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sortOrder">排序值</Label>
              <Input
                id="sortOrder"
                type="number"
                min="0"
                value={formData.sortOrder}
                onChange={(e) => handleFieldChange('sortOrder', Number(e.target.value))}
                className={errors.sortOrder ? 'border-red-500' : ''}
              />
              {errors.sortOrder && (
                <p className="text-sm text-red-500">{errors.sortOrder}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <div className="flex items-center space-x-2 pt-2">
                <Switch
                  id="status"
                  checked={formData.status === Status.ENABLED}
                  onCheckedChange={(checked) => 
                    handleFieldChange('status', checked ? Status.ENABLED : Status.DISABLED)
                  }
                />
                <Label htmlFor="status" className="text-sm">
                  {formData.status === Status.ENABLED ? '启用' : '禁用'}
                </Label>
              </div>
            </div>
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="remark">备注</Label>
            <Textarea
              id="remark"
              placeholder="权限描述信息"
              value={formData.remark}
              onChange={(e) => handleFieldChange('remark', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {isEdit ? '更新' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default PermissionForm
