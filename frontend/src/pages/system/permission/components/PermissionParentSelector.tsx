/**
 * 权限父级选择器组件
 * 
 * 统一重构版本 - 权限父级选择功能
 */

import React, { useState, useEffect } from 'react'
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui'
import { ChevronDown, ChevronRight, Shield } from 'lucide-react'
import { PermissionService } from '@/services/permission'
import type { Permission } from '@/types/permission'

/**
 * 权限父级选择器属性
 */
export interface PermissionParentSelectorProps {
  /** 当前值 */
  value?: number
  /** 值变化回调 */
  onChange: (value?: number) => void
  /** 排除的权限ID（编辑时排除自己） */
  excludeId?: number
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 权限树节点组件属性
 */
interface PermissionTreeNodeProps {
  permission: Permission
  level: number
  selectedId?: number
  excludeId?: number
  onSelect: (permission: Permission) => void
}

/**
 * 权限树节点组件
 */
const PermissionTreeNode: React.FC<PermissionTreeNodeProps> = ({
  permission,
  level,
  selectedId,
  excludeId,
  onSelect
}) => {
  const [expanded, setExpanded] = useState(false)
  const hasChildren = permission.children && permission.children.length > 0
  const isExcluded = excludeId === permission.id
  const isSelected = selectedId === permission.id

  // 如果是被排除的节点，不显示
  if (isExcluded) {
    return null
  }

  return (
    <div>
      <div
        className={`flex items-center py-2 px-2 hover:bg-muted/50 cursor-pointer rounded-sm ${
          isSelected ? 'bg-primary/10 text-primary' : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(permission)}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="w-4 h-4 p-0 mr-2"
            onClick={(e) => {
              e.stopPropagation()
              setExpanded(!expanded)
            }}
          >
            {expanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </Button>
        ) : (
          <div className="w-4 h-4 mr-2" />
        )}
        <Shield className="w-4 h-4 mr-2 text-muted-foreground" />
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">{permission.permissionName}</div>
          <div className="text-xs text-muted-foreground truncate">{permission.permissionCode}</div>
        </div>
      </div>
      
      {expanded && hasChildren && (
        <div>
          {permission.children!.map(child => (
            <PermissionTreeNode
              key={child.id}
              permission={child}
              level={level + 1}
              selectedId={selectedId}
              excludeId={excludeId}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  )
}

/**
 * 权限父级选择器组件
 */
const PermissionParentSelector: React.FC<PermissionParentSelectorProps> = ({
  value,
  onChange,
  excludeId,
  disabled = false
}) => {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)

  /**
   * 加载权限树数据
   */
  const loadPermissions = async () => {
    try {
      setLoading(true)
      // getPermissionTree 已经在服务层处理了树形结构构建
      const response = await PermissionService.getPermissionTree()
      setPermissions(response)
    } catch (error) {
      console.error('加载权限树失败:', error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 根据ID查找权限
   */
  const findPermissionById = (perms: Permission[], id: number): Permission | null => {
    for (const perm of perms) {
      if (perm.id === id) {
        return perm
      }
      if (perm.children) {
        const found = findPermissionById(perm.children, id)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  /**
   * 处理权限选择
   */
  const handleSelect = (permission: Permission) => {
    setSelectedPermission(permission)
    onChange(permission.id)
    setOpen(false)
  }

  /**
   * 处理清除选择
   */
  const handleClear = () => {
    setSelectedPermission(null)
    onChange(undefined)
    setOpen(false)
  }

  /**
   * 初始化数据
   */
  useEffect(() => {
    if (open && permissions.length === 0) {
      loadPermissions()
    }
  }, [open])

  /**
   * 根据value查找对应的权限
   */
  useEffect(() => {
    if (value && permissions.length > 0) {
      const permission = findPermissionById(permissions, value)
      setSelectedPermission(permission)
    } else {
      setSelectedPermission(null)
    }
  }, [value, permissions])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedPermission ? (
            <div className="flex items-center min-w-0 flex-1">
              <Shield className="w-4 h-4 mr-2 text-muted-foreground" />
              <div className="min-w-0 flex-1">
                <div className="text-sm truncate">{selectedPermission.permissionName}</div>
                <div className="text-xs text-muted-foreground truncate">
                  {selectedPermission.permissionCode}
                </div>
              </div>
            </div>
          ) : (
            <span className="text-muted-foreground">请选择父权限</span>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-2 border-b">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-muted-foreground"
            onClick={handleClear}
          >
            无父权限（顶级权限）
          </Button>
        </div>
        <div className="h-64 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : permissions.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="text-center">
                <Shield className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无权限数据</p>
              </div>
            </div>
          ) : (
            <div className="p-2">
              {permissions.map(permission => (
                <PermissionTreeNode
                  key={permission.id}
                  permission={permission}
                  level={0}
                  selectedId={value}
                  excludeId={excludeId}
                  onSelect={handleSelect}
                />
              ))}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

export default PermissionParentSelector
