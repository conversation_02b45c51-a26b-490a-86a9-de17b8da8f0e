/**
 * 权限表格组件
 * 
 * 统一重构版本 - 完整的权限列表展示和操作功能
 */

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Checkbox
} from '@/components/ui'
import {
  Edit,
  Trash2,
  Plus,
  Shield,
  UserCheck,
  UserX
} from 'lucide-react'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper'
import { DataPagination } from '@/components/common/DataPagination'
import type { Permission, PageResult } from '@/types/permission'
import {
  getPermissionStatusLabel,
  getPermissionStatusColor,
  getPermissionTypeLabel,
  getPermissionTypeColor,
  PermissionStatus
} from '@/types/permission'

/**
 * 权限表格属性
 */
export interface PermissionTableProps {
  /** 权限数据 */
  data: PageResult<Permission>
  /** 加载状态 */
  loading?: boolean
  /** 选中的权限ID列表 */
  selectedIds?: number[]
  /** 选中状态变化回调 */
  onSelectionChange?: (selectedIds: number[]) => void
  /** 编辑回调 */
  onEdit?: (permission: Permission) => void
  /** 删除回调 */
  onDelete?: (permission: Permission) => void
  /** 新增子权限回调 */
  onAddChild?: (parentPermission: Permission) => void
  /** 状态切换回调 */
  onToggleStatus?: (permission: Permission) => void
  /** 分页变化回调 */
  onPageChange?: (page: number, pageSize?: number) => void
}

/**
 * 权限表格组件
 */
const PermissionTable: React.FC<PermissionTableProps> = ({
  data,
  loading = false,
  selectedIds = [],
  onSelectionChange,
  onEdit,
  onDelete,
  onAddChild,
  onToggleStatus,
  onPageChange
}) => {
  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = data.records.map(permission => permission.id)
      onSelectionChange?.(allIds)
    } else {
      onSelectionChange?.([])
    }
  }

  /**
   * 处理单个选择
   */
  const handleSelectOne = (id: number, checked: boolean) => {
    const newSelectedIds = checked
      ? [...selectedIds, id]
      : selectedIds.filter(selectedId => selectedId !== id)
    onSelectionChange?.(newSelectedIds)
  }

  const isAllSelected = data.records.length > 0 && selectedIds.length === data.records.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.records.length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  if (data.records.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <div className="text-center space-y-2">
          <Shield className="w-12 h-12 mx-auto opacity-50" />
          <p>暂无权限数据</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isIndeterminate}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>权限信息</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>资源路径</TableHead>
              <TableHead>方法</TableHead>
              <TableHead>排序</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead className="w-32">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.records.map(permission => {
              const isSelected = selectedIds.includes(permission.id)
              
              return (
                <TableRow key={permission.id} className="hover:bg-muted/50">
                  {/* 选择框 */}
                  <TableCell>
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => handleSelectOne(permission.id, checked as boolean)}
                    />
                  </TableCell>

                  {/* 权限信息 */}
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Shield className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{permission.permissionName}</div>
                        <div className="text-sm text-muted-foreground">{permission.permissionCode}</div>
                        {permission.remark && (
                          <div className="text-xs text-muted-foreground mt-1 max-w-xs truncate">
                            {permission.remark}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>

                  {/* 权限类型 */}
                  <TableCell>
                    <Badge className={getPermissionTypeColor(permission.permissionType)}>
                      {getPermissionTypeLabel(permission.permissionType)}
                    </Badge>
                  </TableCell>

                  {/* 资源路径 */}
                  <TableCell>
                    <div className="max-w-xs truncate" title={permission.resourcePath}>
                      {permission.resourcePath || '-'}
                    </div>
                  </TableCell>

                  {/* HTTP方法 */}
                  <TableCell>
                    {permission.method ? (
                      <Badge variant="outline" className="text-xs">
                        {permission.method}
                      </Badge>
                    ) : (
                      '-'
                    )}
                  </TableCell>

                  {/* 排序 */}
                  <TableCell>{permission.sortOrder}</TableCell>

                  {/* 状态 */}
                  <TableCell>
                    <Badge className={getPermissionStatusColor(permission.status)}>
                      {getPermissionStatusLabel(permission.status)}
                    </Badge>
                  </TableCell>

                  {/* 创建时间 */}
                  <TableCell>
                    <div className="text-sm">
                      {permission.createTime ? new Date(permission.createTime).toLocaleDateString() : '-'}
                    </div>
                  </TableCell>

                  {/* 操作 */}
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {/* 编辑按钮 - 蓝色 */}
                      <ActionPermissionButton
                        module="permission"
                        action="edit"
                        config={{
                          text: '',
                          icon: Edit,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onEdit?.(permission),
                          title: '编辑权限',
                          className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                        }}
                      />
                      
                      {/* 新增子权限按钮 - 绿色 */}
                      <ActionPermissionButton
                        module="permission"
                        action="add"
                        config={{
                          text: '',
                          icon: Plus,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onAddChild?.(permission),
                          title: '新增子权限',
                          className: 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        }}
                      />
                      
                      {/* 状态切换按钮 - 动态颜色 */}
                      <ActionPermissionButton
                        module="permission"
                        action="edit"
                        config={{
                          text: '',
                          icon: permission.status === PermissionStatus.ENABLED ? UserX : UserCheck,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onToggleStatus?.(permission),
                          title: permission.status === PermissionStatus.ENABLED ? '禁用权限' : '启用权限',
                          className: permission.status === PermissionStatus.ENABLED 
                            ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                            : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        }}
                      />
                      
                      {/* 删除按钮 - 红色 */}
                      <ActionPermissionButton
                        module="permission"
                        action="delete"
                        config={{
                          text: '',
                          icon: Trash2,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onDelete?.(permission),
                          title: '删除权限',
                          className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      <DataPagination
        current={data.pageNum}
        pageSize={data.pageSize}
        total={data.total}
        onPageChange={(page) => onPageChange?.(page, data.pageSize)}
        onPageSizeChange={(pageSize) => onPageChange?.(1, pageSize)}
      />
    </div>
  )
}

export default PermissionTable
