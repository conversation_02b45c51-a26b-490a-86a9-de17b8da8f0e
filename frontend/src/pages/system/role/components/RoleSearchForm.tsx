import React, { useState } from 'react'
import { useDebounceFn } from 'ahooks'
import { Search, Filter, X, Calendar } from 'lucide-react'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  Badge,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '../../../../components/ui'
import type { RoleQueryRequest } from '../../../../types/role'
import { 
  ROLE_TYPE_OPTIONS, 
  DATA_SCOPE_OPTIONS, 
  ROLE_STATUS_OPTIONS 
} from '../../../../types/role'

/**
 * 搜索表单属性
 */
interface RoleSearchFormProps {
  /** 搜索参数 */
  searchParams: RoleQueryRequest
  /** 搜索参数变更回调 */
  onSearchChange: (params: RoleQueryRequest) => void
  /** 搜索提交回调 */
  onSearch: () => void
  /** 重置搜索回调 */
  onReset: () => void
  /** 是否加载中 */
  loading?: boolean
}

/**
 * 角色搜索表单组件
 * 
 * 功能特性：
 * - 实时搜索（角色编码、角色名称）
 * - 高级筛选（角色类型、数据权限、状态、创建时间）
 * - 搜索条件显示和清除
 * - 响应式设计
 */
const RoleSearchForm: React.FC<RoleSearchFormProps> = ({
  searchParams,
  onSearchChange,
  onSearch,
  onReset,
  loading = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [searchKeyword, setSearchKeyword] = useState('')

  // 防抖搜索函数
  const { run: debouncedSearch } = useDebounceFn(
    (keyword: string) => {
      console.log('🔍 防抖搜索触发:', keyword)
      // 修复：使用统一的keyword字段，后端会进行OR查询
      const newParams = {
        ...searchParams,
        keyword: keyword || undefined,
        // 清除具体字段的搜索条件，避免冲突
        roleCode: undefined,
        roleName: undefined,
        pageNum: 1 // 搜索时重置到第一页
      }
      onSearchChange(newParams)
    },
    {
      wait: 800, // 800ms防抖延迟
    }
  )

  // 处理基础搜索输入变更
  const handleBasicSearchChange = (value: string) => {
    setSearchKeyword(value)
    // 如果输入为空，立即搜索；否则使用防抖
    if (value.trim() === '') {
      const newParams = {
        ...searchParams,
        keyword: undefined,
        roleCode: undefined,
        roleName: undefined,
        pageNum: 1
      }
      onSearchChange(newParams)
    } else {
      debouncedSearch(value.trim())
    }
  }

  // 手动搜索（点击搜索按钮）
  const handleManualSearch = () => {
    console.log('🔍 手动搜索触发:', searchKeyword)
    const keyword = searchKeyword.trim()
    const newParams = {
      ...searchParams,
      keyword: keyword || undefined,
      // 清除具体字段的搜索条件，避免冲突
      roleCode: undefined,
      roleName: undefined,
      pageNum: 1
    }
    onSearchChange(newParams)
  }

  // 处理高级筛选变更
  const handleAdvancedFilterChange = (field: keyof RoleQueryRequest, value: any) => {
    const newParams = { ...searchParams, [field]: value || undefined }
    onSearchChange(newParams)
  }

  // 清除单个搜索条件 (暂时保留，可能在未来版本中使用)
  // const clearSearchCondition = (field: keyof RoleQueryRequest) => {
  //   const newParams = { ...searchParams }
  //   delete newParams[field]
  //   onSearchChange(newParams)
  // }

  // 获取活跃的搜索条件数量
  const getActiveFiltersCount = () => {
    let count = 0
    if (searchParams.roleCode) count++
    if (searchParams.roleName) count++
    if (searchParams.roleType) count++
    if (searchParams.dataScope) count++
    if (searchParams.status !== undefined) count++
    if (searchParams.createTimeStart) count++
    if (searchParams.createTimeEnd) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 基础搜索区域 */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索角色编码或角色名称... (输入后自动搜索，或点击搜索按钮)"
                value={searchKeyword}
                onChange={(e) => handleBasicSearchChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleManualSearch()
                  }
                }}
                className="pl-10"
                disabled={loading}
              />
            </div>

            <Button
              onClick={handleManualSearch}
              disabled={loading}
              className="shrink-0"
            >
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>

            <Popover open={showAdvanced} onOpenChange={setShowAdvanced}>
              <PopoverTrigger asChild>
                <Button 
                  variant="outline" 
                  className="shrink-0 relative"
                  disabled={loading}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  高级筛选
                  {activeFiltersCount > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                    >
                      {activeFiltersCount}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">高级筛选</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAdvanced(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* 角色类型筛选 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">角色类型</label>
                    <Select
                      value={searchParams.roleType || 'all'}
                      onValueChange={(value) => handleAdvancedFilterChange('roleType', value === 'all' ? undefined : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择角色类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        {ROLE_TYPE_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 数据权限筛选 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">数据权限</label>
                    <Select
                      value={searchParams.dataScope || 'all'}
                      onValueChange={(value) => handleAdvancedFilterChange('dataScope', value === 'all' ? undefined : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择数据权限" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        {DATA_SCOPE_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 状态筛选 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">状态</label>
                    <Select
                      value={searchParams.status?.toString() || 'all'}
                      onValueChange={(value) => handleAdvancedFilterChange('status', value === 'all' ? undefined : parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        {ROLE_STATUS_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 创建时间筛选 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">创建时间</label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          type="date"
                          placeholder="开始日期"
                          value={searchParams.createTimeStart || ''}
                          onChange={(e) => handleAdvancedFilterChange('createTimeStart', e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          type="date"
                          placeholder="结束日期"
                          value={searchParams.createTimeEnd || ''}
                          onChange={(e) => handleAdvancedFilterChange('createTimeEnd', e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex justify-between pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onReset}
                      disabled={loading}
                    >
                      重置
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => {
                        onSearch()
                        setShowAdvanced(false)
                      }}
                      disabled={loading}
                    >
                      应用筛选
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {(activeFiltersCount > 0 || searchKeyword.trim()) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchKeyword('') // 清空搜索关键词
                  onReset()
                }}
                disabled={loading}
                className="text-muted-foreground"
              >
                <X className="w-4 h-4 mr-1" />
                清除所有
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default RoleSearchForm