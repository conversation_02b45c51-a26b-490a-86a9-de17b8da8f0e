import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Button,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Switch,
  Alert,
  AlertDescription
} from '@/components/ui'
import { Loader2, AlertCircle } from 'lucide-react'
import { RoleService } from '@/services/role'
import type { Role, RoleCreateRequest, RoleUpdateRequest } from '@/types/role'
import { RoleType, DataScope, RoleStatus } from '@/types/role'
import { useForm } from '@/hooks/useForm'

// 表单数据类型
interface RoleFormData {
  roleCode: string
  roleName: string
  roleType: RoleType
  dataScope: DataScope
  status: RoleStatus
  sortOrder: number
  remark: string
}

// 表单验证规则
const getFormConfig = () => ({
  defaultValues: {
    roleCode: '',
    roleName: '',
    roleType: RoleType.CUSTOM,
    dataScope: DataScope.DEPT,
    status: RoleStatus.ENABLED,
    sortOrder: 0,
    remark: ''
  } as RoleFormData,
  fields: {
    roleCode: {
      rules: {
        required: '角色编码不能为空',
        maxLength: { value: 50, message: '角色编码不能超过50个字符' },
        pattern: {
          value: /^[A-Z0-9_]+$/,
          message: '角色编码只能包含大写字母、数字和下划线'
        }
      }
    },
    roleName: {
      rules: {
        required: '角色名称不能为空',
        maxLength: { value: 50, message: '角色名称不能超过50个字符' }
      }
    },
    roleType: {
      rules: {
        required: '请选择角色类型'
      }
    },
    dataScope: {
      rules: {
        required: '请选择数据权限范围'
      }
    },
    sortOrder: {
      rules: {
        validate: (value: number) => {
          if (value < 0) return '排序值不能小于0'
          if (value > 999) return '排序值不能大于999'
          return true
        }
      }
    },
    remark: {
      rules: {
        maxLength: { value: 200, message: '备注不能超过200个字符' }
      }
    }
  }
})

export interface RoleFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role?: Role | null
  onSuccess: () => void
}

/**
 * 角色表单组件
 */
export const RoleForm: React.FC<RoleFormProps> = ({
  open,
  onOpenChange,
  role,
  onSuccess
}) => {
  const isEdit = !!role
  const title = isEdit ? '编辑角色' : '新增角色'

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  // 注意：权限管理已统一到菜单权限分配中，移除权限相关状态

  // 表单管理
  const form = useForm<RoleFormData>(getFormConfig())

  // 注意：权限管理已统一到菜单权限分配中，移除权限加载函数

  // 初始化表单数据
  useEffect(() => {
    if (open) {
      setSubmitError(null)

      if (isEdit && role) {
        // 编辑模式：填充表单数据
        form.reset({
          roleCode: role.roleCode,
          roleName: role.roleName,
          roleType: role.roleType as RoleType,
          dataScope: role.dataScope as DataScope,
          status: role.status as RoleStatus,
          sortOrder: role.sortOrder || 0,
          remark: role.remark || ''
        })

        // 注意：权限管理已统一到菜单权限分配中，此处不再加载权限列表
      } else {
        // 新增模式：重置表单
        form.reset()
      }
    }
  }, [open, isEdit, role?.id])

  // 注意：权限管理已统一到菜单权限分配中，不再需要监听数据权限范围变化

  // 提交表单
  const handleSubmit = async () => {
    console.log('🚀 开始提交表单')

    // 手动获取表单数据
    const formData: RoleFormData = {
      roleCode: form.watch('roleCode') || '',
      roleName: form.watch('roleName') || '',
      roleType: form.watch('roleType') || RoleType.CUSTOM,
      dataScope: form.watch('dataScope') || DataScope.DEPT,
      status: form.watch('status') || RoleStatus.ENABLED,
      sortOrder: form.watch('sortOrder') || 0,
      remark: form.watch('remark') || ''
    }

    console.log('📋 表单数据:', formData)

    // 验证必填字段
    const errors: any = {}
    if (!formData.roleCode?.trim()) {
      errors.roleCode = '角色编码不能为空'
    } else if (!/^[A-Z0-9_]+$/.test(formData.roleCode)) {
      errors.roleCode = '角色编码只能包含大写字母、数字和下划线'
    }

    if (!formData.roleName?.trim()) {
      errors.roleName = '角色名称不能为空'
    }

    // 如果有验证错误，显示错误并返回
    if (Object.keys(errors).length > 0) {
      console.log('❌ 表单验证失败:', errors)
      Object.keys(errors).forEach(key => {
        form.setError(key as keyof RoleFormData, errors[key])
      })
      return
    }

    try {
      setLoading(true)
      setSubmitError(null)

      console.log('表单数据:', formData)

      if (isEdit && role) {
        // 编辑角色（只编辑基本信息，权限通过菜单权限分配管理）
        const updateRequest: RoleUpdateRequest = {
          id: role.id,
          ...formData
        }
        console.log('更新请求数据:', updateRequest)
        await RoleService.updateRole(updateRequest)
        console.log('角色更新成功')
      } else {
        // 新增角色（只创建基本信息，权限通过菜单权限分配管理）
        const createRequest: RoleCreateRequest = {
          ...formData
        }
        console.log('创建请求数据:', createRequest)
        await RoleService.createRole(createRequest)
        console.log('角色创建成功')
      }

      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('角色操作失败:', error)
      setSubmitError(error.message || '操作失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {isEdit ? '修改角色信息和权限配置' : '创建新角色并配置权限'}
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 角色编码 */}
              <FormField>
                <FormItem>
                  <FormLabel>角色编码 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入角色编码"
                      disabled={isEdit} // 编辑时不允许修改编码
                      value={form.watch('roleCode') || ''}
                      onChange={(e) => {
                        const value = e.target.value.toUpperCase()
                        form.setValue('roleCode', value)
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    角色的唯一标识，只能包含大写字母、数字和下划线
                  </FormDescription>
                  <FormMessage>{form.formState.errors.roleCode}</FormMessage>
                </FormItem>
              </FormField>

              {/* 角色名称 */}
              <FormField>
                <FormItem>
                  <FormLabel>角色名称 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入角色名称"
                      value={form.watch('roleName') || ''}
                      onChange={(e) => form.setValue('roleName', e.target.value)}
                    />
                  </FormControl>
                  <FormMessage>{form.formState.errors.roleName}</FormMessage>
                </FormItem>
              </FormField>

              {/* 角色类型 */}
              <FormField>
                <FormItem>
                  <FormLabel>角色类型 *</FormLabel>
                  <Select
                    value={form.watch('roleType') || RoleType.CUSTOM}
                    onValueChange={(value) => form.setValue('roleType', value as RoleType)}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择角色类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={RoleType.SYSTEM}>系统角色</SelectItem>
                      <SelectItem value={RoleType.CUSTOM}>自定义角色</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage>{form.formState.errors.roleType}</FormMessage>
                </FormItem>
              </FormField>

              {/* 数据权限范围 */}
              <FormField>
                <FormItem>
                  <FormLabel>数据权限范围 *</FormLabel>
                  <Select
                    value={form.watch('dataScope') || DataScope.DEPT}
                    onValueChange={(value) => form.setValue('dataScope', value as DataScope)}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择数据权限范围" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={DataScope.ALL}>全部数据权限</SelectItem>
                      <SelectItem value={DataScope.DEPT}>部门数据权限</SelectItem>
                      <SelectItem value={DataScope.DEPT_AND_SUB}>部门及子部门数据权限</SelectItem>
                      <SelectItem value={DataScope.SELF}>仅本人数据权限</SelectItem>
                      <SelectItem value={DataScope.CUSTOM}>自定义数据权限</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage>{form.formState.errors.dataScope}</FormMessage>
                </FormItem>
              </FormField>

              {/* 排序 */}
              <FormField>
                <FormItem>
                  <FormLabel>排序</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="0"
                      value={form.watch('sortOrder') || 0}
                      onChange={(e) => form.setValue('sortOrder', Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>数值越小排序越靠前</FormDescription>
                  <FormMessage>{form.formState.errors.sortOrder}</FormMessage>
                </FormItem>
              </FormField>

              {/* 状态 */}
              <FormField>
                <FormItem className="flex flex-row items-center justify-between rounded-lg p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">启用状态</FormLabel>
                    <FormDescription>
                      控制角色是否可用
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={form.watch('status') === RoleStatus.ENABLED}
                      onCheckedChange={(checked) => {
                        const newStatus = checked ? RoleStatus.ENABLED : RoleStatus.DISABLED
                        console.log('🔄 状态切换:', checked, '→', newStatus)
                        form.setValue('status', newStatus)
                      }}
                    />
                  </FormControl>
                </FormItem>
              </FormField>
            </div>

            {/* 备注 */}
            <FormField>
              <FormItem>
                <FormLabel>备注</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="请输入备注信息"
                    className="resize-none"
                    value={form.watch('remark') || ''}
                    onChange={(e) => form.setValue('remark', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.remark}</FormMessage>
              </FormItem>
            </FormField>

            {/* 权限配置说明 - 统一权限管理 */}
            {form.watch('dataScope') === DataScope.CUSTOM && (
              <div className="space-y-4 border-t pt-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">权限配置说明</h3>
                  <div className="text-sm text-muted-foreground bg-blue-50 p-3 rounded-md">
                    <div className="flex items-start">
                      <div>
                        <div className="font-medium text-blue-700 mb-1">统一权限管理</div>
                        <div>角色的具体权限请通过"分配权限"按钮进行配置。在权限分配界面可以统一管理菜单访问权限和操作权限。</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isEdit ? '更新' : '创建'}
              </Button>
            </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default RoleForm
