import React, { useState } from 'react'
import {
  Edit,
  Trash2,
  Shield,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Checkbox,
  Badge,
} from '@/components/ui'
import { DataPagination } from '@/components/common/DataPagination'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper'
import type { Role, RoleStatus, RoleType } from '@/types'
import { ROLE_STATUS_LABELS, ROLE_TYPE_LABELS, DATA_SCOPE_LABELS } from '@/types'
import { highlightText } from '@/utils/highlight.ts'

/**
 * 表格排序配置
 */
interface SortConfig {
  key: keyof Role | null
  direction: 'asc' | 'desc'
}

/**
 * 角色表格组件属性
 */
interface RoleTableProps {
  /** 角色数据列表 */
  data: Role[]
  /** 是否加载中 */
  loading?: boolean
  /** 选中的角色ID列表 */
  selectedIds: number[]
  /** 选择变更回调 */
  onSelectionChange: (selectedIds: number[]) => void
  /** 编辑角色回调 */
  onEdit: (role: Role) => void
  /** 删除角色回调 */
  onDelete: (role: Role) => void
  /** 权限分配回调（统一的菜单权限管理） */
  onMenuAssign: (role: Role) => void
  /** 状态切换回调 */
  onStatusToggle: (role: Role) => void
  /** 分页配置 */
  pagination: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  /** 排序变更回调 */
  onSortChange?: (sortKey: keyof Role | null, direction: 'asc' | 'desc') => void
  /** 搜索关键词（用于高亮显示） */
  searchKeyword?: string
}

/**
 * 角色数据表格组件
 * 
 * 功能特性：
 * - 支持分页、排序、筛选
 * - 行选择和批量操作
 * - 状态切换
 * - 操作按钮（编辑、删除、权限配置）
 * - 响应式设计
 */
const RoleTable: React.FC<RoleTableProps> = ({
  data,
  loading = false,
  selectedIds,
  onSelectionChange,
  onEdit,
  onDelete,
  onMenuAssign,
  onStatusToggle,
  pagination,
  onSortChange,
  searchKeyword
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'asc' })

  // 处理排序
  const handleSort = (key: keyof Role) => {
    let direction: 'asc' | 'desc' = 'asc'
    
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    
    setSortConfig({ key, direction })
    onSortChange?.(key, direction)
  }

  // 渲染排序图标
  const renderSortIcon = (key: keyof Role) => {
    if (sortConfig.key !== key) {
      return null
    }
    
    return sortConfig.direction === 'asc' ? (
      <ChevronUp className="w-4 h-4 ml-1" />
    ) : (
      <ChevronDown className="w-4 h-4 ml-1" />
    )
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = data.map(role => role.id)
      onSelectionChange(allIds)
    } else {
      onSelectionChange([])
    }
  }

  // 单行选择
  const handleSelectRow = (roleId: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, roleId])
    } else {
      onSelectionChange(selectedIds.filter(id => id !== roleId))
    }
  }

  // 检查是否全选
  const isAllSelected = data.length > 0 && selectedIds.length === data.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.length

  // 格式化状态显示
  const formatStatus = (status: RoleStatus) => {
    const isEnabled = status === 1
    return (
      <Badge variant={isEnabled ? "default" : "secondary"}>
        {ROLE_STATUS_LABELS[status]}
      </Badge>
    )
  }

  // 格式化角色类型
  const formatRoleType = (roleType: RoleType) => {
    const isSystem = roleType === 'SYSTEM'
    return (
      <Badge variant={isSystem ? "outline" : "secondary"}>
        {ROLE_TYPE_LABELS[roleType]}
      </Badge>
    )
  }

  // 格式化数据权限范围
  const formatDataScope = (dataScope: string) => {
    return DATA_SCOPE_LABELS[dataScope as keyof typeof DATA_SCOPE_LABELS] || dataScope
  }

  // 处理页面大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    console.log('📏 页面大小变更:', newPageSize)
    pagination.onChange(1, newPageSize) // 切换页面大小时回到第一页
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <div className="h-4 w-4 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead>
                  <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                </TableHead>
                <TableHead className="w-32">
                  <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 w-4 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="选择所有角色"
                  data-state={isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked"}
                />
              </TableHead>
              <TableHead
                className="w-32 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('roleCode')}
              >
                <div className="flex items-center">
                  角色编码
                  {renderSortIcon('roleCode')}
                </div>
              </TableHead>
              <TableHead
                className="w-40 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('roleName')}
              >
                <div className="flex items-center">
                  角色名称
                  {renderSortIcon('roleName')}
                </div>
              </TableHead>
              <TableHead className="w-24">角色类型</TableHead>
              <TableHead className="w-36">数据权限</TableHead>
              <TableHead
                className="w-20 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  状态
                  {renderSortIcon('status')}
                </div>
              </TableHead>
              <TableHead
                className="w-40 cursor-pointer select-none hover:bg-muted/50"
                onClick={() => handleSort('createTime')}
              >
                <div className="flex items-center">
                  创建时间
                  {renderSortIcon('createTime')}
                </div>
              </TableHead>
              <TableHead className="w-36">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              data.map((role) => (
                <TableRow 
                  key={role.id}
                  data-state={selectedIds.includes(role.id) ? "selected" : undefined}
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedIds.includes(role.id)}
                      onCheckedChange={(checked) => handleSelectRow(role.id, checked as boolean)}
                      aria-label={`选择角色 ${role.roleName}`}
                    />
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {searchKeyword ? highlightText(role.roleCode, searchKeyword) : role.roleCode}
                  </TableCell>
                  <TableCell className="font-medium">
                    {searchKeyword ? highlightText(role.roleName, searchKeyword) : role.roleName}
                  </TableCell>
                  <TableCell>
                    {formatRoleType(role.roleType)}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDataScope(role.dataScope)}
                  </TableCell>
                  <TableCell>
                    <button
                      onClick={() => onStatusToggle(role)}
                      className="cursor-pointer"
                      title={`点击${role.status === 1 ? '禁用' : '启用'}角色`}
                    >
                      {formatStatus(role.status)}
                    </button>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {new Date(role.createTime).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {/* 编辑按钮 - 蓝色 */}
                      <ActionPermissionButton
                        module="role"
                        action="edit"
                        config={{
                          text: '',
                          icon: Edit,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onEdit(role),
                          title: '编辑角色',
                          className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                        }}
                      />

                      {/* 分配权限按钮 - 紫色（统一权限管理入口） */}
                      <ActionPermissionButton
                        module="role"
                        action="assignMenus"
                        config={{
                          text: '',
                          icon: Shield,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onMenuAssign(role),
                          title: '分配权限',
                          className: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                        }}
                      />

                      {/* 删除按钮 - 红色 */}
                      <ActionPermissionButton
                        module="role"
                        action="delete"
                        config={{
                          text: '',
                          icon: Trash2,
                          variant: 'ghost',
                          size: 'sm',
                          onClick: () => onDelete(role),
                          title: '删除角色',
                          disabled: role.roleType === 'SYSTEM',
                          className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 统一分页组件 */}
      <DataPagination
        current={pagination.current}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onPageChange={(page) => {
          console.log('📄 页码点击:', page, '当前页面大小:', pagination.pageSize)
          pagination.onChange(page, pagination.pageSize || 10)
        }}
        onPageSizeChange={handlePageSizeChange}
        showSizeChanger={true}
        showTotal={true}
        pageSizeOptions={[10, 20, 50, 100]}
      />
    </div>
  )
}

export default RoleTable