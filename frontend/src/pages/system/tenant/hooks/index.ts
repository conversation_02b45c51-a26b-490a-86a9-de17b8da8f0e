/**
 * 租户管理Hooks导出文件
 * 
 * 统一导出所有租户管理相关的自定义Hooks
 */

import { useTenantList } from './useTenantList'
import { useTenantOperations } from './useTenantOperations'
import { useTenantPerformance } from '@/pages/system/tenant'
import { useTenantSearch } from './useTenantSearch'
import { useTenantSelection } from './useTenantSelection'

// 基础Hooks
export { useAsyncOperation, useBatchAsyncOperation } from './useAsyncOperation'
export type { AsyncOperationState, AsyncOperationOptions } from './useAsyncOperation'

// 数据管理Hooks
export { useTenantList } from './useTenantList'
export type { UseTenantListOptions, TenantListState } from './useTenantList'

// 操作管理Hooks
export { useTenantOperations } from './useTenantOperations'
export type { UseTenantOperationsOptions } from './useTenantOperations'

// 选择管理Hooks
export { useTenantSelection } from './useTenantSelection'
export type { UseTenantSelectionOptions } from './useTenantSelection'

// 搜索管理Hooks
export { useTenantSearch } from './useTenantSearch'
export type { 
  UseTenantSearchOptions, 
  SearchFormState, 
  // SearchStatus
} from './useTenantSearch'

// 性能优化Hooks
export { 
  useTenantPerformance, 
  useTenantVirtualization 
} from './useTenantPerformance'
export type { UseTenantPerformanceOptions } from './useTenantPerformance'

/**
 * 组合Hook：租户管理完整功能
 * 
 * 将所有相关Hooks组合在一起，提供完整的租户管理功能
 */
export const useTenantManagement = (options: {
  initialPageSize?: number
  autoLoad?: boolean
  enablePerformanceOptimization?: boolean
} = {}) => {
  const {
    initialPageSize = 10,
    autoLoad = true,
    enablePerformanceOptimization = true
  } = options

  // 数据管理
  const tenantListData = useTenantList({ initialPageSize, autoLoad })
  
  // 操作管理
  const tenantOperationsData = useTenantOperations({
    onSuccess: (operation: string, tenant: any) => {
      switch (operation) {
        case 'create':
          if (tenant) tenantListData.addTenantToList(tenant)
          break
        case 'update':
          if (tenant) tenantListData.updateTenantInList(tenant)
          break
        case 'delete':
          if (tenant) tenantListData.removeTenantFromList(tenant.id)
          break
        case 'toggleStatus':
          if (tenant) tenantListData.updateTenantInList(tenant)
          break
        default:
          tenantListData.refresh()
      }
    }
  })
  
  // 选择管理
  const tenantSelectionData = useTenantSelection()
  
  // 搜索管理
  const tenantSearchData = useTenantSearch(tenantListData.searchParams, {
    onSearchChange: (params: any) => {
      tenantListData.updateSearchParams(params)
      tenantSelectionData.clearSelection()
    }
  })
  
  // 性能优化 - 始终调用Hook，但可以通过参数控制是否启用
  const tenantPerformanceData = useTenantPerformance(
    enablePerformanceOptimization ? tenantListData.data : []
  )

  return {
    // 数据状态
    data: tenantListData.data,
    total: tenantListData.total,
    loading: tenantListData.loading || tenantOperationsData.loading,
    error: tenantListData.error,
    
    // 分页和排序
    pagination: tenantListData.pagination,
    sortConfig: tenantListData.sortConfig,
    handlePageChange: tenantListData.handlePageChange,
    handleSort: tenantListData.handleSort,

    // 搜索功能
    searchState: tenantSearchData.formState,
    searchStatus: tenantSearchData.getSearchStatus(),
    searchSummary: tenantSearchData.getSearchSummary(),
    updateKeyword: tenantSearchData.updateKeyword,
    updateAdvancedFilters: tenantSearchData.updateAdvancedFilters,
    toggleAdvanced: tenantSearchData.toggleAdvanced,
    executeSearch: tenantSearchData.executeSearch,
    resetSearch: tenantSearchData.resetSearch,
    quickFilterByStatus: tenantSearchData.quickFilterByStatus,
    handleKeyPress: tenantSearchData.handleKeyPress,
    
    // 选择功能
    selectedIds: tenantSelectionData.selectedIds,
    selectionStats: tenantSelectionData.selectionStats,
    toggleSelection: tenantSelectionData.toggleSelection,
    clearSelection: tenantSelectionData.clearSelection,
    toggleSelectAll: tenantSelectionData.toggleSelectAll,
    getSelectedTenants: tenantSelectionData.getSelectedTenants,
    isSelected: tenantSelectionData.isSelected,
    isAllSelected: tenantSelectionData.isAllSelected,
    isIndeterminate: tenantSelectionData.isIndeterminate,
    bulkOperations: tenantSelectionData.bulkOperations,
    
    // CRUD操作
    formDialogOpen: tenantOperationsData.formDialogOpen,
    editingTenant: tenantOperationsData.editingTenant,
    deleteDialogOpen: tenantOperationsData.deleteDialogOpen,
    deletingTenant: tenantOperationsData.deletingTenant,
    createTenant: tenantOperationsData.createTenant,
    updateTenant: tenantOperationsData.updateTenant,
    deleteTenant: tenantOperationsData.deleteTenant,
    batchDeleteTenants: tenantOperationsData.batchDeleteTenants,
    toggleTenantStatus: tenantOperationsData.toggleTenantStatus,
    exportTenants: tenantOperationsData.exportTenants,
    openCreateDialog: tenantOperationsData.openCreateDialog,
    openEditDialog: tenantOperationsData.openEditDialog,
    closeFormDialog: tenantOperationsData.closeFormDialog,
    openDeleteDialog: tenantOperationsData.openDeleteDialog,
    closeDeleteDialog: tenantOperationsData.closeDeleteDialog,
    confirmDelete: tenantOperationsData.confirmDelete,
    
    // 数据操作
    refresh: tenantListData.refresh,
    updateTenantInList: tenantListData.updateTenantInList,
    removeTenantFromList: tenantListData.removeTenantFromList,
    addTenantToList: tenantListData.addTenantToList,
    findTenantById: tenantListData.findTenantById,
    
    // 性能优化
    performance: tenantPerformanceData ? {
      optimizedSearch: tenantPerformanceData.optimizedSearch,
      getPerformanceStats: tenantPerformanceData.getPerformanceStats,
      hasDataChanged: tenantPerformanceData.hasDataChanged
    } : null
  }
}
