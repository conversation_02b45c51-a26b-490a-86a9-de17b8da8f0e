/**
 * 通用异步操作Hook
 * 
 * 提供统一的异步操作状态管理，包括loading、error处理等
 */

import { useState, useCallback } from 'react'
import { useToast } from '@/hooks'

export interface AsyncOperationState {
  loading: boolean
  error: string | null
}

export interface AsyncOperationOptions {
  successMessage?: string
  errorMessage?: string
  showSuccessToast?: boolean
  showErrorToast?: boolean
  onSuccess?: () => void
  onError?: (error: Error) => void
}

/**
 * 通用异步操作Hook
 */
export const useAsyncOperation = () => {
  const { toast } = useToast()
  const [state, setState] = useState<AsyncOperationState>({
    loading: false,
    error: null
  })

  /**
   * 执行异步操作
   */
  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    const {
      successMessage,
      errorMessage,
      showSuccessToast = true,
      showErrorToast = true,
      onSuccess,
      onError
    } = options

    try {
      setState({ loading: true, error: null })
      
      const result = await operation()
      
      setState({ loading: false, error: null })
      
      // 显示成功提示
      if (showSuccessToast && successMessage) {
        toast({
          title: '操作成功',
          description: successMessage
        })
      }
      
      // 执行成功回调
      onSuccess?.()
      
      return result
    } catch (error) {
      const errorMsg = (error as Error).message
      setState({ loading: false, error: errorMsg })
      
      // 显示错误提示
      if (showErrorToast) {
        toast({
          title: '操作失败',
          description: errorMessage || errorMsg,
          variant: 'destructive'
        })
      }
      
      // 执行错误回调
      onError?.(error as Error)
      
      return null
    }
  }, [toast])

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    setState({ loading: false, error: null })
  }, [])

  return {
    ...state,
    execute,
    clearError,
    reset
  }
}

/**
 * 批量异步操作Hook
 */
export const useBatchAsyncOperation = () => {
  const { toast } = useToast()
  const [state, setState] = useState<AsyncOperationState & { 
    progress: number 
    total: number 
  }>({
    loading: false,
    error: null,
    progress: 0,
    total: 0
  })

  /**
   * 执行批量异步操作
   */
  const executeBatch = useCallback(async <T>(
    operations: (() => Promise<T>)[],
    options: AsyncOperationOptions & {
      stopOnError?: boolean
    } = {}
  ): Promise<(T | null)[]> => {
    const {
      successMessage,
      errorMessage,
      showSuccessToast = true,
      showErrorToast = true,
      stopOnError: _stopOnError = false,
      onSuccess,
      onError
    } = options

    const results: (T | null)[] = []
    const total = operations.length

    try {
      setState({ loading: true, error: null, progress: 0, total })
      
      for (let i = 0; i < operations.length; i++) {
        try {
          const result = await operations[i]()
          results.push(result)
          setState(prev => ({ ...prev, progress: i + 1 }))
        } catch (error) {
          results.push(null)
          console.error(`批量操作第${i + 1}项失败:`, error)
        }
      }
      
      setState(prev => ({ ...prev, loading: false }))
      
      // 显示成功提示
      if (showSuccessToast && successMessage) {
        toast({
          title: '批量操作完成',
          description: successMessage
        })
      }
      
      // 执行成功回调
      onSuccess?.()
      
      return results
    } catch (error) {
      const errorMsg = (error as Error).message
      setState(prev => ({ ...prev, loading: false, error: errorMsg }))
      
      // 显示错误提示
      if (showErrorToast) {
        toast({
          title: '批量操作失败',
          description: errorMessage || errorMsg,
          variant: 'destructive'
        })
      }
      
      // 执行错误回调
      onError?.(error as Error)
      
      return results
    }
  }, [toast])

  return {
    ...state,
    executeBatch
  }
}
