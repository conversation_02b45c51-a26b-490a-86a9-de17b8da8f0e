/**
 * 租户表格选择管理Hook
 * 
 * 管理表格的选择状态，包括单选、多选、全选等功能
 */

import { useState, useCallback, useMemo } from 'react'
import type { Tenant } from '@/types/tenant'

export interface UseTenantSelectionOptions {
  onSelectionChange?: (selectedIds: number[], selectedTenants: Tenant[]) => void
}

/**
 * 租户表格选择管理Hook
 */
export const useTenantSelection = (options: UseTenantSelectionOptions = {}) => {
  const { onSelectionChange } = options
  
  const [selectedIds, setSelectedIds] = useState<number[]>([])

  /**
   * 切换单个项目的选择状态
   */
  const toggleSelection = useCallback((id: number, tenants: Tenant[]) => {
    setSelectedIds(prev => {
      const newSelectedIds = prev.includes(id)
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
      
      // 触发选择变化回调
      const selectedTenants = tenants.filter(tenant => newSelectedIds.includes(tenant.id))
      onSelectionChange?.(newSelectedIds, selectedTenants)
      
      return newSelectedIds
    })
  }, [onSelectionChange])

  /**
   * 设置选择的项目
   */
  const setSelection = useCallback((ids: number[], tenants: Tenant[]) => {
    setSelectedIds(ids)
    
    // 触发选择变化回调
    const selectedTenants = tenants.filter(tenant => ids.includes(tenant.id))
    onSelectionChange?.(ids, selectedTenants)
  }, [onSelectionChange])

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = useCallback((tenants: Tenant[]) => {
    const allIds = tenants.map(tenant => tenant.id)
    const isAllSelected = allIds.length > 0 && allIds.every(id => selectedIds.includes(id))
    
    const newSelectedIds = isAllSelected ? [] : allIds
    setSelectedIds(newSelectedIds)
    
    // 触发选择变化回调
    const selectedTenants = isAllSelected ? [] : tenants
    onSelectionChange?.(newSelectedIds, selectedTenants)
  }, [selectedIds, onSelectionChange])

  /**
   * 清除所有选择
   */
  const clearSelection = useCallback(() => {
    setSelectedIds([])
    onSelectionChange?.([], [])
  }, [onSelectionChange])

  /**
   * 选择指定状态的租户
   */
  const selectByStatus = useCallback((status: number, tenants: Tenant[]) => {
    const filteredIds = tenants
      .filter(tenant => tenant.status === status)
      .map(tenant => tenant.id)
    
    setSelectedIds(filteredIds)
    
    // 触发选择变化回调
    const selectedTenants = tenants.filter(tenant => filteredIds.includes(tenant.id))
    onSelectionChange?.(filteredIds, selectedTenants)
  }, [onSelectionChange])

  /**
   * 反选
   */
  const invertSelection = useCallback((tenants: Tenant[]) => {
    const allIds = tenants.map(tenant => tenant.id)
    const newSelectedIds = allIds.filter(id => !selectedIds.includes(id))
    
    setSelectedIds(newSelectedIds)
    
    // 触发选择变化回调
    const selectedTenants = tenants.filter(tenant => newSelectedIds.includes(tenant.id))
    onSelectionChange?.(newSelectedIds, selectedTenants)
  }, [selectedIds, onSelectionChange])

  /**
   * 获取选中的租户数据
   */
  const getSelectedTenants = useCallback((tenants: Tenant[]): Tenant[] => {
    return tenants.filter(tenant => selectedIds.includes(tenant.id))
  }, [selectedIds])

  /**
   * 检查是否选中指定租户
   */
  const isSelected = useCallback((id: number): boolean => {
    return selectedIds.includes(id)
  }, [selectedIds])

  /**
   * 检查是否全选
   */
  const isAllSelected = useCallback((tenants: Tenant[]): boolean => {
    if (tenants.length === 0) return false
    return tenants.every(tenant => selectedIds.includes(tenant.id))
  }, [selectedIds])

  /**
   * 检查是否部分选中
   */
  const isIndeterminate = useCallback((tenants: Tenant[]): boolean => {
    if (tenants.length === 0 || selectedIds.length === 0) return false
    const selectedCount = tenants.filter(tenant => selectedIds.includes(tenant.id)).length
    return selectedCount > 0 && selectedCount < tenants.length
  }, [selectedIds])

  // 计算选择统计信息
  const selectionStats = useMemo(() => ({
    selectedCount: selectedIds.length,
    hasSelection: selectedIds.length > 0,
    selectedIds
  }), [selectedIds])

  // 批量操作辅助方法
  const bulkOperations = useMemo(() => ({
    /**
     * 获取选中租户的状态分布
     */
    getStatusDistribution: (tenants: Tenant[]) => {
      const selectedTenants = getSelectedTenants(tenants)
      const distribution = selectedTenants.reduce((acc, tenant) => {
        acc[tenant.status] = (acc[tenant.status] || 0) + 1
        return acc
      }, {} as Record<number, number>)
      
      return {
        enabled: distribution[1] || 0,
        disabled: distribution[0] || 0,
        total: selectedTenants.length
      }
    },

    /**
     * 检查是否可以批量启用
     */
    canBulkEnable: (tenants: Tenant[]) => {
      const selectedTenants = getSelectedTenants(tenants)
      return selectedTenants.some(tenant => tenant.status === 0)
    },

    /**
     * 检查是否可以批量禁用
     */
    canBulkDisable: (tenants: Tenant[]) => {
      const selectedTenants = getSelectedTenants(tenants)
      return selectedTenants.some(tenant => tenant.status === 1)
    },

    /**
     * 检查是否可以批量删除
     */
    canBulkDelete: (tenants: Tenant[]) => {
      return getSelectedTenants(tenants).length > 0
    }
  }), [getSelectedTenants])

  return {
    // 状态
    selectedIds,
    selectionStats,

    // 基础操作
    toggleSelection,
    setSelection,
    clearSelection,

    // 批量选择操作
    toggleSelectAll,
    selectByStatus,
    invertSelection,

    // 查询方法
    getSelectedTenants,
    isSelected,
    isAllSelected,
    isIndeterminate,

    // 批量操作辅助
    bulkOperations
  }
}
