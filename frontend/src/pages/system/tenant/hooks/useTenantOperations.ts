/**
 * 租户CRUD操作Hook
 * 
 * 管理租户的创建、编辑、删除、状态切换等操作
 */

import { useState, useCallback } from 'react'
import type { Tenant, TenantCreateRequest, TenantUpdateRequest } from '@/types/tenant'
import { TenantService } from '@/services/tenant'
import { useAsyncOperation, useBatchAsyncOperation } from './useAsyncOperation'

export interface UseTenantOperationsOptions {
  onSuccess?: (operation: string, tenant?: Tenant) => void
  onError?: (operation: string, error: Error) => void
}

/**
 * 租户CRUD操作Hook
 */
export const useTenantOperations = (options: UseTenantOperationsOptions = {}) => {
  const { onSuccess, onError } = options
  
  const { loading, error, execute } = useAsyncOperation()
  const { loading: batchLoading, executeBatch } = useBatchAsyncOperation()
  
  // 表单对话框状态
  const [formDialogOpen, setFormDialogOpen] = useState(false)
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null)
  
  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingTenant, setDeletingTenant] = useState<Tenant | null>(null)

  /**
   * 创建租户
   */
  const createTenant = useCallback(async (data: TenantCreateRequest): Promise<Tenant | null> => {
    console.log('🚀 开始创建租户:', data)
    
    const result = await execute(
      () => TenantService.createTenant(data),
      {
        successMessage: '租户创建成功',
        errorMessage: '创建租户失败',
        onSuccess: () => {
          setFormDialogOpen(false)
          setEditingTenant(null)
          onSuccess?.('create')
        },
        onError: (error) => onError?.('create', error)
      }
    )
    
    if (result) {
      console.log('✅ 租户创建成功:', result)
    }
    
    return result
  }, [execute, onSuccess, onError])

  /**
   * 更新租户
   */
  const updateTenant = useCallback(async (id: number, data: TenantUpdateRequest): Promise<Tenant | null> => {
    console.log('🚀 开始更新租户:', id, data)
    
    const result = await execute(
      () => TenantService.updateTenant({ ...data, id }),
      {
        successMessage: '租户更新成功',
        errorMessage: '更新租户失败',
        onSuccess: () => {
          setFormDialogOpen(false)
          setEditingTenant(null)
          onSuccess?.('update')
        },
        onError: (error) => onError?.('update', error)
      }
    )
    
    if (result) {
      console.log('✅ 租户更新成功:', result)
    }
    
    return result
  }, [execute, onSuccess, onError])

  /**
   * 删除租户
   */
  const deleteTenant = useCallback(async (tenant: Tenant): Promise<boolean> => {
    console.log('🚀 开始删除租户:', tenant.id)
    
    const result = await execute(
      () => TenantService.deleteTenant(tenant.id),
      {
        successMessage: `租户 "${tenant.tenantName}" 删除成功`,
        errorMessage: '删除租户失败',
        onSuccess: () => {
          setDeleteDialogOpen(false)
          setDeletingTenant(null)
          onSuccess?.('delete', tenant)
        },
        onError: (error) => onError?.('delete', error)
      }
    )
    
    if (result !== null) {
      console.log('✅ 租户删除成功')
      return true
    }
    
    return false
  }, [execute, onSuccess, onError])

  /**
   * 批量删除租户
   */
  const batchDeleteTenants = useCallback(async (tenants: Tenant[]): Promise<boolean> => {
    console.log('🚀 开始批量删除租户:', tenants.map(t => t.id))
    
    const operations = tenants.map(tenant => 
      () => TenantService.deleteTenant(tenant.id)
    )
    
    const results = await executeBatch(operations, {
      successMessage: `成功删除 ${tenants.length} 个租户`,
      errorMessage: '批量删除租户失败',
      stopOnError: false,
      onSuccess: () => onSuccess?.('batchDelete'),
      onError: (error) => onError?.('batchDelete', error)
    })
    
    const successCount = results.filter(result => result !== null).length
    console.log(`✅ 批量删除完成，成功: ${successCount}/${tenants.length}`)
    
    return successCount > 0
  }, [executeBatch, onSuccess, onError])

  /**
   * 切换租户状态
   */
  const toggleTenantStatus = useCallback(async (tenant: Tenant): Promise<Tenant | null> => {
    const newStatus = tenant.status === 1 ? 0 : 1
    const statusText = newStatus === 1 ? '启用' : '禁用'
    
    console.log('🚀 开始切换租户状态:', tenant.id, statusText)
    
    const result = await execute(
      () => TenantService.updateTenantStatus(tenant.id, newStatus),
      {
        successMessage: `租户 "${tenant.tenantName}" ${statusText}成功`,
        errorMessage: `${statusText}租户失败`,
        onSuccess: () => onSuccess?.('toggleStatus', { ...tenant, status: newStatus }),
        onError: (error) => onError?.('toggleStatus', error)
      }
    )
    
    if (result) {
      console.log('✅ 租户状态切换成功:', result)
    }
    
    return result
  }, [execute, onSuccess, onError])

  /**
   * 导出租户数据
   */
  const exportTenants = useCallback(async (tenantIds?: number[]): Promise<boolean> => {
    console.log('🚀 开始导出租户数据:', tenantIds)
    
    const result = await execute(
      () => TenantService.exportTenants(tenantIds),
      {
        successMessage: '租户数据导出成功',
        errorMessage: '导出租户数据失败',
        onSuccess: () => onSuccess?.('export'),
        onError: (error) => onError?.('export', error)
      }
    )
    
    return result !== null
  }, [execute, onSuccess, onError])

  /**
   * 打开新增租户对话框
   */
  const openCreateDialog = useCallback(() => {
    setEditingTenant(null)
    setFormDialogOpen(true)
  }, [])

  /**
   * 打开编辑租户对话框
   */
  const openEditDialog = useCallback((tenant: Tenant) => {
    setEditingTenant(tenant)
    setFormDialogOpen(true)
  }, [])

  /**
   * 关闭表单对话框
   */
  const closeFormDialog = useCallback(() => {
    setFormDialogOpen(false)
    setEditingTenant(null)
  }, [])

  /**
   * 打开删除确认对话框
   */
  const openDeleteDialog = useCallback((tenant: Tenant) => {
    setDeletingTenant(tenant)
    setDeleteDialogOpen(true)
  }, [])

  /**
   * 关闭删除确认对话框
   */
  const closeDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(false)
    setDeletingTenant(null)
  }, [])

  /**
   * 确认删除
   */
  const confirmDelete = useCallback(async () => {
    if (deletingTenant) {
      await deleteTenant(deletingTenant)
    }
  }, [deletingTenant, deleteTenant])

  return {
    // 状态
    loading: loading || batchLoading,
    error,
    formDialogOpen,
    editingTenant,
    deleteDialogOpen,
    deletingTenant,
    
    // CRUD操作
    createTenant,
    updateTenant,
    deleteTenant,
    batchDeleteTenants,
    toggleTenantStatus,
    exportTenants,
    
    // 对话框操作
    openCreateDialog,
    openEditDialog,
    closeFormDialog,
    openDeleteDialog,
    closeDeleteDialog,
    confirmDelete
  }
}
