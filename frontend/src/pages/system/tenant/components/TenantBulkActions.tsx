/**
 * 租户批量操作组件
 * 
 * 提供批量操作功能，如批量删除、批量导出等
 */

import React from 'react'
import { Button, Card, CardContent } from '@/components/ui'
import { Download, Trash2, Power, PowerOff } from 'lucide-react'
import type { Tenant } from '@/types/tenant'

export interface TenantBulkActionsProps {
  selectedCount: number
  selectedTenants: Tenant[]
  onClearSelection: () => void
  onBatchExport: () => void
  onBatchDelete: () => void
  onBatchEnable?: () => void
  onBatchDisable?: () => void
  loading?: boolean
  canBulkEnable?: boolean
  canBulkDisable?: boolean
}

/**
 * 租户批量操作组件
 */
const TenantBulkActions: React.FC<TenantBulkActionsProps> = ({
  selectedCount,
  selectedTenants,
  onClearSelection,
  onBatchExport,
  onBatchDelete,
  onBatchEnable,
  onBatchDisable,
  loading = false,
  canBulkEnable = false,
  canBulkDisable = false
}) => {
  if (selectedCount === 0) {
    return null
  }

  // 计算状态分布
  const statusDistribution = selectedTenants.reduce((acc, tenant) => {
    acc[tenant.status] = (acc[tenant.status] || 0) + 1
    return acc
  }, {} as Record<number, number>)

  const enabledCount = statusDistribution[1] || 0
  const disabledCount = statusDistribution[0] || 0

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              已选择 {selectedCount} 项
            </span>
            {enabledCount > 0 && (
              <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                启用: {enabledCount}
              </span>
            )}
            {disabledCount > 0 && (
              <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                禁用: {disabledCount}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={onClearSelection}
            >
              取消选择
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 批量启用 */}
            {canBulkEnable && onBatchEnable && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBatchEnable}
                disabled={loading}
              >
                <Power className="w-4 h-4 mr-2" />
                批量启用
              </Button>
            )}
            
            {/* 批量禁用 */}
            {canBulkDisable && onBatchDisable && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBatchDisable}
                disabled={loading}
              >
                <PowerOff className="w-4 h-4 mr-2" />
                批量禁用
              </Button>
            )}
            
            {/* 批量导出 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onBatchExport}
              disabled={loading}
            >
              <Download className="w-4 h-4 mr-2" />
              批量导出
            </Button>
            
            {/* 批量删除 */}
            <Button
              variant="destructive"
              size="sm"
              onClick={onBatchDelete}
              disabled={loading}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              批量删除
            </Button>
          </div>
        </div>
        
        {/* 操作提示 */}
        <div className="mt-2 text-xs text-muted-foreground">
          提示: 批量操作将应用于所有选中的租户，请谨慎操作
        </div>
      </CardContent>
    </Card>
  )
}

export default TenantBulkActions
