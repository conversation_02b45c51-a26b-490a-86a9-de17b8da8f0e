/**
 * 租户用户管理对话框组件
 * 
 * 提供租户用户的查看、分配和移除功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Checkbox,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Separator
} from '@/components/ui'
import { Users, UserPlus, UserMinus, Search } from 'lucide-react'
import { useToast } from '@/hooks'
import type { Tenant } from '@/types/tenant'
import { TenantService } from '@/services'

export interface TenantUser {
  id: number
  tenantId: number
  userId: number
  username: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  userRole: number
  userRoleName: string
  assignTime: string
  status: number
  statusName: string
  createTime: string
}

export interface AvailableUser {
  id: number
  username: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  status: number
}

export interface TenantUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tenant: Tenant | null
  onSuccess?: () => void
}

/**
 * 租户用户管理对话框组件
 */
const TenantUserDialog: React.FC<TenantUserDialogProps> = ({
  open,
  onOpenChange,
  tenant,
  onSuccess
}) => {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [tenantUsers, setTenantUsers] = useState<TenantUser[]>([])
  const [availableUsers, setAvailableUsers] = useState<AvailableUser[]>([])
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([])
  const [searchKeyword, setSearchKeyword] = useState('')
  const [activeTab, setActiveTab] = useState('current')

  // 加载租户用户列表
  const loadTenantUsers = async () => {
    if (!tenant) return

    try {
      setLoading(true)
      const users = await TenantService.getTenantUsers(tenant.id)
      setTenantUsers(users || [])
    } catch (error) {
      console.error('加载租户用户失败:', error)
      toast({
        title: '加载失败',
        description: '加载租户用户失败: ' + (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 加载可分配用户列表
  const loadAvailableUsers = async () => {
    if (!tenant) return

    try {
      setLoading(true)
      const users = await TenantService.getAvailableUsersForTenant(tenant.id)
      setAvailableUsers(users || [])
    } catch (error) {
      console.error('加载可分配用户失败:', error)
      toast({
        title: '加载失败',
        description: '加载可分配用户失败: ' + (error as Error).message,
        variant: 'destructive'
      })
      setAvailableUsers([])
    } finally {
      setLoading(false)
    }
  }

  // 分配用户到租户
  const assignUsers = async () => {
    if (!tenant || selectedUserIds.length === 0) return

    try {
      setLoading(true)
      await TenantService.assignUsersToTenant({
        tenantId: tenant.id,
        userIds: selectedUserIds
      })

      toast({
        title: '分配成功',
        description: `成功分配 ${selectedUserIds.length} 个用户到租户`
      })
      setSelectedUserIds([])
      await loadTenantUsers()
      await loadAvailableUsers()
      onSuccess?.()
    } catch (error) {
      console.error('分配用户失败:', error)
      toast({
        title: '分配失败',
        description: '分配用户失败: ' + (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 从租户移除用户
  const removeUser = async (userId: number) => {
    if (!tenant) return

    try {
      setLoading(true)
      await TenantService.removeUserFromTenant(tenant.id, userId)

      toast({
        title: '移除成功',
        description: '用户已从租户中移除'
      })
      await loadTenantUsers()
      await loadAvailableUsers()
      onSuccess?.()
    } catch (error) {
      console.error('移除用户失败:', error)
      toast({
        title: '移除失败',
        description: '移除用户失败: ' + (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理用户选择
  const handleUserSelect = (userId: number, checked: boolean) => {
    if (checked) {
      setSelectedUserIds(prev => [...prev, userId])
    } else {
      setSelectedUserIds(prev => prev.filter(id => id !== userId))
    }
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredUsers = availableUsers.filter(user => 
        user.username.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        user.realName?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchKeyword.toLowerCase())
      )
      setSelectedUserIds(filteredUsers.map(user => user.id))
    } else {
      setSelectedUserIds([])
    }
  }

  // 过滤可分配用户
  const filteredAvailableUsers = availableUsers.filter(user => 
    user.username.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    user.realName?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchKeyword.toLowerCase())
  )

  // 当对话框打开时加载数据
  useEffect(() => {
    if (open && tenant) {
      loadTenantUsers()
      if (activeTab === 'assign') {
        loadAvailableUsers()
      }
    }
  }, [open, tenant, activeTab])

  // 渲染用户头像
  const renderUserAvatar = (user: TenantUser | AvailableUser) => (
    <Avatar className="w-8 h-8">
      <AvatarImage src={`/api/user/${user.id}/avatar`} />
      <AvatarFallback>
        {user.realName?.charAt(0) || user.username.charAt(0).toUpperCase()}
      </AvatarFallback>
    </Avatar>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl min-w-3xl min-h-[60vh] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            租户用户管理
          </DialogTitle>
          <DialogDescription>
            管理租户 "{tenant?.tenantName}" 的用户分配和权限
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="current">当前用户</TabsTrigger>
            <TabsTrigger value="assign">分配用户</TabsTrigger>
          </TabsList>

          {/* 当前用户列表 */}
          <TabsContent value="current" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                共 {tenantUsers.length} 个用户
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={loadTenantUsers}
                disabled={loading}
              >
                刷新
              </Button>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead>用户名</TableHead>
                    <TableHead>真实姓名</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>分配时间</TableHead>
                    <TableHead className="w-20">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tenantUsers.map(user => (
                    <TableRow key={user.id}>
                      <TableCell>
                        {renderUserAvatar(user)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {user.username}
                      </TableCell>
                      <TableCell>{user.realName || '-'}</TableCell>
                      <TableCell>{user.email || '-'}</TableCell>
                      <TableCell>
                        <Badge variant={user.userRole === 1 ? 'default' : 'secondary'}>
                          {user.userRoleName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.status === 1 ? 'default' : 'destructive'}>
                          {user.statusName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(user.assignTime).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeUser(user.userId)}
                          disabled={loading}
                        >
                          <UserMinus className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* 分配用户 */}
          <TabsContent value="assign" className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="搜索用户名、姓名或邮箱..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                onClick={assignUsers}
                disabled={loading || selectedUserIds.length === 0}
              >
                <UserPlus className="w-4 h-4 mr-2" />
                分配用户 ({selectedUserIds.length})
              </Button>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={filteredAvailableUsers.length > 0 && 
                          filteredAvailableUsers.every(user => selectedUserIds.includes(user.id))}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-12"></TableHead>
                    <TableHead>用户名</TableHead>
                    <TableHead>真实姓名</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>手机号</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAvailableUsers.map(user => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedUserIds.includes(user.id)}
                          onCheckedChange={(checked) => handleUserSelect(user.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        {renderUserAvatar(user)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {user.username}
                      </TableCell>
                      <TableCell>{user.realName || '-'}</TableCell>
                      <TableCell>{user.email || '-'}</TableCell>
                      <TableCell>{user.phone || '-'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>

        <Separator />

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default TenantUserDialog
