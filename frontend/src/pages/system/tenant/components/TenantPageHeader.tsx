/**
 * 租户页面头部组件
 * 
 * 包含页面标题、描述和主要操作按钮
 */

import React from 'react'
import { Button } from '@/components/ui'
import { Plus, Download, RefreshCw } from 'lucide-react'

export interface TenantPageHeaderProps {
  onAdd: () => void
  onExport: () => void
  onRefresh: () => void
  loading?: boolean
  exportLoading?: boolean
}

/**
 * 租户页面头部组件
 */
const TenantPageHeader: React.FC<TenantPageHeaderProps> = ({
  onAdd,
  onExport,
  onRefresh,
  loading = false,
  exportLoading = false
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">租户管理</h1>
        <p className="text-muted-foreground">
          管理系统中的所有租户，包括租户信息、状态、配置等
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Button 
          variant="outline" 
          onClick={onRefresh}
          disabled={loading}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          刷新
        </Button>
        <Button 
          variant="outline" 
          onClick={onExport}
          disabled={exportLoading}
        >
          <Download className="w-4 h-4 mr-2" />
          导出
        </Button>
        <Button onClick={onAdd}>
          <Plus className="w-4 h-4 mr-2" />
          新增租户
        </Button>
      </div>
    </div>
  )
}

export default TenantPageHeader
