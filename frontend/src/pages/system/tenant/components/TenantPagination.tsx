/**
 * 租户分页组件
 * 
 * 提供分页导航和页面大小选择功能
 */

import React from 'react'
import { Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, RefreshCw } from 'lucide-react'

export interface TenantPaginationProps {
  current: number
  pageSize: number
  total: number
  onChange: (page: number, size?: number) => void
  onRefresh?: () => void
  loading?: boolean
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
  pageSizeOptions?: number[]
}

/**
 * 租户分页组件
 */
const TenantPagination: React.FC<TenantPaginationProps> = ({
  current,
  pageSize,
  total,
  onChange,
  onRefresh,
  loading = false,
  showSizeChanger = true,
  showQuickJumper = true,
  showTotal,
  pageSizeOptions = [10, 20, 50, 100]
}) => {
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (current - 1) * pageSize + 1
  const endIndex = Math.min(current * pageSize, total)

  // 处理页面变化
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== current) {
      onChange(page)
    }
  }

  // 处理页面大小变化
  const handlePageSizeChange = (size: string) => {
    const newSize = parseInt(size)
    if (newSize !== pageSize) {
      onChange(1, newSize) // 改变页面大小时回到第一页
    }
  }

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons: React.ReactNode[] = []
    const maxVisible = 5 // 最多显示5个页码按钮
    
    let startPage = Math.max(1, current - Math.floor(maxVisible / 2))
    const endPage = Math.min(totalPages, startPage + maxVisible - 1)
    
    // 调整起始页
    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1)
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <Button
          key={i}
          variant={i === current ? 'default' : 'outline'}
          size="sm"
          onClick={() => handlePageChange(i)}
          disabled={loading}
        >
          {i}
        </Button>
      )
    }

    return buttons
  }

  if (total === 0) {
    return null
  }

  return (
    <div className="flex items-center justify-between space-x-4">
      {/* 总数显示 */}
      <div className="flex items-center space-x-4">
        {showTotal && (
          <span className="text-sm text-muted-foreground">
            {showTotal(total, [startIndex, endIndex])}
          </span>
        )}
        
        {/* 刷新按钮 */}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
        )}
      </div>

      {/* 分页控件 */}
      <div className="flex items-center space-x-2">
        {/* 页面大小选择器 */}
        {showSizeChanger && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">每页</span>
            <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map(size => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">条</span>
          </div>
        )}

        {/* 分页按钮 */}
        <div className="flex items-center space-x-1">
          {/* 首页 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            disabled={current === 1 || loading}
          >
            <ChevronsLeft className="w-4 h-4" />
          </Button>

          {/* 上一页 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(current - 1)}
            disabled={current === 1 || loading}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          {/* 页码按钮 */}
          {renderPageButtons()}

          {/* 下一页 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(current + 1)}
            disabled={current === totalPages || loading}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>

          {/* 末页 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(totalPages)}
            disabled={current === totalPages || loading}
          >
            <ChevronsRight className="w-4 h-4" />
          </Button>
        </div>

        {/* 快速跳转 */}
        {showQuickJumper && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">跳至</span>
            <input
              type="number"
              min={1}
              max={totalPages}
              className="w-16 px-2 py-1 text-sm border rounded"
              onKeyUp={(e) => {
                if (e.key === 'Enter') {
                  const page = parseInt((e.target as HTMLInputElement).value)
                  if (page >= 1 && page <= totalPages) {
                    handlePageChange(page)
                    ;(e.target as HTMLInputElement).value = ''
                  }
                }
              }}
              disabled={loading}
            />
            <span className="text-sm text-muted-foreground">页</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default TenantPagination
