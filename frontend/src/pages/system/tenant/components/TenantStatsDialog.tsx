/**
 * 租户统计对话框组件
 * 
 * 显示租户的详细统计信息
 */

import React, { useEffect, useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Card,
  CardContent,
  // CardDescription,
  CardHeader,
  CardTitle,
  Skeleton,
  Badge
} from '@/components/ui'
import {
  Users,
  UserCheck,
  Building2,
  Clock,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import type { Tenant, TenantStats } from '@/types'
import { TenantService } from '@/services'

export interface TenantStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tenant: Tenant | null
}

/**
 * 租户统计对话框组件
 */
const TenantStatsDialog: React.FC<TenantStatsDialogProps> = ({
  open,
  onOpenChange,
  tenant
}) => {
  const [stats, setStats] = useState<TenantStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载统计数据
  const loadStats = async () => {
    if (!tenant) return

    try {
      setLoading(true)
      setError(null)
      console.log('🔄 开始加载租户统计:', tenant.id)
      const statsData = await TenantService.getTenantStats(tenant.id)
      console.log('✅ 租户统计加载成功:', statsData)
      setStats(statsData)
    } catch (error) {
      console.error('❌ 加载租户统计失败:', error)
      setError('加载统计数据失败: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 当对话框打开时加载数据
  useEffect(() => {
    if (open && tenant) {
      loadStats()
    } else {
      setStats(null)
      setError(null)
    }
  }, [open, tenant])

  // 格式化存储空间
  // const formatStorage = (mb: number) => {
  //   if (mb < 1024) return `${mb}MB`
  //   if (mb < 1024 * 1024) return `${(mb / 1024).toFixed(1)}GB`
  //   return `${(mb / (1024 * 1024)).toFixed(1)}TB`
  // }

  // 格式化日期时间
  const formatDateTime = (dateString?: string) => {
    if (!dateString) return '从未登录'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 计算用户使用率
  const getUserUsageRate = () => {
    if (!stats || !tenant?.maxUserCount) return 0
    return Math.round((stats.userCount / tenant.maxUserCount) * 100)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            租户统计 - {tenant?.tenantName}
          </DialogTitle>
          <DialogDescription>
            查看租户的详细统计信息和使用情况
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* 用户统计 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="space-y-2">
                  <div className="text-2xl font-bold">{stats?.userCount || 0}</div>
                  {tenant?.maxUserCount && (
                    <div className="flex items-center space-x-2">
                      <div className="text-xs text-muted-foreground">
                        限制: {tenant.maxUserCount}
                      </div>
                      <Badge variant={getUserUsageRate() > 80 ? 'destructive' : 'secondary'}>
                        {getUserUsageRate()}%
                      </Badge>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 角色统计 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">角色总数</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold">{stats?.roleCount || 0}</div>
              )}
            </CardContent>
          </Card>

          {/* 部门统计 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">部门总数</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="text-2xl font-bold">{stats?.deptCount || 0}</div>
              )}
            </CardContent>
          </Card>



          {/* 最后登录 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最后登录</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="text-sm">
                  {formatDateTime(stats?.lastLoginTime)}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 活跃度 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃度</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <div className="space-y-2">
                  <Badge variant={stats?.lastLoginTime ? 'default' : 'secondary'}>
                    {stats?.lastLoginTime ? '活跃' : '未活跃'}
                  </Badge>
                  {stats?.lastLoginTime && (
                    <div className="text-xs text-muted-foreground">
                      最近有用户登录
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 详细信息 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">租户详细信息</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">租户编码:</span>
                  <span className="font-medium">{tenant?.tenantCode}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">租户名称:</span>
                  <span className="font-medium">{tenant?.tenantName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">状态:</span>
                  <Badge variant={tenant?.status === 1 ? 'default' : 'secondary'}>
                    {tenant?.status === 1 ? '启用' : '禁用'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">创建时间:</span>
                  <span className="text-sm">
                    {tenant?.createTime ? formatDateTime(tenant.createTime) : '-'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">联系信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">联系人:</span>
                  <span className="font-medium">{tenant?.contactName || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">联系电话:</span>
                  <span className="font-medium">{tenant?.contactPhone || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">联系邮箱:</span>
                  <span className="font-medium">{tenant?.contactEmail || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">过期时间:</span>
                  <span className="text-sm">
                    {tenant?.expireTime ? formatDateTime(tenant.expireTime) : '永久'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {tenant?.remark && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">备注信息</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{tenant.remark}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default TenantStatsDialog
