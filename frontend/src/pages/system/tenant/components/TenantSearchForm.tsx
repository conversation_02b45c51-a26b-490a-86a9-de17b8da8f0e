/**
 * 租户搜索表单组件 - 重构版本
 * 
 * 使用新的搜索Hook，提供更好的搜索体验和状态管理
 */

import React from 'react'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Badge,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui'
import {
  Search,
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Filter,
  User,
  Building2
} from 'lucide-react'
import { TENANT_STATUS_OPTIONS } from '@/types/tenant'
import type { TenantQueryRequest } from '@/types/tenant'

export interface SearchFormState {
  keyword: string
  isAdvancedOpen: boolean
  advancedFilters: Partial<TenantQueryRequest>
}

export interface SearchStatus {
  hasKeyword: boolean
  hasAdvancedFilters: boolean
  hasAnyFilter: boolean
  isAdvancedOpen: boolean
}

export interface TenantSearchFormProps {
  formState: SearchFormState
  onKeywordChange: (keyword: string) => void
  onAdvancedFiltersChange: (filters: Partial<TenantQueryRequest>) => void
  onToggleAdvanced: () => void
  onSearch: () => void
  onReset: () => void
  onQuickFilter?: (status?: number) => void
  onKeyPress: (event: React.KeyboardEvent) => void
  searchStatus: SearchStatus
  searchSummary: string[]
  loading?: boolean
}

/**
 * 租户搜索表单组件
 */
const TenantSearchForm: React.FC<TenantSearchFormProps> = ({
  formState,
  onKeywordChange,
  onAdvancedFiltersChange,
  onToggleAdvanced,
  onSearch,
  onReset,
  onQuickFilter,
  onKeyPress,
  searchStatus,
  searchSummary,
  loading = false
}) => {
  // 处理高级筛选字段变更
  const handleAdvancedFilterChange = (field: keyof TenantQueryRequest, value: any) => {
    onAdvancedFiltersChange({ [field]: value || undefined })
  }

  // 清除单个筛选条件
  // const clearFilter = (field: keyof TenantQueryRequest) => {
  //   onAdvancedFiltersChange({ [field]: undefined })
  // }

  return (
    <div className="space-y-4">
      {/* 基础搜索区域 */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="搜索租户编码、租户名称..."
            value={formState.keyword}
            onChange={(e) => onKeywordChange(e.target.value)}
            onKeyUp={onKeyPress}
            className="pl-10"
            disabled={loading}
          />
        </div>
        
        {/* 快速筛选按钮 */}
        {onQuickFilter && (
          <div className="flex items-center space-x-2">
            <Button
              variant={formState.advancedFilters.status === undefined ? 'outline' : 'secondary'}
              size="sm"
              onClick={() => onQuickFilter(undefined)}
            >
              全部
            </Button>
            <Button
              variant={formState.advancedFilters.status === 1 ? 'default' : 'outline'}
              size="sm"
              onClick={() => onQuickFilter(1)}
            >
              启用
            </Button>
            <Button
              variant={formState.advancedFilters.status === 0 ? 'default' : 'outline'}
              size="sm"
              onClick={() => onQuickFilter(0)}
            >
              禁用
            </Button>
          </div>
        )}
        
        <Button 
          onClick={onSearch}
          disabled={loading}
          className="shrink-0"
        >
          <Search className="w-4 h-4 mr-2" />
          搜索
        </Button>
        
        <Button 
          variant="outline" 
          onClick={onReset}
          disabled={loading}
          className="shrink-0"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          重置
        </Button>
        
        {/* 高级搜索切换 */}
        <Collapsible open={searchStatus.isAdvancedOpen} onOpenChange={onToggleAdvanced}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              高级筛选
              {searchStatus.isAdvancedOpen ? (
                <ChevronUp className="w-4 h-4 ml-2" />
              ) : (
                <ChevronDown className="w-4 h-4 ml-2" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
              {/* 租户编码 */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center">
                  <Building2 className="w-4 h-4 mr-1" />
                  租户编码
                </label>
                <Input
                  placeholder="请输入租户编码"
                  value={formState.advancedFilters.tenantCode || ''}
                  onChange={(e) => handleAdvancedFilterChange('tenantCode', e.target.value)}
                  disabled={loading}
                />
              </div>
              
              {/* 租户名称 */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center">
                  <Building2 className="w-4 h-4 mr-1" />
                  租户名称
                </label>
                <Input
                  placeholder="请输入租户名称"
                  value={formState.advancedFilters.tenantName || ''}
                  onChange={(e) => handleAdvancedFilterChange('tenantName', e.target.value)}
                  disabled={loading}
                />
              </div>
              
              {/* 联系人 */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  联系人
                </label>
                <Input
                  placeholder="请输入联系人"
                  value={formState.advancedFilters.contactName || ''}
                  onChange={(e) => handleAdvancedFilterChange('contactName', e.target.value)}
                  disabled={loading}
                />
              </div>
              
              {/* 状态 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">状态</label>
                <Select
                  value={formState.advancedFilters.status?.toString() || 'all'}
                  onValueChange={(value) => handleAdvancedFilterChange('status', value === 'all' ? undefined : parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {TENANT_STATUS_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* 创建时间范围 */}
              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  创建时间
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="date"
                    value={formState.advancedFilters.startTime?.split('T')[0] || ''}
                    onChange={(e) => handleAdvancedFilterChange('startTime', e.target.value ? `${e.target.value}T00:00:00` : undefined)}
                    disabled={loading}
                  />
                  <span className="text-muted-foreground">至</span>
                  <Input
                    type="date"
                    value={formState.advancedFilters.endTime?.split('T')[0] || ''}
                    onChange={(e) => handleAdvancedFilterChange('endTime', e.target.value ? `${e.target.value}T23:59:59` : undefined)}
                    disabled={loading}
                  />
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      {/* 搜索摘要 */}
      {searchSummary.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">当前筛选:</span>
          {searchSummary.map((summary, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {summary}
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}

export default TenantSearchForm
