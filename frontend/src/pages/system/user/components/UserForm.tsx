/**
 * 用户表单组件
 * 
 * 用于新增和编辑用户信息
 * 支持表单验证、角色分配等功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Checkbox
} from '@/components/ui'
import { Loader2, Eye, EyeOff } from 'lucide-react'
import { useForm } from '@/hooks/useForm'
import type { User, UserCreateRequest, UserUpdateRequest } from '@/types/user'
import { UserStatus, Gender, GENDER_OPTIONS } from '@/types/user'
import { UserService } from '@/services'
import { RoleService } from '@/services/role'
import { DeptService } from '@/services/dept'
import type { Role, Dept } from '@/types'

export interface UserFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user?: User | null
  onSuccess: () => void
}

interface UserFormData {
  username: string
  password: string
  nickname: string
  realName: string
  email: string
  phone: string
  gender: Gender
  birthday: string
  status: UserStatus
  deptId: number
  remark: string
}

/**
 * 用户表单组件
 */
const UserForm: React.FC<UserFormProps> = ({
  open,
  onOpenChange,
  user,
  onSuccess
}) => {
  const isEdit = !!user
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  
  // 角色相关状态
  const [roles, setRoles] = useState<Role[]>([])
  const [rolesLoading, setRolesLoading] = useState(false)
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([])

  // 部门相关状态
  const [depts, setDepts] = useState<Dept[]>([])
  const [deptsLoading, setDeptsLoading] = useState(false)

  // 表单配置
  const getFormConfig = (isEdit: boolean) => ({
    defaultValues: {
      username: '',
      password: '',
      nickname: '',
      realName: '',
      email: '',
      phone: '',
      gender: Gender.UNKNOWN,
      birthday: '',
      status: UserStatus.ENABLED,
      deptId: 0,
      remark: ''
    },
    fields: {
      username: {
        rules: {
          required: '用户名不能为空',
          minLength: { value: 2, message: '用户名至少2个字符' },
          maxLength: { value: 50, message: '用户名不能超过50个字符' },
          pattern: { value: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
        }
      },
      password: {
        rules: isEdit ? {} : {
          required: '密码不能为空',
          minLength: { value: 6, message: '密码至少6个字符' },
          maxLength: { value: 20, message: '密码不能超过20个字符' }
        }
      },
      nickname: {
        rules: {
          maxLength: { value: 50, message: '昵称不能超过50个字符' }
        }
      },
      realName: {
        rules: {
          maxLength: { value: 50, message: '真实姓名不能超过50个字符' }
        }
      },
      email: {
        rules: {
          pattern: { value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入有效的邮箱地址' }
        }
      },
      phone: {
        rules: {
          pattern: { value: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
        }
      },
      remark: {
        rules: {
          maxLength: { value: 500, message: '备注不能超过500个字符' }
        }
      }
    }
  })

  // 初始化表单
  const form = useForm<UserFormData>(getFormConfig(isEdit))

  // 加载角色列表
  const loadRoles = async () => {
    try {
      setRolesLoading(true)
      console.log('🔄 开始加载角色列表...')
      const result = await RoleService.pageRoles({
        pageNum: 1,
        pageSize: 100 // 修复：将pageSize从1000改为100，符合后端验证规则
      })
      console.log('✅ 角色列表加载成功，数量:', result.records?.length || 0)
      setRoles(result.records || [])
    } catch (error) {
      console.error('❌ 加载角色列表失败:', error)
      setSubmitError('加载角色列表失败: ' + (error as Error).message)
    } finally {
      setRolesLoading(false)
    }
  }

  // 加载部门树
  const loadDepts = async () => {
    try {
      setDeptsLoading(true)
      console.log('🔄 开始加载部门树...')
      const result = await DeptService.getDeptTree()
      console.log('✅ 部门树加载成功，数量:', result?.length || 0)
      setDepts(result || [])
    } catch (error) {
      console.error('❌ 加载部门树失败:', error)
      setSubmitError('加载部门树失败: ' + (error as Error).message)
    } finally {
      setDeptsLoading(false)
    }
  }

  // 加载用户角色
  const loadUserRoles = async (userId: number) => {
    try {
      console.log('🔄 开始加载用户角色:', userId)
      const roleIds = await UserService.getUserRoles(userId)
      console.log('✅ 用户角色加载成功:', roleIds)
      setSelectedRoleIds(roleIds)
    } catch (error) {
      console.error('❌ 加载用户角色失败:', error)
    }
  }

  // 加载用户编辑数据
  const loadUserForEdit = async (userId: number) => {
    try {
      console.log('🔄 加载用户编辑数据:', userId)
      const userData = await UserService.getUserForEdit(userId)
      console.log('✅ 获取用户编辑数据成功:', userData)

      // 填充表单数据（使用原始数据）
      form.reset({
        username: userData.username,
        password: '', // 编辑时不显示密码
        nickname: userData.nickname || '',
        realName: userData.realName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        gender: (userData.gender as Gender) || Gender.UNKNOWN,
        birthday: userData.birthday || '',
        status: userData.status as UserStatus,
        deptId: userData.deptId || 0,
        remark: userData.remark || ''
      })
      loadUserRoles(userData.id)
    } catch (error) {
      console.error('❌ 加载用户编辑数据失败:', error)
      setSubmitError('加载用户数据失败，请重试')
    }
  }

  // 初始化表单数据
  useEffect(() => {
    if (open) {
      setSubmitError(null)
      loadRoles()
      loadDepts()

      if (isEdit && user) {
        // 编辑模式：获取原始数据
        loadUserForEdit(user.id)
      } else {
        // 新增模式：重置表单
        form.reset()
        setSelectedRoleIds([])
      }
    }
  }, [open, isEdit, user?.id])

  // 提交表单
  const handleSubmit = async () => {
    console.log('🚀 开始提交表单')
    
    // 手动获取表单数据
    const formData: UserFormData = {
      username: form.watch('username') || '',
      password: form.watch('password') || '',
      nickname: form.watch('nickname') || '',
      realName: form.watch('realName') || '',
      email: form.watch('email') || '',
      phone: form.watch('phone') || '',
      gender: form.watch('gender') || Gender.UNKNOWN,
      birthday: form.watch('birthday') || '',
      status: form.watch('status') || UserStatus.ENABLED,
      deptId: form.watch('deptId') || 0,
      remark: form.watch('remark') || ''
    }
    
    console.log('📋 表单数据:', formData)
    console.log('🔐 选中角色:', selectedRoleIds)

    // 验证必填字段
    const errors: Partial<Record<keyof UserFormData, string>> = {}
    if (!formData.username?.trim()) {
      errors.username = '用户名不能为空'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username = '用户名只能包含字母、数字和下划线'
    }

    if (!isEdit && !formData.password?.trim()) {
      errors.password = '密码不能为空'
    }

    if (!formData.deptId || formData.deptId === 0) {
      errors.deptId = '请选择部门'
    }

    // 如果有验证错误，显示错误并返回
    if (Object.keys(errors).length > 0) {
      console.log('❌ 表单验证失败:', errors)
      Object.entries(errors).forEach(([key, message]) => {
        if (message) {
          form.setError(key as keyof UserFormData, message)
        }
      })
      return
    }

    try {
      setLoading(true)
      setSubmitError(null)

      if (isEdit && user) {
        // 编辑用户
        const updateRequest: UserUpdateRequest = {
          id: user.id,
          nickname: formData.nickname || undefined,
          realName: formData.realName || undefined,
          email: formData.email || undefined,
          phone: formData.phone || undefined,
          gender: formData.gender,
          birthday: formData.birthday || undefined,
          status: formData.status,
          deptId: formData.deptId,
          remark: formData.remark || undefined,
          roleIds: selectedRoleIds
        }
        console.log('🔄 更新用户请求:', updateRequest)
        await UserService.updateUser(updateRequest)
        console.log('✅ 用户更新成功')
      } else {
        // 新增用户
        const createRequest: UserCreateRequest = {
          username: formData.username,
          password: formData.password,
          nickname: formData.nickname || undefined,
          realName: formData.realName || undefined,
          email: formData.email || undefined,
          phone: formData.phone || undefined,
          gender: formData.gender,
          birthday: formData.birthday || undefined,
          status: formData.status,
          deptId: formData.deptId,
          remark: formData.remark || undefined,
          roleIds: selectedRoleIds
        }
        console.log('🔄 创建用户请求:', createRequest)
        await UserService.createUser(createRequest)
        console.log('✅ 用户创建成功')
      }

      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('❌ 用户操作失败:', error)
      setSubmitError(error.message || '操作失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理角色选择
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoleIds([...selectedRoleIds, roleId])
    } else {
      setSelectedRoleIds(selectedRoleIds.filter(id => id !== roleId))
    }
  }

  // 递归渲染部门树选项
  const renderDeptOptions = (depts: Dept[], level: number = 0): React.ReactNode[] => {
    return depts.map(dept => {
      const prefix = '　'.repeat(level) // 使用全角空格缩进
      const options: React.ReactNode[] = [
        <SelectItem key={dept.id} value={dept.id.toString()}>
          {prefix}{dept.deptName}
        </SelectItem>
      ]

      // 如果有子部门，递归渲染
      if (dept.children && dept.children.length > 0) {
        options.push(...renderDeptOptions(dept.children, level + 1))
      }

      return options
    }).flat()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? '编辑用户' : '新增用户'}</DialogTitle>
          <DialogDescription>
            {isEdit ? '修改用户信息和角色分配' : '创建新用户并分配角色'}
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {submitError}
          </div>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 用户名 */}
            <FormField>
              <FormItem>
                <FormLabel>用户名 *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入用户名"
                    disabled={isEdit} // 编辑时不允许修改用户名
                    value={form.watch('username') || ''}
                    onChange={(e) => form.setValue('username', e.target.value)}
                  />
                </FormControl>
                <FormDescription>
                  用户的唯一标识，只能包含字母、数字和下划线
                </FormDescription>
                <FormMessage>{form.formState.errors.username}</FormMessage>
              </FormItem>
            </FormField>

            {/* 密码 */}
            {!isEdit && (
              <FormField>
                <FormItem>
                  <FormLabel>密码 *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="请输入密码"
                        value={form.watch('password') || ''}
                        onChange={(e) => form.setValue('password', e.target.value)}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage>{form.formState.errors.password}</FormMessage>
                </FormItem>
              </FormField>
            )}

            {/* 昵称 */}
            <FormField>
              <FormItem>
                <FormLabel>昵称</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入昵称"
                    value={form.watch('nickname') || ''}
                    onChange={(e) => form.setValue('nickname', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.nickname}</FormMessage>
              </FormItem>
            </FormField>

            {/* 真实姓名 */}
            <FormField>
              <FormItem>
                <FormLabel>真实姓名</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入真实姓名"
                    value={form.watch('realName') || ''}
                    onChange={(e) => form.setValue('realName', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.realName}</FormMessage>
              </FormItem>
            </FormField>

            {/* 邮箱 */}
            <FormField>
              <FormItem>
                <FormLabel>邮箱</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="请输入邮箱地址"
                    value={form.watch('email') || ''}
                    onChange={(e) => form.setValue('email', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.email}</FormMessage>
              </FormItem>
            </FormField>

            {/* 手机号 */}
            <FormField>
              <FormItem>
                <FormLabel>手机号</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入手机号"
                    value={form.watch('phone') || ''}
                    onChange={(e) => form.setValue('phone', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.phone}</FormMessage>
              </FormItem>
            </FormField>

            {/* 性别 */}
            <FormField>
              <FormItem>
                <FormLabel>性别</FormLabel>
                <FormControl>
                  <Select
                    value={form.watch('gender')?.toString() || Gender.UNKNOWN.toString()}
                    onValueChange={(value) => form.setValue('gender', parseInt(value) as Gender)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      {GENDER_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage>{form.formState.errors.gender}</FormMessage>
              </FormItem>
            </FormField>

            {/* 所属部门 */}
            <FormField>
              <FormItem>
                <FormLabel>所属部门 *</FormLabel>
                <FormControl>
                  <Select
                    value={form.watch('deptId')?.toString() || '0'}
                    onValueChange={(value) => form.setValue('deptId', parseInt(value))}
                    disabled={deptsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择部门" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[300px] overflow-y-auto">
                      {deptsLoading ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            加载部门树...
                          </div>
                        </SelectItem>
                      ) : (
                        <>
                          <SelectItem value="0" disabled>
                            请选择部门
                          </SelectItem>
                          {renderDeptOptions(depts)}
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  用户所属的部门，用于权限控制和数据隔离
                </FormDescription>
                <FormMessage>{form.formState.errors.deptId}</FormMessage>
              </FormItem>
            </FormField>

            {/* 生日 */}
            <FormField>
              <FormItem>
                <FormLabel>生日</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    value={form.watch('birthday') || ''}
                    onChange={(e) => form.setValue('birthday', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.birthday}</FormMessage>
              </FormItem>
            </FormField>

            {/* 状态 */}
            <FormField>
              <FormItem className="flex flex-row items-center justify-between rounded-lg p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">用户状态</FormLabel>
                  <FormDescription>
                    启用后用户可以正常登录系统
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={form.watch('status') === UserStatus.ENABLED}
                    onCheckedChange={(checked) => {
                      const newStatus = checked ? UserStatus.ENABLED : UserStatus.DISABLED
                      console.log('🔄 状态切换:', checked, '→', newStatus)
                      form.setValue('status', newStatus)
                    }}
                  />
                </FormControl>
              </FormItem>
            </FormField>
          </div>

          {/* 备注 */}
          <FormField>
            <FormItem>
              <FormLabel>备注</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请输入备注信息"
                  className="resize-none"
                  value={form.watch('remark') || ''}
                  onChange={(e) => form.setValue('remark', e.target.value)}
                />
              </FormControl>
              <FormMessage>{form.formState.errors.remark}</FormMessage>
            </FormItem>
          </FormField>

          {/* 角色分配 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">角色分配</h3>
              {rolesLoading && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  加载角色列表...
                </div>
              )}
            </div>

            {!rolesLoading && roles.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border rounded-md p-4">
                {roles.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`role-${role.id}`}
                      checked={selectedRoleIds.includes(role.id)}
                      onCheckedChange={(checked) => handleRoleChange(role.id, checked as boolean)}
                    />
                    <label
                      htmlFor={`role-${role.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      {role.roleName}
                    </label>
                  </div>
                ))}
              </div>
            )}

            {!rolesLoading && roles.length === 0 && (
              <div className="text-center text-muted-foreground py-8 border rounded-md">
                <div className="text-lg mb-2">⚠️</div>
                <div>暂无可分配的角色</div>
                <div className="text-xs mt-1">请先创建角色</div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {isEdit ? '更新' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default UserForm
