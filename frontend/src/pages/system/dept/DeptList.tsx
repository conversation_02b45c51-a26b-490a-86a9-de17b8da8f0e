/**
 * 部门管理页面
 *
 * 提供部门的增删改查、树形展示、状态管理等功能
 * 统一重构版本 - 完整的权限控制实现
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button, ConfirmDialog } from '@/components/ui'
import { Plus, Search, RefreshCw, Building2 } from 'lucide-react'
import { usePageTitle } from '@/router'
import { PagePermissionWrapper, PermissionToolbar } from '@/components/auth/PermissionWrapper'
import DeptSearchForm from './components/DeptSearchForm'
import DeptTreeTable from './components/DeptTreeTable'
import DeptForm from './components/DeptForm'
import DeptUserDialog from './components/DeptUserDialog'
import { DeptService } from '@/services/dept.ts'
import type { Dept, DeptQueryRequest } from '@/types/dept.ts'
import type { PageResult } from '@/types'

/**
 * 部门管理页面
 */
const DeptList: React.FC = () => {
  // 设置页面标题
  usePageTitle('部门管理')

  // 数据状态
  const [depts, setDepts] = useState<Dept[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedIds, setSelectedIds] = useState<number[]>([])

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<DeptQueryRequest>({
    pageNum: 1,
    pageSize: 10
  })

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 表单相关状态
  const [formOpen, setFormOpen] = useState(false)
  const [editingDept, setEditingDept] = useState<Dept | null>(null)
  const [parentDept, setParentDept] = useState<Dept | null>(null)

  // 用户管理相关状态
  const [userDialogOpen, setUserDialogOpen] = useState(false)
  const [managingDept, setManagingDept] = useState<Dept | null>(null)

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => void
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => {}
  })

  // 排序状态
  const [sortConfig, setSortConfig] = useState<{
    field: string
    order: 'asc' | 'desc'
  }>()

  // 加载部门列表
  const loadDepts = useCallback(async () => {
    try {
      setLoading(true)

      const result: PageResult<Dept> = await DeptService.pageDepts(searchParams)

      setDepts(result.records || [])
      
      // 更新分页信息
      setPagination({
        current: searchParams.pageNum || 1,
        pageSize: searchParams.pageSize || 10,
        total: result.total || 0
      })
    } catch (error) {
      console.error('❌ 加载部门列表失败:', error)
      setDepts([])
      setPagination({ current: 1, pageSize: 10, total: 0 })
    } finally {
      setLoading(false)
    }
  }, [searchParams])

  // 初始化加载数据
  useEffect(() => {
    loadDepts()
  }, [])

  // 监听搜索参数变化，重新加载数据
  useEffect(() => {
    loadDepts()
  }, [searchParams, loadDepts])

  // 处理搜索参数变更
  const handleSearchChange = (params: DeptQueryRequest) => {
    setSearchParams(params)
  }

  // 处理搜索
  const handleSearch = () => {
    loadDepts()
  }

  // 处理重置
  const handleReset = () => {
    const resetParams: DeptQueryRequest = {
      pageNum: 1,
      pageSize: searchParams.pageSize || 10
    }
    setSearchParams(resetParams)
  }

  // 处理分页变更
  const handlePaginationChange = (page: number, pageSize: number) => {
    const newParams = {
      ...searchParams,
      pageNum: page,
      pageSize: pageSize
    }
    setSearchParams(newParams)
  }

  // 处理排序
  const handleSort = (field: string) => {
    let order: 'asc' | 'desc' = 'asc'
    
    if (sortConfig && sortConfig.field === field) {
      order = sortConfig.order === 'asc' ? 'desc' : 'asc'
    }
    
    setSortConfig({ field, order })
    
    // 更新搜索参数
    const newParams = {
      ...searchParams,
      orderBy: field,
      orderDirection: order.toUpperCase() as 'ASC' | 'DESC',
      pageNum: 1 // 排序时重置到第一页
    }
    setSearchParams(newParams)
  }

  // 处理新增部门
  const handleAdd = () => {
    setEditingDept(null)
    setParentDept(null)
    setFormOpen(true)
  }

  // 处理编辑部门
  const handleEdit = (dept: Dept) => {
    setEditingDept(dept)
    setParentDept(null)
    setFormOpen(true)
  }

  // 处理添加子部门
  const handleAddChild = (parentDept: Dept) => {
    setEditingDept(null)
    setParentDept(parentDept)
    setFormOpen(true)
  }

  // 处理部门删除
  const handleDelete = (dept: Dept) => {
    console.log('🗑️ 删除部门:', dept)
    
    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: '删除部门',
      description: `确定要删除部门"${dept.deptName}"吗？此操作不可恢复，该部门下的所有子部门和用户关联将被删除。`,
      onConfirm: async () => {
        try {
          await DeptService.deleteDept(dept.id)
          console.log('✅ 部门删除成功')
          
          // 重新加载数据
          loadDepts()
          
          // 清空选择
          setSelectedIds([])
        } catch (error) {
          console.error('❌ 删除部门失败:', error)
          
          // 显示错误确认对话框
          setConfirmDialog({
            open: true,
            title: '删除部门失败',
            description: `删除部门"${dept.deptName}"失败：\n\n${(error as Error).message}`,
            onConfirm: () => {}
          })
        }
      }
    })
  }

  // 处理状态切换
  const handleToggleStatus = (dept: Dept) => {
    console.log('🔄 切换状态:', dept)
    
    const newStatus = dept.status === 1 ? 0 : 1
    const statusText = newStatus === 1 ? '启用' : '禁用'
    
    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: `${statusText}部门`,
      description: `确定要${statusText}部门"${dept.deptName}"吗？${statusText === '禁用' ? '禁用后该部门下的用户将无法正常使用相关功能。' : ''}`,
      onConfirm: async () => {
        try {
          if (newStatus === 1) {
            await DeptService.enableDept(dept.id)
          } else {
            await DeptService.disableDept(dept.id)
          }
          console.log(`✅ 部门${statusText}成功`)
          
          // 重新加载数据
          loadDepts()
        } catch (error) {
          console.error(`❌ ${statusText}部门失败:`, error)
          
          // 显示错误确认对话框
          setConfirmDialog({
            open: true,
            title: `${statusText}部门失败`,
            description: `${statusText}部门"${dept.deptName}"失败：\n\n${(error as Error).message}`,
            onConfirm: () => {}
          })
        }
      }
    })
  }

  // 处理用户管理
  const handleManageUsers = (dept: Dept) => {
    console.log('👥 管理用户:', dept)
    setManagingDept(dept)
    setUserDialogOpen(true)
  }

  // 处理表单成功
  const handleFormSuccess = () => {
    console.log('✅ 表单操作成功')
    loadDepts()
    setSelectedIds([])
  }

  // 处理刷新
  const handleRefresh = () => {
    console.log('🔄 刷新数据')
    loadDepts()
  }

  // 处理导入
  // const handleImport = () => {
  //   console.log('📥 导入部门')
  //   // TODO: 实现部门导入功能
  // }

  // 处理导出
  // const handleExport = () => {
  //   console.log('📤 导出部门')
  //   // TODO: 实现部门导出功能
  // }

  return (
    <PagePermissionWrapper module="dept">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">部门管理</h1>
          <p className="text-muted-foreground">
            管理组织架构，包括部门信息、层级关系、状态控制等
          </p>
        </div>

        {/* 权限控制的工具栏 */}
        <PermissionToolbar
          module="dept"
          searchComponent={
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="w-5 h-5 mr-2" />
                  搜索筛选
                </CardTitle>
              </CardHeader>
              <CardContent>
                <DeptSearchForm
                  searchParams={searchParams}
                  onSearchChange={handleSearchChange}
                  onSearch={handleSearch}
                  onReset={handleReset}
                  loading={loading}
                />
              </CardContent>
            </Card>
          }
          primaryActions={[
            {
              action: 'add',
              config: {
                text: '新增部门',
                icon: Plus,
                onClick: handleAdd,
                disabled: loading,
              }
            }
          ]}
          customActions={
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          }
        />

      {/* 部门表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Building2 className="w-5 h-5 mr-2" />
              部门列表
            </div>
            <div className="text-sm text-muted-foreground">
              共 {pagination.total} 条记录
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DeptTreeTable
            data={depts}
            loading={loading}
            selectedIds={selectedIds}
            onSelectionChange={setSelectedIds}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onAddChild={handleAddChild}
            onToggleStatus={handleToggleStatus}
            onManageUsers={handleManageUsers}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: handlePaginationChange
            }}
            sortConfig={sortConfig}
            onSort={handleSort}
          />
        </CardContent>
      </Card>

      {/* 部门表单对话框 */}
      <DeptForm
        open={formOpen}
        onOpenChange={setFormOpen}
        dept={editingDept}
        parentDept={parentDept}
        onSuccess={handleFormSuccess}
      />

      {/* 部门用户管理对话框 */}
      <DeptUserDialog
        open={userDialogOpen}
        onOpenChange={setUserDialogOpen}
        dept={managingDept}
        onSuccess={handleFormSuccess}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant="destructive"
        confirmText="确认"
        cancelText="取消"
        onConfirm={confirmDialog.onConfirm}
      />
      </div>
    </PagePermissionWrapper>
  )
}

export default DeptList
