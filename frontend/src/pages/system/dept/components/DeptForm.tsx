/**
 * 部门表单组件
 * 
 * 用于新增和编辑部门信息
 * 支持表单验证、父部门选择等功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from '@/components/ui'
import { Loader2 } from 'lucide-react'
import { useForm } from '@/hooks/useForm.ts'
import type { Dept, DeptCreateRequest, DeptUpdateRequest } from '@/types/dept.ts'
import { DeptStatus, buildDeptSelectOptions } from '@/types/dept.ts'
import { DeptService } from '@/services/dept.ts'

export interface DeptFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  dept?: Dept | null
  parentDept?: Dept | null // 指定父部门（添加子部门时使用）
  onSuccess: () => void
}

interface DeptFormData {
  parentId: number
  deptCode: string
  deptName: string
  leaderId: number | undefined
  phone: string
  email: string
  status: DeptStatus
  sortOrder: number
  remark: string
}

/**
 * 部门表单组件
 */
const DeptForm: React.FC<DeptFormProps> = ({
  open,
  onOpenChange,
  dept,
  parentDept,
  onSuccess
}) => {
  const isEdit = !!dept
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  
  // 部门选择相关状态
  const [, setAllDepts] = useState<Dept[]>([])
  const [deptsLoading, setDeptsLoading] = useState(false)
  const [parentDeptOptions, setParentDeptOptions] = useState<any[]>([])

  // 表单配置
  const getFormConfig = () => ({
    defaultValues: {
      parentId: parentDept?.id || 0,
      deptCode: '',
      deptName: '',
      leaderId: undefined,
      phone: '',
      email: '',
      status: DeptStatus.ENABLED,
      sortOrder: 1,
      remark: ''
    },
    fields: {
      deptCode: {
        rules: {
          required: '部门编码不能为空',
          minLength: { value: 2, message: '部门编码至少2个字符' },
          maxLength: { value: 50, message: '部门编码不能超过50个字符' },
          pattern: { value: /^[A-Z0-9_]+$/, message: '部门编码只能包含大写字母、数字和下划线' }
        }
      },
      deptName: {
        rules: {
          required: '部门名称不能为空',
          minLength: { value: 2, message: '部门名称至少2个字符' },
          maxLength: { value: 50, message: '部门名称不能超过50个字符' }
        }
      },
      phone: {
        rules: {
          pattern: { value: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
        }
      },
      email: {
        rules: {
          pattern: { value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入有效的邮箱地址' }
        }
      },
      sortOrder: {
        rules: {
          required: '排序不能为空',
          min: { value: 1, message: '排序必须大于0' },
          max: { value: 9999, message: '排序不能超过9999' }
        }
      },
      remark: {
        rules: {
          maxLength: { value: 500, message: '备注不能超过500个字符' }
        }
      }
    }
  })

  // 初始化表单
  const form = useForm<DeptFormData>(getFormConfig())

  // 加载所有部门（用于父部门选择）
  const loadAllDepts = async () => {
    try {
      setDeptsLoading(true)
      const result = await DeptService.getAllEnabledDepts()
      setAllDepts(result)
      
      // 构建父部门选择选项（排除当前编辑的部门）
      const options = buildDeptSelectOptions(result, dept?.id)
      setParentDeptOptions([
        { value: 0, label: '根部门', disabled: false },
        ...options
      ])
    } catch (error) {
      console.error('❌ 加载部门列表失败:', error)
      setSubmitError('加载部门列表失败: ' + (error as Error).message)
    } finally {
      setDeptsLoading(false)
    }
  }

  // 初始化表单数据
  useEffect(() => {
    if (open) {
      setSubmitError(null)
      loadAllDepts()
      
      if (isEdit && dept) {
        // 编辑模式：填充表单数据
        form.reset({
          parentId: dept.parentId,
          deptCode: dept.deptCode,
          deptName: dept.deptName,
          leaderId: dept.leaderId,
          phone: dept.phone || '',
          email: dept.email || '',
          status: dept.status as DeptStatus,
          sortOrder: dept.sortOrder,
          remark: dept.remark || ''
        })
      } else {
        // 新增模式：重置表单
        form.reset({
          parentId: parentDept?.id || 0,
          deptCode: '',
          deptName: '',
          leaderId: undefined,
          phone: '',
          email: '',
          status: DeptStatus.ENABLED,
          sortOrder: 1,
          remark: ''
        })
      }
    }
  }, [open, isEdit, dept?.id, parentDept?.id])

  // 提交表单
  const handleSubmit = async () => {

    // 手动获取表单数据
    const formData: DeptFormData = {
      parentId: form.watch('parentId') || 0,
      deptCode: form.watch('deptCode') || '',
      deptName: form.watch('deptName') || '',
      leaderId: form.watch('leaderId'),
      phone: form.watch('phone') || '',
      email: form.watch('email') || '',
      status: form.watch('status') || DeptStatus.ENABLED,
      sortOrder: form.watch('sortOrder') || 1,
      remark: form.watch('remark') || ''
    }
    

    // 验证必填字段
    const errors: any = {}
    if (!formData.deptCode?.trim()) {
      errors.deptCode = '部门编码不能为空'
    } else if (!/^[A-Z0-9_]+$/.test(formData.deptCode)) {
      errors.deptCode = '部门编码只能包含大写字母、数字和下划线'
    }

    if (!formData.deptName?.trim()) {
      errors.deptName = '部门名称不能为空'
    }

    if (!formData.sortOrder || formData.sortOrder < 1) {
      errors.sortOrder = '排序必须大于0'
    }

    // 如果有验证错误，显示错误并返回
    if (Object.keys(errors).length > 0) {
      Object.keys(errors).forEach(key => {
        form.setError(key as keyof DeptFormData, errors[key])
      })
      return
    }

    try {
      setLoading(true)
      setSubmitError(null)

      if (isEdit && dept) {
        // 编辑部门
        const updateRequest: DeptUpdateRequest = {
          id: dept.id,
          parentId: formData.parentId,
          deptName: formData.deptName,
          leaderId: formData.leaderId,
          phone: formData.phone || undefined,
          email: formData.email || undefined,
          status: formData.status,
          sortOrder: formData.sortOrder,
          remark: formData.remark || undefined
        }
        await DeptService.updateDept(updateRequest)
      } else {
        // 新增部门
        const createRequest: DeptCreateRequest = {
          parentId: formData.parentId,
          deptCode: formData.deptCode,
          deptName: formData.deptName,
          leaderId: formData.leaderId,
          phone: formData.phone || undefined,
          email: formData.email || undefined,
          status: formData.status,
          sortOrder: formData.sortOrder,
          remark: formData.remark || undefined
        }
        await DeptService.createDept(createRequest)
      }

      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('❌ 部门操作失败:', error)
      setSubmitError(error.message || '操作失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? '编辑部门' : parentDept ? `添加子部门 - ${parentDept.deptName}` : '新增部门'}
          </DialogTitle>
          <DialogDescription>
            {isEdit ? '修改部门信息和配置' : '创建新部门并设置基本信息'}
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {submitError}
          </div>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 父部门 */}
            <FormField>
              <FormItem>
                <FormLabel>父部门</FormLabel>
                <FormControl>
                  <Select
                    value={form.watch('parentId')?.toString() || '0'}
                    onValueChange={(value) => form.setValue('parentId', parseInt(value))}
                    disabled={deptsLoading || !!parentDept}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={parentDept ? parentDept.deptName : "选择父部门"}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {deptsLoading ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            加载中...
                          </div>
                        </SelectItem>
                      ) : (
                        parentDeptOptions.map(option => (
                          <SelectItem 
                            key={option.value} 
                            value={option.value.toString()}
                            disabled={option.disabled}
                          >
                            {option.label}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  {parentDept
                    ? `子部门时父部门已固定为：${parentDept.deptName}`
                    : '选择部门的上级部门，根部门的父部门为空'
                  }
                </FormDescription>
                <FormMessage>{form.formState.errors.parentId}</FormMessage>
              </FormItem>
            </FormField>

            {/* 部门编码 */}
            <FormField>
              <FormItem>
                <FormLabel>部门编码 *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入部门编码"
                    disabled={isEdit} // 编辑时不允许修改编码
                    value={form.watch('deptCode') || ''}
                    onChange={(e) => {
                      const upperValue = e.target.value.toUpperCase()
                      form.setValue('deptCode', upperValue)
                      console.log('🔤 部门编码自动转大写:', e.target.value, '→', upperValue)
                    }}
                  />
                </FormControl>
                <FormDescription>
                  部门的唯一标识，只能包含大写字母、数字和下划线
                </FormDescription>
                <FormMessage>{form.formState.errors.deptCode}</FormMessage>
              </FormItem>
            </FormField>

            {/* 部门名称 */}
            <FormField>
              <FormItem>
                <FormLabel>部门名称 *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入部门名称"
                    value={form.watch('deptName') || ''}
                    onChange={(e) => form.setValue('deptName', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.deptName}</FormMessage>
              </FormItem>
            </FormField>

            {/* 负责人ID */}
            <FormField>
              <FormItem>
                <FormLabel>负责人ID</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="请输入负责人用户ID"
                    value={form.watch('leaderId')?.toString() || ''}
                    onChange={(e) => form.setValue('leaderId', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </FormControl>
                <FormDescription>
                  部门负责人的用户ID，可以为空
                </FormDescription>
                <FormMessage>{form.formState.errors.leaderId}</FormMessage>
              </FormItem>
            </FormField>

            {/* 联系电话 */}
            <FormField>
              <FormItem>
                <FormLabel>联系电话</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入联系电话"
                    value={form.watch('phone') || ''}
                    onChange={(e) => form.setValue('phone', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.phone}</FormMessage>
              </FormItem>
            </FormField>

            {/* 联系邮箱 */}
            <FormField>
              <FormItem>
                <FormLabel>联系邮箱</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="请输入联系邮箱"
                    value={form.watch('email') || ''}
                    onChange={(e) => form.setValue('email', e.target.value)}
                  />
                </FormControl>
                <FormMessage>{form.formState.errors.email}</FormMessage>
              </FormItem>
            </FormField>

            {/* 排序 */}
            <FormField>
              <FormItem>
                <FormLabel>排序 *</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    max="9999"
                    placeholder="请输入排序值"
                    value={form.watch('sortOrder')?.toString() || ''}
                    onChange={(e) => form.setValue('sortOrder', e.target.value ? parseInt(e.target.value) : 1)}
                  />
                </FormControl>
                <FormDescription>
                  数值越小排序越靠前，范围：1-9999
                </FormDescription>
                <FormMessage>{form.formState.errors.sortOrder}</FormMessage>
              </FormItem>
            </FormField>

            {/* 状态 */}
            <FormField>
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">部门状态</FormLabel>
                  <FormDescription>
                    启用后部门可以正常使用
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={form.watch('status') === DeptStatus.ENABLED}
                    onCheckedChange={(checked) => {
                      const newStatus = checked ? DeptStatus.ENABLED : DeptStatus.DISABLED
                      console.log('🔄 状态切换:', checked, '→', newStatus)
                      form.setValue('status', newStatus)
                    }}
                  />
                </FormControl>
              </FormItem>
            </FormField>
          </div>

          {/* 备注 */}
          <FormField>
            <FormItem>
              <FormLabel>备注</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请输入备注信息"
                  className="resize-none"
                  value={form.watch('remark') || ''}
                  onChange={(e) => form.setValue('remark', e.target.value)}
                />
              </FormControl>
              <FormMessage>{form.formState.errors.remark}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {isEdit ? '更新' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DeptForm
