/**
 * 部门用户管理对话框组件
 * 
 * 提供部门用户的分配、移除、主部门设置等功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Checkbox,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui'
import { Loader2, Search, UserPlus, UserMinus, Crown, Users } from 'lucide-react'
import type { Dept } from '@/types/dept.ts'
import type { User } from '@/types/user.ts'
import { DeptService } from '@/services/dept.ts'
import { UserService } from '@/services'

export interface DeptUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  dept: Dept | null
  onSuccess: () => void
}

/**
 * 部门用户管理对话框组件
 */
const DeptUserDialog: React.FC<DeptUserDialogProps> = ({
  open,
  onOpenChange,
  dept,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  
  // 部门用户相关状态
  const [deptUsers, setDeptUsers] = useState<User[]>([])
  const [deptUsersLoading, setDeptUsersLoading] = useState(false)
  
  // 可分配用户相关状态
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [availableUsersLoading, setAvailableUsersLoading] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([])
  
  // 搜索状态
  const [deptUserSearch, setDeptUserSearch] = useState('')
  const [availableUserSearch, setAvailableUserSearch] = useState('')

  // 加载部门用户
  const loadDeptUsers = async () => {
    if (!dept) return

    try {
      setDeptUsersLoading(true)

      // 临时方案：如果后端API有问题，先使用空数组
      try {
        const users = await DeptService.getDeptUsers(dept.id)
        setDeptUsers(users)
      } catch (apiError: any) {
        console.warn('⚠️ 部门用户API暂不可用，使用空数据:', apiError.message)
        if (apiError.message?.includes('Unknown column') || apiError.message?.includes('is_primary')) {
          setDeptUsers([])
          setSubmitError('部门用户功能暂时不可用，后端API需要修复数据库表结构')
        } else {
          throw apiError
        }
      }
    } catch (error) {
      console.error('❌ 加载部门用户失败:', error)
      setSubmitError('加载部门用户失败: ' + (error as Error).message)
      setDeptUsers([])
    } finally {
      setDeptUsersLoading(false)
    }
  }

  // 加载可分配用户
  const loadAvailableUsers = async () => {
    try {
      setAvailableUsersLoading(true)
      const result = await UserService.pageUsers({
        pageNum: 1,
        pageSize: 100,
        status: 1 // 只加载启用的用户
      })
      setAvailableUsers(result.records || [])
    } catch (error) {
      console.error('❌ 加载可分配用户失败:', error)
      setSubmitError('加载可分配用户失败: ' + (error as Error).message)
    } finally {
      setAvailableUsersLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    if (open && dept) {
      setSubmitError(null)
      setSelectedUserIds([])
      loadDeptUsers()
      loadAvailableUsers()
    }
  }, [open, dept?.id])

  // 分配用户到部门
  const handleAssignUsers = async () => {
    if (!dept || selectedUserIds.length === 0) return

    try {
      setLoading(true)
      console.log('🔄 开始分配用户到部门:', { deptId: dept.id, userIds: selectedUserIds })

      // 检查API是否可用
      try {
        // 为每个用户分配部门
        for (const userId of selectedUserIds) {
          await DeptService.assignUserToDepts(userId, [dept.id], dept.id)
        }

        console.log('✅ 用户分配成功')

        // 重新加载数据
        await loadDeptUsers()
        setSelectedUserIds([])

        onSuccess()
      } catch (apiError: any) {
        console.warn('⚠️ 用户分配API暂不可用:', apiError.message)
        setSubmitError('用户分配功能暂时不可用: ' + apiError.message)
      }
    } catch (error) {
      console.error('❌ 分配用户失败:', error)
      setSubmitError('分配用户失败: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 从部门移除用户
  const handleRemoveUser = async (user: User) => {
    if (!dept) return

    try {
      setLoading(true)
      console.log('🔄 开始从部门移除用户:', { deptId: dept.id, userId: user.id })

      try {
        await DeptService.removeUserFromDept(user.id, dept.id)
        console.log('✅ 用户移除成功')

        // 重新加载数据
        await loadDeptUsers()
        onSuccess()
      } catch (apiError: any) {
        console.warn('⚠️ 移除用户API暂不可用:', apiError.message)
        setSubmitError('移除用户功能暂时不可用: ' + apiError.message)
      }
    } catch (error) {
      console.error('❌ 移除用户失败:', error)
      setSubmitError('移除用户失败: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 设置用户主部门
  const handleSetPrimaryDept = async (user: User) => {
    if (!dept) return

    try {
      setLoading(true)
      console.log('🔄 开始设置用户主部门:', { userId: user.id, deptId: dept.id })

      try {
        await DeptService.setUserPrimaryDept(user.id, dept.id)
        console.log('✅ 主部门设置成功')

        // 重新加载数据
        await loadDeptUsers()
        onSuccess()
      } catch (apiError: any) {
        console.warn('⚠️ 设置主部门API暂不可用:', apiError.message)
        setSubmitError('设置主部门功能暂时不可用: ' + apiError.message)
      }
    } catch (error) {
      console.error('❌ 设置主部门失败:', error)
      setSubmitError('设置主部门失败: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 筛选部门用户
  const filteredDeptUsers = deptUsers.filter(user => 
    user.username.toLowerCase().includes(deptUserSearch.toLowerCase()) ||
    (user.realName && user.realName.toLowerCase().includes(deptUserSearch.toLowerCase())) ||
    (user.nickname && user.nickname.toLowerCase().includes(deptUserSearch.toLowerCase()))
  )

  // 筛选可分配用户（排除已在部门的用户）
  const filteredAvailableUsers = availableUsers.filter(user => {
    const isInDept = deptUsers.some(deptUser => deptUser.id === user.id)
    const matchesSearch = user.username.toLowerCase().includes(availableUserSearch.toLowerCase()) ||
      (user.realName && user.realName.toLowerCase().includes(availableUserSearch.toLowerCase())) ||
      (user.nickname && user.nickname.toLowerCase().includes(availableUserSearch.toLowerCase()))
    
    return !isInDept && matchesSearch
  })

  if (!dept) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            部门用户管理 - {dept.deptName}
          </DialogTitle>
          <DialogDescription>
            管理部门下的用户，包括用户分配、移除和主部门设置
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {submitError}
          </div>
        )}

        <Tabs defaultValue="current" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="current">当前用户 ({deptUsers.length})</TabsTrigger>
            <TabsTrigger value="assign">分配用户</TabsTrigger>
          </TabsList>

          {/* 当前部门用户 */}
          <TabsContent value="current" className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="搜索用户..."
                  value={deptUserSearch}
                  onChange={(e) => setDeptUserSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户名</TableHead>
                    <TableHead>真实姓名</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>主部门</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deptUsersLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredDeptUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        {deptUserSearch ? '没有找到匹配的用户' : '该部门暂无用户'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredDeptUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.realName || '-'}</TableCell>
                        <TableCell>{user.email || '-'}</TableCell>
                        <TableCell>
                          <Badge variant={user.status === 1 ? 'default' : 'secondary'}>
                            {user.status === 1 ? '启用' : '禁用'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {/* TODO: 显示是否为主部门 */}
                          <Badge variant="outline">
                            <Crown className="w-3 h-3 mr-1" />
                            主部门
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSetPrimaryDept(user)}
                              disabled={loading}
                              title="设为主部门"
                            >
                              <Crown className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveUser(user)}
                              disabled={loading}
                              className="text-destructive hover:text-destructive"
                              title="移除用户"
                            >
                              <UserMinus className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* 分配用户 */}
          <TabsContent value="assign" className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="搜索可分配用户..."
                  value={availableUserSearch}
                  onChange={(e) => setAvailableUserSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                onClick={handleAssignUsers}
                disabled={loading || selectedUserIds.length === 0}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <UserPlus className="w-4 h-4 mr-2" />
                分配用户 ({selectedUserIds.length})
              </Button>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedUserIds.length === filteredAvailableUsers.length && filteredAvailableUsers.length > 0}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedUserIds(filteredAvailableUsers.map(user => user.id))
                          } else {
                            setSelectedUserIds([])
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead>用户名</TableHead>
                    <TableHead>真实姓名</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {availableUsersLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredAvailableUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        {availableUserSearch ? '没有找到匹配的用户' : '暂无可分配的用户'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAvailableUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUserIds.includes(user.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedUserIds([...selectedUserIds, user.id])
                              } else {
                                setSelectedUserIds(selectedUserIds.filter(id => id !== user.id))
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.realName || '-'}</TableCell>
                        <TableCell>{user.email || '-'}</TableCell>
                        <TableCell>
                          <Badge variant={user.status === 1 ? 'default' : 'secondary'}>
                            {user.status === 1 ? '启用' : '禁用'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DeptUserDialog
