/**
 * 部门搜索表单组件
 * 
 * 提供部门搜索和筛选功能
 * 支持基础搜索和高级筛选
 */

import React, { useState } from 'react'
import { useDebounceFn } from 'ahooks'
import { Search, Filter, X } from 'lucide-react'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../../../components/ui'
import type { DeptQueryRequest } from '../../../../types/dept'
import { DEPT_STATUS_OPTIONS } from '../../../../types/dept'

export interface DeptSearchFormProps {
  searchParams: DeptQueryRequest
  onSearchChange: (params: DeptQueryRequest) => void
  onSearch: () => void
  onReset: () => void
  loading?: boolean
}

/**
 * 部门搜索表单组件
 */
const DeptSearchForm: React.FC<DeptSearchFormProps> = ({
  searchParams,
  onSearchChange,
  onSearch: _onSearch,
  onReset,
  loading = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [searchKeyword, setSearchKeyword] = useState('')

  // 防抖搜索函数
  const { run: debouncedSearch } = useDebounceFn(
    (keyword: string) => {
      // 修复：使用单一的搜索关键词字段，而不是分别设置deptCode和deptName
      const newParams = {
        ...searchParams,
        keyword: keyword || undefined, // 使用通用的keyword字段
        deptCode: undefined, // 清除单独的字段
        deptName: undefined, // 清除单独的字段
        pageNum: 1 // 搜索时重置到第一页
      }
      onSearchChange(newParams)
    },
    {
      wait: 800, // 800ms防抖延迟
    }
  )

  // 处理基础搜索输入变更
  const handleBasicSearchChange = (value: string) => {
    setSearchKeyword(value)
    // 如果输入为空，立即搜索；否则使用防抖
    if (value.trim() === '') {
      const newParams = {
        ...searchParams,
        keyword: undefined,
        deptCode: undefined,
        deptName: undefined,
        pageNum: 1
      }
      onSearchChange(newParams)
    } else {
      debouncedSearch(value.trim())
    }
  }

  // 手动搜索（点击搜索按钮）
  const handleManualSearch = () => {
    const newParams = {
      ...searchParams,
      keyword: searchKeyword.trim() || undefined,
      deptCode: undefined,
      deptName: undefined,
      pageNum: 1
    }
    onSearchChange(newParams)
  }

  // 高级筛选防抖函数
  const { run: debouncedAdvancedFilter } = useDebounceFn(
    (field: keyof DeptQueryRequest, value: any) => {
      const newParams = {
        ...searchParams,
        [field]: value,
        pageNum: 1 // 筛选时重置到第一页
      }
      onSearchChange(newParams)
    },
    {
      wait: 500, // 500ms防抖延迟，比基础搜索稍快
    }
  )

  // 处理高级筛选变更
  const handleAdvancedFilterChange = (field: keyof DeptQueryRequest, value: any) => {
    // 对于下拉选择和日期选择，立即触发；对于文本输入，使用防抖
    if (field === 'status' || field === 'startTime' || field === 'endTime') {
      // 立即触发
      const newParams = {
        ...searchParams,
        [field]: value,
        pageNum: 1
      }
      onSearchChange(newParams)
    } else {
      // 使用防抖
      debouncedAdvancedFilter(field, value)
    }
  }

  // 计算活跃筛选条件数量
  const activeFiltersCount = [
    searchParams.phone,
    searchParams.email,
    searchParams.status,
    searchParams.leaderId,
    searchParams.startTime,
    searchParams.endTime
  ].filter(Boolean).length

  return (
    <div className="space-y-4">
      {/* 基础搜索 */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 flex-1">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="搜索部门编码或部门名称... (输入后自动搜索，或点击搜索按钮)"
              value={searchKeyword}
              onChange={(e) => handleBasicSearchChange(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleManualSearch()
                }
              }}
              className="pl-10"
              disabled={loading}
            />
          </div>
          
          <Button 
            onClick={handleManualSearch}
            disabled={loading}
            className="shrink-0"
          >
            <Search className="w-4 h-4 mr-2" />
            搜索
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="relative"
            disabled={loading}
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Filter className="w-4 h-4 mr-2" />
            高级筛选
            {activeFiltersCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {activeFiltersCount}
              </span>
            )}
          </Button>

          {(activeFiltersCount > 0 || searchKeyword.trim()) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchKeyword('') // 清空搜索关键词
                onReset()
              }}
              disabled={loading}
              className="text-muted-foreground"
            >
              <X className="w-4 h-4 mr-1" />
              清除所有
            </Button>
          )}
        </div>
      </div>

      {/* 高级筛选 */}
      {showAdvanced && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/20">
            <div className="space-y-2">
              <label className="text-sm font-medium">联系电话</label>
              <Input
                placeholder="请输入联系电话"
                value={searchParams.phone || ''}
                onChange={(e) => handleAdvancedFilterChange('phone', e.target.value || undefined)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">联系邮箱</label>
              <Input
                placeholder="请输入联系邮箱"
                value={searchParams.email || ''}
                onChange={(e) => handleAdvancedFilterChange('email', e.target.value || undefined)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">状态</label>
              <Select
                value={searchParams.status?.toString() || 'all'}
                onValueChange={(value) => handleAdvancedFilterChange('status', value === 'all' ? undefined : parseInt(value))}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {DEPT_STATUS_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">负责人ID</label>
              <Input
                type="number"
                placeholder="请输入负责人ID"
                value={searchParams.leaderId?.toString() || ''}
                onChange={(e) => handleAdvancedFilterChange('leaderId', e.target.value ? parseInt(e.target.value) : undefined)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">创建时间（开始）</label>
              <Input
                type="date"
                value={searchParams.startTime || ''}
                onChange={(e) => handleAdvancedFilterChange('startTime', e.target.value || undefined)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">创建时间（结束）</label>
              <Input
                type="date"
                value={searchParams.endTime || ''}
                onChange={(e) => handleAdvancedFilterChange('endTime', e.target.value || undefined)}
                disabled={loading}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DeptSearchForm
