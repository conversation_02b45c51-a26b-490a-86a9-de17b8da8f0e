/**
 * 用户消费明细页面
 * 
 * 展示用户的消费记录，支持搜索、筛选、排序和分页功能
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'
import { 
  Search, 
  Filter, 
  Download, 
  RefreshCw, 
  ShoppingCart, 
  Calendar,
  DollarSign,
  Wallet,
  Info,
  ArrowUpDown
} from 'lucide-react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  Skeleton,
  Alert,
  AlertDescription,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui'
import { OperationsService } from '@/services/operations'
import type { ConsumeQueryRequest, ConsumeDetailResponse, PageResult } from '@/services/operations'
import { formatTimestamp, formatCurrency } from '@/utils'
import { toast } from '@/hooks/useToast'

/**
 * 用户消费明细页面组件
 */
export default function ConsumeDetails() {
  const [searchParams] = useSearchParams()
  const userId = Number(searchParams.get('userId'))
  const anchorId = Number(searchParams.get('anchorId'))
  const userName = searchParams.get('userName') || '未知用户'
  const anchorName = searchParams.get('anchorName') || '未知主播'
  const queryType = searchParams.get('type') || 'user' // 'user' 或 'anchor'

  // 根据查询类型确定显示的ID和名称
  const displayId = queryType === 'anchor' ? anchorId : userId
  const displayName = queryType === 'anchor' ? anchorName : userName
  const displayTitle = queryType === 'anchor' ? '主播消费明细' : '用户消费明细'

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<PageResult<ConsumeDetailResponse> | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 请求ID管理，防止竞态条件
  const currentRequestId = useRef<string>('')

  // 查询参数状态
  const [queryParams, setQueryParams] = useState<ConsumeQueryRequest>({
    pageNum: 1,
    pageSize: 20,
    orderBy: 'time',
    orderDirection: 'DESC'
  })

  // 搜索表单状态
  const [searchForm, setSearchForm] = useState({
    orderId: '',
    minAmount: '',
    maxAmount: '',
    info: ''
  })

  /**
   * 获取消费明细列表
   */
  const fetchConsumeDetails = useCallback(async (params: ConsumeQueryRequest, requestId?: string) => {
    if (!displayId) {
      setError(`缺少${queryType === 'anchor' ? '主播' : '用户'}ID参数`)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await OperationsService.getConsumeDetails(displayId, params)

      // 检查请求是否已过期（防止竞态条件）
      if (requestId && requestId !== currentRequestId.current) {
        return
      }

      setData(result)
    } catch (err) {
      // 同样检查请求是否已过期
      if (requestId && requestId !== currentRequestId.current) {
        return
      }

      let errorMessage = '获取消费明细失败'
      let userFriendlyMessage = '请稍后重试，如问题持续存在请联系管理员'

      if (err instanceof Error) {
        errorMessage = err.message
        // 根据错误类型提供用户友好的提示
        if (err.message.includes('网络')) {
          userFriendlyMessage = '网络连接异常，请检查网络后重试'
        } else if (err.message.includes('权限')) {
          userFriendlyMessage = '您没有权限查看此数据，请联系管理员'
        } else if (err.message.includes('参数')) {
          userFriendlyMessage = '查询参数有误，请检查输入条件'
        }
      }

      setError(errorMessage)
      toast({
        title: '获取数据失败',
        description: userFriendlyMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [userId])

  /**
   * 防抖搜索
   */
  // const _debouncedSearch = useCallback(
  //   debounce((params: ConsumeQueryRequest) => {
  //     fetchConsumeDetails(params)
  //   }, 500),
  //   [fetchConsumeDetails]
  // )

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    const requestId = Date.now().toString()
    currentRequestId.current = requestId

    const newParams: ConsumeQueryRequest = {
      ...queryParams,
      pageNum: 1,
      ...Object.fromEntries(
        Object.entries(searchForm).filter(([_, value]) => value !== '')
      ),
      minAmount: searchForm.minAmount ? Number(searchForm.minAmount) : undefined,
      maxAmount: searchForm.maxAmount ? Number(searchForm.maxAmount) : undefined
    }
    setQueryParams(newParams)
    fetchConsumeDetails(newParams, requestId)
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    const resetForm = {
      orderId: '',
      minAmount: '',
      maxAmount: '',
      info: ''
    }
    setSearchForm(resetForm)
    
    const resetParams: ConsumeQueryRequest = {
      pageNum: 1,
      pageSize: 20,
      orderBy: 'time',
      orderDirection: 'DESC'
    }
    setQueryParams(resetParams)
    fetchConsumeDetails(resetParams)
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number) => {
    const newParams = { ...queryParams, pageNum: page }
    setQueryParams(newParams)
    fetchConsumeDetails(newParams)
  }

  /**
   * 处理每页条数变化
   */
  const handlePageSizeChange = (pageSize: number) => {
    const newParams = { ...queryParams, pageNum: 1, pageSize }
    setQueryParams(newParams)
    fetchConsumeDetails(newParams)
  }

  /**
   * 处理排序变化
   */
  const handleSortChange = (field: string) => {
    const newDirection = queryParams.orderBy === field && queryParams.orderDirection === 'DESC' 
      ? 'ASC' 
      : 'DESC'
    
    const newParams = {
      ...queryParams,
      orderBy: field,
      orderDirection: newDirection as 'ASC' | 'DESC'
    }
    setQueryParams(newParams)
    fetchConsumeDetails(newParams)
  }

  /**
   * 处理导出
   */
  const handleExport = () => {
    toast({
      title: '导出功能',
      description: '导出功能正在开发中...',
      variant: 'default'
    })
  }

  // 初始化加载数据
  useEffect(() => {
    fetchConsumeDetails(queryParams)
  }, [])

  // 渲染加载状态
  if (loading && !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>消费明细</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{displayTitle}</h1>
          <p className="text-muted-foreground">
            {queryType === 'anchor' ? '主播' : '用户'}：{displayName}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchConsumeDetails(queryParams)}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            搜索筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">订单ID</label>
              <Input
                placeholder="请输入订单ID"
                value={searchForm.orderId}
                onChange={(e) => setSearchForm(prev => ({ ...prev, orderId: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">最小金额</label>
              <Input
                type="number"
                placeholder="请输入最小金额"
                value={searchForm.minAmount}
                onChange={(e) => setSearchForm(prev => ({ ...prev, minAmount: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">最大金额</label>
              <Input
                type="number"
                placeholder="请输入最大金额"
                value={searchForm.maxAmount}
                onChange={(e) => setSearchForm(prev => ({ ...prev, maxAmount: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">消费说明</label>
              <Input
                placeholder="请输入消费说明关键词"
                value={searchForm.info}
                onChange={(e) => setSearchForm(prev => ({ ...prev, info: e.target.value }))}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={handleReset}>
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>消费记录</CardTitle>
            <div className="text-sm text-muted-foreground">
              共 {data?.total || 0} 条记录
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单ID</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('amount')}
                  >
                    <div className="flex items-center">
                      消费金额
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('balance')}
                  >
                    <div className="flex items-center">
                      余额
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('time')}
                  >
                    <div className="flex items-center">
                      消费时间
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead>消费说明</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                    </TableRow>
                  ))
                ) : data?.records?.length ? (
                  data.records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium font-mono text-sm">
                        {record.id}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1 text-red-600" />
                          <span className="font-medium text-red-600">
                            -{formatCurrency(record.amount)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Wallet className="h-4 w-4 mr-1 text-blue-600" />
                          <span className="font-medium">
                            {formatCurrency(record.balance)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span className="text-sm">
                            {formatTimestamp(record.time)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center max-w-xs">
                                <ShoppingCart className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                                <span className="truncate text-sm">
                                  {record.info}
                                </span>
                                {record.info.length > 30 && (
                                  <Info className="h-4 w-4 ml-1 text-muted-foreground flex-shrink-0" />
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs break-words">{record.info}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页组件 */}
          {data && data.total > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground w-14">每页显示</span>
                <Select
                  value={queryParams.pageSize.toString()}
                  onValueChange={(value) => handlePageSizeChange(Number(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">条</span>
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => handlePageChange(queryParams.pageNum - 1)}
                      className={queryParams.pageNum <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  
                  {(() => {
                    const currentPage = queryParams.pageNum
                    const totalPages = data.totalPages
                    const maxVisible = 5

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))
                    const endPage = Math.min(totalPages, startPage + maxVisible - 1)

                    // 调整起始页，确保显示足够的页码
                    if (endPage - startPage + 1 < maxVisible) {
                      startPage = Math.max(1, endPage - maxVisible + 1)
                    }

                    return Array.from({ length: endPage - startPage + 1 }, (_, i) => {
                      const page = startPage + i
                      return (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={page === currentPage}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      )
                    })
                  })()}
                  
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => handlePageChange(queryParams.pageNum + 1)}
                      className={queryParams.pageNum >= data.totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
