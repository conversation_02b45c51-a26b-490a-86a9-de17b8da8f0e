/**
 * 新增人数卡片组件示例
 * 
 * 展示如何使用新增的 periodNewInviteCount 字段
 */

import React from 'react'
import { UserPlus, TrendingUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import { Badge } from '@/components/ui'
import type { AnchorStatsResponse } from '../types/operations'

interface NewInviteCountCardProps {
  /** 主播统计数据 */
  stats: AnchorStatsResponse | null
  /** 是否显示加载状态 */
  loading?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * 新增人数卡片组件
 */
export const NewInviteCountCard: React.FC<NewInviteCountCardProps> = ({
  stats,
  loading = false,
  className
}) => {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            新增人数
          </CardTitle>
          <UserPlus className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
          <div className="h-3 w-24 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    )
  }

  const newInviteCount = stats?.periodNewInviteCount ?? 0
  const hasNewInvites = newInviteCount > 0

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          新增人数
        </CardTitle>
        <UserPlus className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {newInviteCount.toLocaleString()}
          <span className="text-sm font-normal text-muted-foreground ml-1">人</span>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          时间区间内新邀请的用户
        </p>
        {hasNewInvites && (
          <div className="flex items-center mt-2">
            <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
            <Badge variant="secondary" className="text-xs">
              +{newInviteCount} 新增
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default NewInviteCountCard
