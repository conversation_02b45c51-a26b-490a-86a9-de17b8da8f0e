/**
 * 主播列表数据管理Hook
 * 
 * 提供主播列表的查询、筛选、分页等功能
 */

import { useState, useCallback, useEffect } from 'react'
import { toast } from '@/hooks/useToast'
import { OperationsService } from '../services/operations'
import {
  UserIdentity,
  UserState,
  AuthStatus
} from '../types/operations'
import type {
  AnchorQueryRequest,
  AnchorListResponse,
  PageResult
} from '../types/operations'

/**
 * 主播列表Hook状态接口
 */
interface UseAnchorListState {
  /** 主播列表数据 */
  data: AnchorListResponse[]
  /** 总数 */
  total: number
  /** 加载状态 */
  loading: boolean
  /** 错误信息 */
  error: string | null
  /** 当前页码 */
  currentPage: number
  /** 每页大小 */
  pageSize: number
  /** 查询条件 */
  filters: Partial<AnchorQueryRequest>
}

/**
 * 主播列表Hook返回值接口
 */
interface UseAnchorListReturn extends UseAnchorListState {
  /** 刷新数据 */
  refresh: () => Promise<void>
  /** 搜索主播 */
  search: (filters: Partial<AnchorQueryRequest>) => Promise<void>
  /** 重置筛选条件 */
  resetFilters: () => void
  /** 切换页码 */
  changePage: (page: number) => void
  /** 切换每页大小 */
  changePageSize: (size: number) => void
  /** 设置筛选条件 */
  setFilters: (filters: Partial<AnchorQueryRequest>) => void
}

/**
 * 主播列表数据管理Hook
 * 
 * @param initialFilters 初始筛选条件
 * @returns 主播列表状态和操作方法
 */
export function useAnchorList(
  initialFilters: Partial<AnchorQueryRequest> = {}
): UseAnchorListReturn {
  
  const [state, setState] = useState<UseAnchorListState>({
    data: [],
    total: 0,
    loading: false,
    error: null,
    currentPage: 1,
    pageSize: 10,
    filters: initialFilters
  })
  
  /**
   * 获取主播列表数据
   */
  const fetchData = useCallback(async (
    page: number = state.currentPage,
    size: number = state.pageSize,
    filters: Partial<AnchorQueryRequest> = state.filters
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const params: AnchorQueryRequest = {
        pageNum: page,
        pageSize: size,
        ...filters
      }
      
      const result: PageResult<AnchorListResponse> = await OperationsService.getAnchorList(params)
      
      setState(prev => ({
        ...prev,
        data: result.records || [],
        total: result.total || 0,
        currentPage: page,
        pageSize: size,
        filters,
        loading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取主播列表失败'
      setState(prev => ({
        ...prev,
        data: [],
        total: 0,
        loading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [state.currentPage, state.pageSize, state.filters])
  
  /**
   * 刷新数据
   */
  const refresh = useCallback(async () => {
    await fetchData()
  }, [fetchData])
  
  /**
   * 搜索主播
   */
  const search = useCallback(async (filters: Partial<AnchorQueryRequest>) => {
    await fetchData(1, state.pageSize, filters)
  }, [fetchData, state.pageSize])
  
  /**
   * 重置筛选条件
   */
  const resetFilters = useCallback(() => {
    const resetFilters = {}
    setState(prev => ({ ...prev, filters: resetFilters }))
    fetchData(1, state.pageSize, resetFilters)
  }, [fetchData, state.pageSize])
  
  /**
   * 切换页码
   */
  const changePage = useCallback((page: number) => {
    fetchData(page, state.pageSize, state.filters)
  }, [fetchData, state.pageSize, state.filters])
  
  /**
   * 切换每页大小
   */
  const changePageSize = useCallback((size: number) => {
    fetchData(1, size, state.filters)
  }, [fetchData, state.filters])
  
  /**
   * 设置筛选条件
   */
  const setFilters = useCallback((filters: Partial<AnchorQueryRequest>) => {
    setState(prev => ({ ...prev, filters }))
  }, [])
  
  // 初始化数据加载
  useEffect(() => {
    fetchData()
  }, []) // 只在组件挂载时执行一次
  
  return {
    ...state,
    refresh,
    search,
    resetFilters,
    changePage,
    changePageSize,
    setFilters
  }
}

/**
 * 主播筛选条件Hook
 * 
 * 提供主播筛选表单的状态管理
 */
export function useAnchorFilters() {
  const [filters, setFilters] = useState<Partial<AnchorQueryRequest>>({})
  
  /**
   * 更新筛选条件
   */
  const updateFilter = useCallback(<K extends keyof AnchorQueryRequest>(
    key: K,
    value: AnchorQueryRequest[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])
  
  /**
   * 批量更新筛选条件
   */
  const updateFilters = useCallback((newFilters: Partial<AnchorQueryRequest>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }, [])
  
  /**
   * 重置筛选条件
   */
  const resetFilters = useCallback(() => {
    setFilters({})
  }, [])
  
  /**
   * 获取筛选条件的显示文本
   */
  const getFilterText = useCallback(() => {
    const texts: string[] = []
    
    if (filters.nickname) {
      texts.push(`昵称: ${filters.nickname}`)
    }
    
    if (filters.status !== undefined) {
      const statusText = filters.status === UserState.NORMAL ? '正常' : '禁用'
      texts.push(`状态: ${statusText}`)
    }
    
    if (filters.identity !== undefined) {
      const identityText = filters.identity === UserIdentity.ONLINE_ANCHOR ? '线上主播' : '线下主播'
      texts.push(`身份: ${identityText}`)
    }
    
    if (filters.isAuth !== undefined) {
      const authText = filters.isAuth === AuthStatus.AUTHED ? '已实名' : '未实名'
      texts.push(`认证: ${authText}`)
    }
    
    if (filters.phone) {
      texts.push(`手机号: ${filters.phone}`)
    }
    
    if (filters.registerStartTime && filters.registerEndTime) {
      const startDate = new Date(filters.registerStartTime * 1000).toLocaleDateString()
      const endDate = new Date(filters.registerEndTime * 1000).toLocaleDateString()
      texts.push(`注册时间: ${startDate} - ${endDate}`)
    }
    
    return texts.length > 0 ? texts.join(', ') : '无筛选条件'
  }, [filters])
  
  return {
    filters,
    updateFilter,
    updateFilters,
    resetFilters,
    getFilterText
  }
}
