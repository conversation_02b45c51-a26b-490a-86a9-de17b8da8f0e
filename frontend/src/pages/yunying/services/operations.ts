/**
 * 运营模块API服务
 * 
 * 提供运营数据相关的API调用功能
 */

import { httpClient } from '@/services/request'
// PageResult类型已在operations.ts中定义
import type {
  AnchorQueryRequest,
  AnchorListResponse,
  AnchorStatsResponse,
  FirstRechargeStatsResponse,
  SubUserQueryRequest,
  SubUserResponse,
  ConsumeQueryRequest,
  ConsumeDetailResponse,
  RechargeQueryRequest,
  RechargeDetailResponse,
  PageResult
} from '../types/operations'

/**
 * 运营数据API服务类
 */
export class OperationsService {
  
  /**
   * 获取主播列表
   * 支持分页、搜索和筛选功能
   * 
   * @param params 查询参数
   * @returns 分页的主播列表
   */
  static async getAnchorList(params: AnchorQueryRequest): Promise<PageResult<AnchorListResponse>> {
    return httpClient.post('/operations/anchors', params)
  }
  
  /**
   * 获取主播详细统计数据
   * 调用存储过程获取主播的详细业务统计信息
   * 
   * @param anchorId 主播ID
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 主播统计数据
   */
  static async getAnchorStats(
    anchorId: number,
    startTime?: number,
    endTime?: number
  ): Promise<AnchorStatsResponse> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }
    
    const url = `/operations/anchors/${anchorId}/stats${params.toString() ? '?' + params.toString() : ''}`
    return httpClient.get(url)
  }
  
  /**
   * 获取主播首充统计数据
   * 获取主播下级用户的首充人数和转化率统计
   * 
   * @param anchorId 主播ID
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 首充统计数据
   */
  static async getFirstRechargeStats(
    anchorId: number,
    startTime?: number,
    endTime?: number
  ): Promise<FirstRechargeStatsResponse> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }
    
    const url = `/operations/anchors/${anchorId}/first-recharge-stats${params.toString() ? '?' + params.toString() : ''}`
    return httpClient.get(url)
  }
  
  /**
   * 获取主播下级用户列表
   * 支持分页和搜索功能
   * 
   * @param anchorId 主播ID
   * @param params 查询参数
   * @returns 分页的下级用户列表
   */
  static async getSubUsers(
    anchorId: number,
    params: SubUserQueryRequest
  ): Promise<PageResult<SubUserResponse>> {
    return httpClient.post(`/operations/anchors/${anchorId}/users`, params)
  }
  
  /**
   * 获取用户消费详情
   * 查询用户的消费记录，支持时间范围筛选
   * 
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 分页的消费详情列表
   */
  static async getUserConsumeDetails(
    userId: number,
    params: ConsumeQueryRequest
  ): Promise<PageResult<ConsumeDetailResponse>> {
    return httpClient.post(`/operations/users/${userId}/consume`, params)
  }
  
  /**
   * 获取用户充值详情
   * 查询用户的充值记录，支持时间范围筛选
   * 
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 分页的充值详情列表
   */
  static async getUserRechargeDetails(
    userId: number,
    params: RechargeQueryRequest
  ): Promise<PageResult<RechargeDetailResponse>> {
    return httpClient.post(`/operations/users/${userId}/recharge`, params)
  }

  /**
   * 获取运营统计数据
   * 获取总主播数、活跃主播数、总用户数等关键指标
   *
   * @param startTime 开始时间（时间戳，可选）
   * @param endTime 结束时间（时间戳，可选）
   * @returns 运营统计数据
   */
  static async getOperationsStats(startTime?: number, endTime?: number): Promise<any> {
    const params = new URLSearchParams()
    if (startTime !== undefined) {
      params.append('startTime', startTime.toString())
    }
    if (endTime !== undefined) {
      params.append('endTime', endTime.toString())
    }

    const url = `/operations/stats${params.toString() ? '?' + params.toString() : ''}`
    return httpClient.get(url)
  }
}

/**
 * 运营数据工具函数
 */
export class OperationsUtils {
  
  /**
   * 格式化时间戳为日期字符串
   * 
   * @param timestamp 时间戳
   * @returns 格式化的日期字符串
   */
  static formatTimestamp(timestamp: number): string {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  /**
   * 格式化金额显示
   * 
   * @param amount 金额
   * @param precision 小数位数，默认2位
   * @returns 格式化的金额字符串
   */
  static formatAmount(amount: number, precision: number = 2): string {
    if (amount === null || amount === undefined) return '0.00'
    return amount.toFixed(precision)
  }
  
  /**
   * 格式化百分比显示
   * 
   * @param ratio 比率值
   * @param precision 小数位数，默认2位
   * @returns 格式化的百分比字符串
   */
  static formatPercentage(ratio: number, precision: number = 2): string {
    if (ratio === null || ratio === undefined) return '0.00%'
    return `${ratio.toFixed(precision)}%`
  }
  
  /**
   * 获取用户状态显示文本
   * 
   * @param state 用户状态
   * @returns 状态显示文本
   */
  static getUserStateText(state: number): string {
    switch (state) {
      case 1:
        return '正常'
      case 2:
        return '禁用'
      default:
        return '未知'
    }
  }
  
  /**
   * 获取用户身份显示文本
   * 
   * @param identity 用户身份
   * @returns 身份显示文本
   */
  static getUserIdentityText(identity: number): string {
    switch (identity) {
      case 1:
        return '普通用户'
      case 2:
        return '线上主播'
      case 3:
        return '线下主播'
      default:
        return '未知'
    }
  }
  
  /**
   * 获取实名认证状态显示文本
   * 
   * @param isAuth 实名认证状态
   * @returns 认证状态显示文本
   */
  static getAuthStatusText(isAuth: number): string {
    switch (isAuth) {
      case 0:
        return '未实名'
      case 1:
        return '已实名'
      default:
        return '未知'
    }
  }
  
  /**
   * 获取充值状态显示文本
   * 
   * @param state 充值状态
   * @returns 状态显示文本
   */
  static getRechargeStateText(state: number): string {
    switch (state) {
      case 1:
        return '未支付'
      case 2:
        return '已支付'
      case 3:
        return '已支付但回调异常'
      default:
        return '未知'
    }
  }
  
  /**
   * 获取充值状态对应的颜色类名
   * 
   * @param state 充值状态
   * @returns 颜色类名
   */
  static getRechargeStateColor(state: number): string {
    switch (state) {
      case 1:
        return 'text-yellow-600'
      case 2:
        return 'text-green-600'
      case 3:
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }
  
  /**
   * 获取用户状态对应的颜色类名
   * 
   * @param state 用户状态
   * @returns 颜色类名
   */
  static getUserStateColor(state: number): string {
    switch (state) {
      case 1:
        return 'text-green-600'
      case 2:
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }
  
  /**
   * 计算时间范围的默认值
   * 
   * @param days 天数，默认30天
   * @returns 开始和结束时间戳
   */
  static getDefaultTimeRange(days: number = 30): { startTime: number; endTime: number } {
    const endTime = Math.floor(Date.now() / 1000)
    const startTime = endTime - (days * 24 * 60 * 60)
    return { startTime, endTime }
  }
  
  /**
   * 将日期转换为时间戳
   * 
   * @param date 日期对象或日期字符串
   * @returns 时间戳（秒）
   */
  static dateToTimestamp(date: Date | string): number {
    return Math.floor(new Date(date).getTime() / 1000)
  }
  
  /**
   * 将时间戳转换为日期对象
   * 
   * @param timestamp 时间戳（秒）
   * @returns 日期对象
   */
  static timestampToDate(timestamp: number): Date {
    return new Date(timestamp * 1000)
  }
}
