import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useMemoizedFn } from 'ahooks'
import { Home, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui'
import { usePageTitle } from '@/router'
import { useAuthStore, usePermission } from '@/stores'
import { ROUTES } from '@/constants'

/**
 * 404页面组件 - 基于Context7最佳实践的导航优化
 */
const NotFound: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()
  const { hasAllPermissions } = usePermission()

  // 设置页面标题
  usePageTitle('页面不存在')

  /**
   * 智能导航到首页 - 根据用户权限选择合适的页面
   * 使用ahooks的useMemoizedFn确保函数引用稳定
   */
  const handleGoHome = useMemoizedFn(() => {
    if (!isAuthenticated) {
      navigate(ROUTES.LOGIN)
      return
    }

    // 根据用户权限确定最佳首页
    if (hasAllPermissions(['dashboard:view'])) {
      navigate(ROUTES.DASHBOARD)
    } else if (hasAllPermissions(['system:user:list'])) {
      navigate(ROUTES.SYSTEM_USER)
    } else if (hasAllPermissions(['system:view'])) {
      navigate(ROUTES.SYSTEM)
    } else {
      // 如果没有任何权限，跳转到仪表板（会显示权限提示）
      navigate(ROUTES.DASHBOARD)
    }
  })

  /**
   * 安全返回上一页
   * 使用ahooks的useMemoizedFn确保函数引用稳定
   */
  const handleGoBack = useMemoizedFn(() => {
    // 检查是否有历史记录
    if (window.history.length > 1) {
      navigate(-1)
    } else {
      // 如果没有历史记录，跳转到首页
      handleGoHome()
    }
  })

  /**
   * 导航到指定页面（带权限检查）
   * 使用ahooks的useMemoizedFn确保函数引用稳定
   */
  const handleNavigateTo = useMemoizedFn((path: string, requiredPermissions: string[] = []) => {
    if (!isAuthenticated) {
      navigate(ROUTES.LOGIN)
      return
    }

    if (requiredPermissions.length === 0 || hasAllPermissions(requiredPermissions)) {
      navigate(path)
    } else {
      // 如果没有权限，显示提示并跳转到首页
      console.warn('没有访问权限:', path)
      handleGoHome()
    }
  })

  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        {/* 404图标 */}
        <div className="space-y-4">
          <div className="text-8xl font-bold text-primary/20">404</div>
          <h1 className="text-2xl font-bold text-foreground">
            页面不存在
          </h1>
          <p className="text-muted-foreground">
            抱歉，您访问的页面不存在或已被移除。
            请检查URL是否正确，或返回首页继续浏览。
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={handleGoHome} className="flex items-center">
            <Home className="w-4 h-4 mr-2" />
            返回首页
          </Button>
          <Button variant="outline" onClick={handleGoBack} className="flex items-center">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回上页
          </Button>
        </div>

        {/* 智能建议链接 - 基于用户权限 */}
        {isAuthenticated && (
          <div className="pt-6 border-t border-border">
            <div className="space-y-2">
              {hasAllPermissions(['dashboard:view']) && (
                <button
                  onClick={() => handleNavigateTo(ROUTES.DASHBOARD, ['dashboard:view'])}
                  className="block w-full text-sm text-primary hover:text-primary/80 transition-colors"
                >
                  仪表板
                </button>
              )}
              {hasAllPermissions(['system:user:list']) && (
                <button
                  onClick={() => handleNavigateTo(ROUTES.SYSTEM_USER, ['system:user:list'])}
                  className="block w-full text-sm text-primary hover:text-primary/80 transition-colors"
                >
                  用户管理
                </button>
              )}
              {hasAllPermissions(['system:role:list']) && (
                <button
                  onClick={() => handleNavigateTo(ROUTES.SYSTEM_ROLE, ['system:role:list'])}
                  className="block w-full text-sm text-primary hover:text-primary/80 transition-colors"
                >
                  角色管理
                </button>
              )}
              {hasAllPermissions(['system:permission:list']) && (
                <button
                  onClick={() => handleNavigateTo(ROUTES.SYSTEM_PERMISSION, ['system:permission:list'])}
                  className="block w-full text-sm text-primary hover:text-primary/80 transition-colors"
                >
                  权限管理
                </button>
              )}
            </div>

            {/* 用户信息 */}
            {user && (
              <div className="mt-4 pt-4 border-t border-border">
                <p className="text-xs text-muted-foreground">
                  当前用户: {user.username} | 租户: {user.tenantId}
                </p>
              </div>
            )}
          </div>
        )}

        {/* 未登录用户的提示 */}
        {!isAuthenticated && (
          <div className="pt-6 border-t border-border">
            <p className="text-sm text-muted-foreground mb-3">
              请先登录以访问系统功能
            </p>
            <Button
              onClick={() => navigate(ROUTES.LOGIN)}
              variant="outline"
              className="w-full"
            >
              🔑 前往登录
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default NotFound
