/**
 * 登录日志管理页面
 * 
 * 基于权限管理模块的成功架构模式
 */

import React, { useState, useCallback, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui'
import {
  Trash2,
  Download,
  RefreshCw,
  AlertTriangle,
  LogIn,
  Eraser
} from 'lucide-react'
import { PagePermissionWrapper, PermissionToolbar, PermissionButton } from '@/components/auth/PermissionWrapper'
import { useToast } from '@/hooks'
import { MonitorService } from '@/services/monitor'
import type { LoginLog, LoginLogQueryRequest, PageResult } from '@/types/monitor'
import { DeleteConfirmDialog } from '../operlog/components'
import {
  LoginLogSearchForm,
  LoginLogTable,
  LoginLogDetailDialog
} from './components'

/**
 * 登录日志管理页面组件
 */
const LoginLogList: React.FC = () => {
  const { toast } = useToast()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [loginLogs, setLoginLogs] = useState<LoginLog[]>([])
  const [pageData, setPageData] = useState<PageResult<LoginLog>>({
    records: [],
    total: 0,
    pageNum: 1,
    pageSize: 20
  })
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [searchParams, setSearchParams] = useState<LoginLogQueryRequest>({
    pageNum: 1,
    pageSize: 20
  })

  // 对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [clearDialogOpen, setClearDialogOpen] = useState(false)
  const [currentLoginLog, setCurrentLoginLog] = useState<LoginLog | undefined>()
  const [deleteLoading, setDeleteLoading] = useState(false)

  /**
   * 加载登录日志列表
   */
  const loadLoginLogs = useCallback(async () => {
    try {
      setLoading(true)
      const data = await MonitorService.getLoginLogList(searchParams)
      setPageData(data)
      setLoginLogs(data.records)
      
      // 清除已删除的选中项
      setSelectedIds(prev => prev.filter(id => 
        data.records.some(loginLog => loginLog.id === id)
      ))
    } catch (error) {
      console.error('加载登录日志列表失败:', error)
      toast({
        title: '加载失败',
        description: '登录日志列表加载失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [searchParams, toast])

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    setSearchParams(prev => ({ ...prev, pageNum: 1 }))
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    setSearchParams({
      pageNum: 1,
      pageSize: 20
    })
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number, pageSize?: number) => {
    const validPage = typeof page === 'number' && !isNaN(page) && page > 0 ? page : 1
    const validPageSize = typeof pageSize === 'number' && !isNaN(pageSize) && pageSize > 0 
      ? pageSize 
      : (typeof searchParams.pageSize === 'number' && !isNaN(searchParams.pageSize) ? searchParams.pageSize : 20)
    
    setSearchParams(prev => ({
      ...prev,
      pageNum: validPage,
      pageSize: validPageSize
    }))
  }

  /**
   * 处理查看详情
   */
  const handleView = (loginLog: LoginLog) => {
    setCurrentLoginLog(loginLog)
    setDetailDialogOpen(true)
  }

  /**
   * 处理删除单个
   */
  const handleDelete = (loginLog: LoginLog) => {
    setCurrentLoginLog(loginLog)
    setDeleteDialogOpen(true)
  }

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      toast({
        title: '请选择要删除的日志',
        description: '请先选择要删除的登录日志',
        variant: 'destructive'
      })
      return
    }

    const selectedLoginLogs = loginLogs.filter(loginLog => selectedIds.includes(loginLog.id))
    setCurrentLoginLog(undefined)
    setLoginLogs(selectedLoginLogs)
    setDeleteDialogOpen(true)
  }

  /**
   * 确认删除
   */
  const handleConfirmDelete = async () => {
    try {
      setDeleteLoading(true)
      
      if (currentLoginLog) {
        // 删除单个
        await MonitorService.deleteLoginLog(currentLoginLog.id)
        toast({
          title: '删除成功',
          description: '登录日志删除成功'
        })
      } else {
        // 批量删除
        await MonitorService.batchDeleteLoginLogs(selectedIds)
        toast({
          title: '删除成功',
          description: `成功删除 ${selectedIds.length} 条登录日志`
        })
      }

      setDeleteDialogOpen(false)
      setCurrentLoginLog(undefined)
      setSelectedIds([])
      await loadLoginLogs()
    } catch (error) {
      console.error('删除登录日志失败:', error)
      toast({
        title: '删除失败',
        description: '登录日志删除失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  /**
   * 处理清空日志
   */
  const handleClearLogs = () => {
    setClearDialogOpen(true)
  }

  /**
   * 确认清空日志
   */
  const handleConfirmClear = async () => {
    try {
      setDeleteLoading(true)
      await MonitorService.clearLoginLogs()
      
      toast({
        title: '清空成功',
        description: '所有登录日志已清空'
      })
      
      setClearDialogOpen(false)
      setSelectedIds([])
      await loadLoginLogs()
    } catch (error) {
      console.error('清空登录日志失败:', error)
      toast({
        title: '清空失败',
        description: '登录日志清空失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  /**
   * 处理导出
   */
  const handleExport = async () => {
    try {
      setLoading(true)
      const blob = await MonitorService.exportLoginLogs(searchParams)
      const filename = MonitorService.generateExportFilename('登录日志')
      MonitorService.downloadFile(blob, filename)
      
      toast({
        title: '导出成功',
        description: '登录日志导出成功'
      })
    } catch (error) {
      console.error('导出登录日志失败:', error)
      toast({
        title: '导出失败',
        description: '登录日志导出失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    loadLoginLogs()
  }

  // 初始化加载
  useEffect(() => {
    loadLoginLogs()
  }, [loadLoginLogs])

  return (
    <PagePermissionWrapper permission="monitor:loginlog:list">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">登录日志</h1>
          <p className="text-muted-foreground">
            查看和管理用户登录日志，包括登录记录、登录状态、设备信息等
          </p>
        </div>

        {/* 搜索表单 */}
        <LoginLogSearchForm
          searchParams={searchParams}
          onSearchParamsChange={setSearchParams}
          onSearch={handleSearch}
          onReset={handleReset}
          loading={loading}
        />

        {/* 主要内容 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <LogIn className="w-5 h-5 mr-2" />
                登录日志列表
                {pageData.total > 0 && (
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    (共 {pageData.total} 条)
                  </span>
                )}
              </CardTitle>

              <PermissionToolbar className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  刷新
                </Button>

                <PermissionButton
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  disabled={loading}
                  permissions={["monitor:loginlog:export"]}
                >
                  <Download className="w-4 h-4 mr-2" />
                  导出
                </PermissionButton>

                <PermissionButton
                  variant="outline"
                  size="sm"
                  onClick={handleBatchDelete}
                  disabled={loading || selectedIds.length === 0}
                  permissions={["monitor:loginlog:remove"]}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  批量删除
                </PermissionButton>

                <PermissionButton
                  variant="destructive"
                  size="sm"
                  onClick={handleClearLogs}
                  disabled={loading}
                  permissions={["monitor:loginlog:remove"]}
                >
                  <Eraser className="w-4 h-4 mr-2" />
                  清空日志
                </PermissionButton>
              </PermissionToolbar>
            </div>
          </CardHeader>

          <CardContent>
            {/* 统计信息 */}
            {pageData.total > 0 && (
              <Alert className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  当前共有 <strong>{pageData.total}</strong> 条登录日志，
                  {selectedIds.length > 0 && (
                    <>已选择 <strong>{selectedIds.length}</strong> 条，</>
                  )}
                  建议定期清理历史日志以优化系统性能。
                </AlertDescription>
              </Alert>
            )}

            {/* 数据表格 */}
            <LoginLogTable
              data={pageData}
              loading={loading}
              selectedIds={selectedIds}
              onSelectionChange={setSelectedIds}
              onView={handleView}
              onDelete={handleDelete}
              onPageChange={handlePageChange}
            />
          </CardContent>
        </Card>

        {/* 详情对话框 */}
        <LoginLogDetailDialog
          open={detailDialogOpen}
          onClose={() => setDetailDialogOpen(false)}
          loginLog={currentLoginLog}
        />

        {/* 删除确认对话框 */}
        <DeleteConfirmDialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleConfirmDelete}
          loading={deleteLoading}
        />

        {/* 清空确认对话框 */}
        {clearDialogOpen && (
          <DeleteConfirmDialog
            open={clearDialogOpen}
            onClose={() => setClearDialogOpen(false)}
            onConfirm={handleConfirmClear}
            loading={deleteLoading}
          />
        )}
      </div>
    </PagePermissionWrapper>
  )
}

export default LoginLogList
