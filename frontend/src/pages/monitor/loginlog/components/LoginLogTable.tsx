/**
 * 登录日志表格组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Button,
  Checkbox,
  Badge
} from '@/components/ui'
import { Eye, Trash2, Monitor, Globe } from 'lucide-react'
import { DataPagination } from '@/components/common'
import type { LoginLog, PageResult } from '@/types/monitor'
import { getLoginStatusLabel, getLoginStatusColor } from '@/types/monitor'

/**
 * 表格属性接口
 */
interface LoginLogTableProps {
  /** 数据 */
  data: PageResult<LoginLog>
  /** 加载状态 */
  loading?: boolean
  /** 选中的ID列表 */
  selectedIds: number[]
  /** 选择变化回调 */
  onSelectionChange: (ids: number[]) => void
  /** 查看详情回调 */
  onView: (loginLog: LoginLog) => void
  /** 删除回调 */
  onDelete: (loginLog: LoginLog) => void
  /** 分页变化回调 */
  onPageChange?: (page: number, pageSize?: number) => void
}

/**
 * 登录日志表格组件
 */
const LoginLogTable: React.FC<LoginLogTableProps> = ({
  data,
  loading = false,
  selectedIds,
  onSelectionChange,
  onView,
  onDelete,
  onPageChange
}) => {
  /**
   * 处理全选
   */
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(data.records.map(item => item.id))
    } else {
      onSelectionChange([])
    }
  }

  /**
   * 处理单选
   */
  const handleSelectOne = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id])
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id))
    }
  }

  /**
   * 格式化时间
   */
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '-'
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  /**
   * 截断文本
   */
  const truncateText = (text: string, maxLength: number = 20) => {
    if (!text) return '-'
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text
  }

  /**
   * 获取浏览器图标
   */
  const getBrowserIcon = (browser?: string) => {
    if (!browser) return <Monitor className="w-4 h-4" />
    
    const browserLower = browser.toLowerCase()
    if (browserLower.includes('chrome')) {
      return <Globe className="w-4 h-4 text-blue-500" />
    } else if (browserLower.includes('firefox')) {
      return <Globe className="w-4 h-4 text-orange-500" />
    } else if (browserLower.includes('safari')) {
      return <Globe className="w-4 h-4 text-blue-400" />
    } else if (browserLower.includes('edge')) {
      return <Globe className="w-4 h-4 text-blue-600" />
    }
    return <Monitor className="w-4 h-4" />
  }

  const isAllSelected = data.records.length > 0 && selectedIds.length === data.records.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.records.length

  return (
    <div className="space-y-4">
      {/* 表格 */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isIndeterminate}
                  onCheckedChange={handleSelectAll}
                  disabled={loading}
                />
              </TableHead>
              <TableHead>用户账号</TableHead>
              <TableHead>登录IP</TableHead>
              <TableHead>登录地点</TableHead>
              <TableHead>浏览器</TableHead>
              <TableHead>操作系统</TableHead>
              <TableHead>登录状态</TableHead>
              <TableHead>登录时间</TableHead>
              <TableHead>提示消息</TableHead>
              <TableHead className="w-32">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={10} className="h-32 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span className="ml-2">加载中...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : data.records.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="h-32 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              data.records.map((loginLog) => (
                <TableRow key={loginLog.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedIds.includes(loginLog.id)}
                      onCheckedChange={(checked) => handleSelectOne(loginLog.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{loginLog.userName || '-'}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">{loginLog.ipaddr || '-'}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{truncateText(loginLog.loginLocation || '-', 15)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {getBrowserIcon(loginLog.browser)}
                      <span className="ml-2 text-sm">{truncateText(loginLog.browser || '-', 12)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{truncateText(loginLog.os || '-', 12)}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getLoginStatusColor(loginLog.status)}>
                      {getLoginStatusLabel(loginLog.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{formatTime(loginLog.loginTime)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm max-w-32 truncate" title={loginLog.msg}>
                      {loginLog.msg || '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onView(loginLog)}
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        title="查看详情"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(loginLog)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="删除"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      <DataPagination
        current={data.pageNum}
        pageSize={data.pageSize}
        total={data.total}
        onPageChange={(page) => onPageChange?.(page, data.pageSize)}
        onPageSizeChange={(pageSize) => onPageChange?.(1, pageSize)}
      />
    </div>
  )
}

export default LoginLogTable
