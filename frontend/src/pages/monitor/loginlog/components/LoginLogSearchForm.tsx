/**
 * 登录日志搜索表单组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui'
import { Search, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react'
import type { LoginLogQueryRequest } from '@/types/monitor'
import { LOGIN_STATUS_LABELS } from '@/types/monitor'

/**
 * 搜索表单属性接口
 */
interface LoginLogSearchFormProps {
  /** 搜索参数 */
  searchParams: LoginLogQueryRequest
  /** 搜索参数变化回调 */
  onSearchParamsChange: (params: LoginLogQueryRequest) => void
  /** 搜索回调 */
  onSearch: () => void
  /** 重置回调 */
  onReset: () => void
  /** 加载状态 */
  loading?: boolean
}

/**
 * 登录日志搜索表单组件
 */
const LoginLogSearchForm: React.FC<LoginLogSearchFormProps> = ({
  searchParams,
  onSearchParamsChange,
  onSearch,
  onReset,
  loading = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  /**
   * 处理基础字段变化
   */
  const handleBasicFieldChange = (field: keyof LoginLogQueryRequest, value: any) => {
    onSearchParamsChange({
      ...searchParams,
      [field]: value
    })
  }

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    onSearch()
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    onReset()
  }

  /**
   * 处理回车搜索
   */
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 基础搜索条件 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userName">用户账号</Label>
              <Input
                id="userName"
                placeholder="请输入用户账号"
                value={searchParams.userName || ''}
                onChange={(e) => handleBasicFieldChange('userName', e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ipaddr">登录IP</Label>
              <Input
                id="ipaddr"
                placeholder="请输入登录IP地址"
                value={searchParams.ipaddr || ''}
                onChange={(e) => handleBasicFieldChange('ipaddr', e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">登录状态</Label>
              <Select
                value={searchParams.status?.toString() || 'all'}
                onValueChange={(value) => handleBasicFieldChange('status', value === 'all' ? undefined : Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择登录状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  {Object.entries(LOGIN_STATUS_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleSearch}
                disabled={loading}
                className="w-full"
              >
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
            </div>
          </div>

          {/* 高级搜索条件 */}
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-center">
                高级搜索
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4 ml-2" />
                ) : (
                  <ChevronDown className="w-4 h-4 ml-2" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="loginTimeStart">登录时间开始</Label>
                  <Input
                    id="loginTimeStart"
                    type="datetime-local"
                    value={searchParams.loginTimeStart || ''}
                    onChange={(e) => handleBasicFieldChange('loginTimeStart', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="loginTimeEnd">登录时间结束</Label>
                  <Input
                    id="loginTimeEnd"
                    type="datetime-local"
                    value={searchParams.loginTimeEnd || ''}
                    onChange={(e) => handleBasicFieldChange('loginTimeEnd', e.target.value)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={loading}
                className="min-w-[80px]"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                重置
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              支持按用户账号、登录IP、登录状态、登录时间等条件搜索
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default LoginLogSearchForm
