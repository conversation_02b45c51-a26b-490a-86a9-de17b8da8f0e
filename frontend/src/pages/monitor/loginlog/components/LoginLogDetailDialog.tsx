/**
 * 登录日志详情对话框组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  But<PERSON>,
  Badge,
  Separator,
  ScrollArea
} from '@/components/ui'
import { X, Copy, Check, Monitor, Globe, MapPin, Clock } from 'lucide-react'
import type { LoginLog } from '@/types/monitor'
import { getLoginStatusLabel, getLoginStatusColor } from '@/types/monitor'

/**
 * 详情对话框属性接口
 */
interface LoginLogDetailDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 登录日志数据 */
  loginLog?: LoginLog
}

/**
 * 登录日志详情对话框组件
 */
const LoginLogDetailDialog: React.FC<LoginLogDetailDialogProps> = ({
  open,
  onClose,
  loginLog
}) => {
  const [copiedField, setCopiedField] = React.useState<string | null>(null)

  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  /**
   * 格式化时间
   */
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '-'
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  /**
   * 渲染信息行
   */
  const renderInfoRow = (
    label: string, 
    value: string | number | undefined, 
    field?: string, 
    copyable = false,
    icon?: React.ReactNode
  ) => (
    <div className="grid grid-cols-3 gap-4 py-3">
      <div className="text-sm font-medium text-muted-foreground flex items-center">
        {icon && <span className="mr-2">{icon}</span>}
        {label}:
      </div>
      <div className="col-span-2 flex items-center justify-between">
        <span className="text-sm break-all">{value || '-'}</span>
        {copyable && value && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 ml-2"
            onClick={() => copyToClipboard(String(value), field || label)}
          >
            {copiedField === (field || label) ? (
              <Check className="h-3 w-3 text-green-600" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
          </Button>
        )}
      </div>
    </div>
  )

  /**
   * 获取浏览器图标和颜色
   */
  const getBrowserInfo = (browser?: string) => {
    if (!browser) return { icon: <Monitor className="w-4 h-4" />, color: 'text-gray-500' }
    
    const browserLower = browser.toLowerCase()
    if (browserLower.includes('chrome')) {
      return { icon: <Globe className="w-4 h-4" />, color: 'text-blue-500' }
    } else if (browserLower.includes('firefox')) {
      return { icon: <Globe className="w-4 h-4" />, color: 'text-orange-500' }
    } else if (browserLower.includes('safari')) {
      return { icon: <Globe className="w-4 h-4" />, color: 'text-blue-400' }
    } else if (browserLower.includes('edge')) {
      return { icon: <Globe className="w-4 h-4" />, color: 'text-blue-600' }
    }
    return { icon: <Monitor className="w-4 h-4" />, color: 'text-gray-500' }
  }

  if (!loginLog) return null

  const browserInfo = getBrowserInfo(loginLog.browser)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>登录日志详情</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh]">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Monitor className="w-5 h-5 mr-2" />
                基本信息
              </h3>
              <div className="space-y-1">
                {renderInfoRow('日志ID', loginLog.id)}
                {renderInfoRow('用户账号', loginLog.userName, 'userName', true)}
                <div className="grid grid-cols-3 gap-4 py-3">
                  <div className="text-sm font-medium text-muted-foreground flex items-center">
                    <Badge className="w-4 h-4 mr-2" />
                    登录状态:
                  </div>
                  <div className="col-span-2">
                    <Badge className={getLoginStatusColor(loginLog.status)}>
                      {getLoginStatusLabel(loginLog.status)}
                    </Badge>
                  </div>
                </div>
                {renderInfoRow(
                  '登录时间', 
                  formatTime(loginLog.loginTime), 
                  'loginTime', 
                  false,
                  <Clock className="w-4 h-4" />
                )}
              </div>
            </div>

            <Separator />

            {/* 网络信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                网络信息
              </h3>
              <div className="space-y-1">
                {renderInfoRow(
                  '登录IP', 
                  loginLog.ipaddr, 
                  'ipaddr', 
                  true,
                  <Globe className="w-4 h-4" />
                )}
                {renderInfoRow(
                  '登录地点', 
                  loginLog.loginLocation, 
                  'loginLocation', 
                  true,
                  <MapPin className="w-4 h-4" />
                )}
              </div>
            </div>

            <Separator />

            {/* 设备信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Monitor className="w-5 h-5 mr-2" />
                设备信息
              </h3>
              <div className="space-y-1">
                <div className="grid grid-cols-3 gap-4 py-3">
                  <div className="text-sm font-medium text-muted-foreground flex items-center">
                    <span className={`mr-2 ${browserInfo.color}`}>{browserInfo.icon}</span>
                    浏览器:
                  </div>
                  <div className="col-span-2 flex items-center justify-between">
                    <span className="text-sm break-all">{loginLog.browser || '-'}</span>
                    {loginLog.browser && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 ml-2"
                        onClick={() => copyToClipboard(loginLog.browser!, 'browser')}
                      >
                        {copiedField === 'browser' ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    )}
                  </div>
                </div>
                {renderInfoRow(
                  '操作系统', 
                  loginLog.os, 
                  'os', 
                  true,
                  <Monitor className="w-4 h-4" />
                )}
              </div>
            </div>

            <Separator />

            {/* 登录结果 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Badge className="w-5 h-5 mr-2" />
                登录结果
              </h3>
              <div className="space-y-1">
                {loginLog.msg && (
                  <div className={`p-4 rounded-lg border ${
                    loginLog.status === 0 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <h4 className={`text-sm font-medium ${
                        loginLog.status === 0 ? 'text-green-800' : 'text-red-800'
                      }`}>
                        提示消息
                      </h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(loginLog.msg!, 'msg')}
                      >
                        {copiedField === 'msg' ? (
                          <Check className="h-4 w-4 mr-2 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4 mr-2" />
                        )}
                        复制
                      </Button>
                    </div>
                    <div className={`text-sm ${
                      loginLog.status === 0 ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {loginLog.msg}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* 系统信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                系统信息
              </h3>
              <div className="space-y-1">
                {renderInfoRow('租户ID', loginLog.tenantId)}
                {renderInfoRow('创建时间', formatTime(loginLog.createTime))}
                {renderInfoRow('更新时间', formatTime(loginLog.updateTime))}
              </div>
            </div>
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default LoginLogDetailDialog
