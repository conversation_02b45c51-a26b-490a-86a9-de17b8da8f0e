/**
 * 系统信息监控页面
 * 
 * 基于权限管理模块的成功架构模式
 */

import React, { useState, useCallback, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Progress,
  Badge,
  Alert,
  AlertDescription
} from '@/components/ui'
import {
  RefreshCw,
  Server,
  Cpu,
  HardDrive,
  MemoryStick,
  Clock,
  Monitor,
  Activity,
  Zap
} from 'lucide-react'
import { PagePermissionWrapper } from '@/components/auth/PermissionWrapper'
import { useToast } from '@/hooks'
import { MonitorService } from '@/services/monitor'
import type { SystemInfo, SystemStats } from '@/types/monitor'

/**
 * 系统信息监控页面组件
 */
const SystemInfoPage: React.FC = () => {
  const { toast } = useToast()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null)
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null)

  /**
   * 加载系统信息
   */
  const loadSystemInfo = useCallback(async () => {
    try {
      setLoading(true)
      const [infoData, statsData] = await Promise.all([
        MonitorService.getSystemInfo(),
        MonitorService.getSystemStats()
      ])
      
      setSystemInfo(infoData)
      setSystemStats(statsData)
      setLastUpdateTime(new Date())
    } catch (error) {
      console.error('加载系统信息失败:', error)
      toast({
        title: '加载失败',
        description: '系统信息加载失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    loadSystemInfo()
  }

  /**
   * 格式化内存大小
   */
  const formatMemorySize = (bytes: number): string => {
    return MonitorService.formatFileSize(bytes)
  }

  /**
   * 格式化运行时间
   */
  const formatUptime = (uptime: number): string => {
    return MonitorService.formatUptime(uptime)
  }

  /**
   * 计算内存使用率
   */
  const calculateMemoryUsage = (used: number, max: number): number => {
    return MonitorService.formatMemoryUsage(used, max)
  }

  /**
   * 获取内存使用率颜色
   */
  const getMemoryUsageColor = (usage: number): string => {
    if (usage >= 90) return 'bg-red-500'
    if (usage >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  /**
   * 格式化启动时间
   */
  const formatStartTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 初始化加载和定时刷新
  useEffect(() => {
    loadSystemInfo()
    
    // 每30秒自动刷新一次
    const interval = setInterval(loadSystemInfo, 30000)
    
    return () => clearInterval(interval)
  }, [loadSystemInfo])

  const heapUsage = systemInfo ? calculateMemoryUsage(systemInfo.heapMemoryUsed, systemInfo.heapMemoryMax) : 0
  const nonHeapUsage = systemInfo ? calculateMemoryUsage(systemInfo.nonHeapMemoryUsed, systemInfo.nonHeapMemoryMax) : 0

  return (
    <PagePermissionWrapper permission="monitor:server:list">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">系统信息</h1>
            <p className="text-muted-foreground">
              实时监控服务器状态、性能指标和资源使用情况
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {lastUpdateTime && (
              <div className="text-sm text-muted-foreground">
                最后更新: {lastUpdateTime.toLocaleTimeString('zh-CN')}
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        {/* 自动刷新提示 */}
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            系统信息每30秒自动刷新一次，确保数据的实时性。
          </AlertDescription>
        </Alert>

        {/* 统计卡片 */}
        {systemStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日操作</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemStats.todayOperCount}</div>
                <p className="text-xs text-muted-foreground">
                  错误操作: {systemStats.errorOperCount}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日登录</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemStats.todayLoginCount}</div>
                <p className="text-xs text-muted-foreground">
                  失败登录: {systemStats.failedLoginCount}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">处理器</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemInfo?.availableProcessors || 0}
                </div>
                <p className="text-xs text-muted-foreground">可用核心数</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">运行时间</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">
                  {systemInfo ? formatUptime(systemInfo.uptime) : '-'}
                </div>
                <p className="text-xs text-muted-foreground">系统运行时长</p>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* JVM 信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Server className="w-5 h-5 mr-2" />
                JVM 信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemInfo ? (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">JVM名称</div>
                      <div className="text-sm">{systemInfo.jvmName}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">JVM版本</div>
                      <div className="text-sm">{systemInfo.jvmVersion}</div>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">JVM厂商</div>
                    <div className="text-sm">{systemInfo.jvmVendor}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">启动时间</div>
                    <div className="text-sm">{formatStartTime(systemInfo.startTime)}</div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 操作系统信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Monitor className="w-5 h-5 mr-2" />
                操作系统
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemInfo ? (
                <>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">系统名称</div>
                    <div className="text-sm">{systemInfo.osName}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">系统版本</div>
                      <div className="text-sm">{systemInfo.osVersion}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">系统架构</div>
                      <div className="text-sm">{systemInfo.osArch}</div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 内存使用情况 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MemoryStick className="w-5 h-5 mr-2" />
                堆内存使用情况
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemInfo ? (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已使用</span>
                      <span>{formatMemorySize(systemInfo.heapMemoryUsed)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>最大值</span>
                      <span>{formatMemorySize(systemInfo.heapMemoryMax)}</span>
                    </div>
                    <Progress 
                      value={heapUsage} 
                      className="w-full"
                    />
                    <div className="flex justify-between items-center">
                      <Badge 
                        variant="outline"
                        className={`${getMemoryUsageColor(heapUsage)} text-white border-0`}
                      >
                        {heapUsage}%
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        使用率
                      </span>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <HardDrive className="w-5 h-5 mr-2" />
                非堆内存使用情况
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemInfo ? (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已使用</span>
                      <span>{formatMemorySize(systemInfo.nonHeapMemoryUsed)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>最大值</span>
                      <span>{formatMemorySize(systemInfo.nonHeapMemoryMax)}</span>
                    </div>
                    <Progress 
                      value={nonHeapUsage} 
                      className="w-full"
                    />
                    <div className="flex justify-between items-center">
                      <Badge 
                        variant="outline"
                        className={`${getMemoryUsageColor(nonHeapUsage)} text-white border-0`}
                      >
                        {nonHeapUsage}%
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        使用率
                      </span>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </PagePermissionWrapper>
  )
}

export default SystemInfoPage
