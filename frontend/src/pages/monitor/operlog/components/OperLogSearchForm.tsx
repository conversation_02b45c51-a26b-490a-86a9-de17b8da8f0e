/**
 * 操作日志搜索表单组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui'
import { Search, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react'
import type { OperLogQueryRequest } from '@/types/monitor'
import { BUSINESS_TYPE_LABELS, OPER_STATUS_LABELS } from '@/types/monitor'

/**
 * 搜索表单属性接口
 */
interface OperLogSearchFormProps {
  /** 搜索参数 */
  searchParams: OperLogQueryRequest
  /** 搜索参数变化回调 */
  onSearchParamsChange: (params: OperLogQueryRequest) => void
  /** 搜索回调 */
  onSearch: () => void
  /** 重置回调 */
  onReset: () => void
  /** 加载状态 */
  loading?: boolean
}

/**
 * 操作日志搜索表单组件
 */
const OperLogSearchForm: React.FC<OperLogSearchFormProps> = ({
  searchParams,
  onSearchParamsChange,
  onSearch,
  onReset,
  loading = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  /**
   * 处理基础字段变化
   */
  const handleBasicFieldChange = (field: keyof OperLogQueryRequest, value: any) => {
    onSearchParamsChange({
      ...searchParams,
      [field]: value
    })
  }

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    onSearch()
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    onReset()
  }

  /**
   * 处理回车搜索
   */
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 基础搜索条件 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">操作模块</Label>
              <Input
                id="title"
                placeholder="请输入操作模块"
                value={searchParams.title || ''}
                onChange={(e) => handleBasicFieldChange('title', e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="operName">操作人员</Label>
              <Input
                id="operName"
                placeholder="请输入操作人员"
                value={searchParams.operName || ''}
                onChange={(e) => handleBasicFieldChange('operName', e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessType">业务类型</Label>
              <Select
                value={searchParams.businessType?.toString() || 'all'}
                onValueChange={(value) => handleBasicFieldChange('businessType', value === 'all' ? undefined : Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择业务类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {Object.entries(BUSINESS_TYPE_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">操作状态</Label>
              <Select
                value={searchParams.status?.toString() || 'all'}
                onValueChange={(value) => handleBasicFieldChange('status', value === 'all' ? undefined : Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择操作状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  {Object.entries(OPER_STATUS_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 高级搜索条件 */}
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-center">
                高级搜索
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4 ml-2" />
                ) : (
                  <ChevronDown className="w-4 h-4 ml-2" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="operTimeStart">操作时间开始</Label>
                  <Input
                    id="operTimeStart"
                    type="datetime-local"
                    value={searchParams.operTimeStart || ''}
                    onChange={(e) => handleBasicFieldChange('operTimeStart', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="operTimeEnd">操作时间结束</Label>
                  <Input
                    id="operTimeEnd"
                    type="datetime-local"
                    value={searchParams.operTimeEnd || ''}
                    onChange={(e) => handleBasicFieldChange('operTimeEnd', e.target.value)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleSearch}
                disabled={loading}
                className="min-w-[80px]"
              >
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={loading}
                className="min-w-[80px]"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                重置
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              支持按操作模块、操作人员、业务类型、操作状态等条件搜索
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default OperLogSearchForm
