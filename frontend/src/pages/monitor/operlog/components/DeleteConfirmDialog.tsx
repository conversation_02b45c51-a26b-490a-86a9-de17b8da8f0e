/**
 * 删除确认对话框组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui'
import { AlertTriangle, Trash2 } from 'lucide-react'
import type { OperLog } from '@/types/monitor'

/**
 * 删除确认对话框属性接口
 */
interface DeleteConfirmDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 确认删除回调 */
  onConfirm: () => void
  /** 单个操作日志 */
  operLog?: OperLog
  /** 多个操作日志 */
  operLogs?: OperLog[]
  /** 加载状态 */
  loading?: boolean
}

/**
 * 删除确认对话框组件
 */
const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  open,
  onClose,
  onConfirm,
  operLog,
  operLogs,
  loading = false
}) => {
  const isBatch = operLogs && operLogs.length > 0
  const count = isBatch ? operLogs.length : 1

  /**
   * 处理确认删除
   */
  const handleConfirm = () => {
    onConfirm()
  }

  /**
   * 处理取消
   */
  const handleCancel = () => {
    if (!loading) {
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            确认删除
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {isBatch ? (
                <>
                  您确定要删除选中的 <span className="font-semibold text-red-600">{count}</span> 条操作日志吗？
                </>
              ) : (
                <>
                  您确定要删除操作日志 <span className="font-semibold text-red-600">"{operLog?.title}"</span> 吗？
                </>
              )}
            </AlertDescription>
          </Alert>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  删除警告
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>删除操作不可恢复</li>
                    <li>删除后将无法查看相关操作记录</li>
                    <li>建议定期备份重要的操作日志</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {isBatch && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">
                即将删除的操作日志：
              </h4>
              <div className="max-h-32 overflow-y-auto">
                <ul className="text-sm text-blue-700 space-y-1">
                  {operLogs.slice(0, 5).map((log) => (
                    <li key={log.id} className="flex items-center">
                      <Trash2 className="w-3 h-3 mr-2 flex-shrink-0" />
                      <span className="truncate">
                        {log.title} - {log.operName} - {new Date(log.operTime).toLocaleString('zh-CN')}
                      </span>
                    </li>
                  ))}
                  {operLogs.length > 5 && (
                    <li className="text-blue-600 font-medium">
                      还有 {operLogs.length - 5} 条日志...
                    </li>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={loading}
            className="min-w-[80px]"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                删除中...
              </div>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                确认删除
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteConfirmDialog
