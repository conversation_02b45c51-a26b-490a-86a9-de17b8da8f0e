/**
 * 操作日志详情对话框组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  But<PERSON>,
  Badge,
  Separator,
  ScrollArea
} from '@/components/ui'
import { X, Copy, Check } from 'lucide-react'
import type { OperLog } from '@/types/monitor'
import { getBusinessTypeLabel, getOperStatusLabel, getOperStatusColor } from '@/types/monitor'

/**
 * 详情对话框属性接口
 */
interface OperLogDetailDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 操作日志数据 */
  operLog?: OperLog
}

/**
 * 操作日志详情对话框组件
 */
const OperLogDetailDialog: React.FC<OperLogDetailDialogProps> = ({
  open,
  onClose,
  operLog
}) => {
  const [copiedField, setCopiedField] = React.useState<string | null>(null)

  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  /**
   * 格式化时间
   */
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '-'
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  /**
   * 格式化JSON
   */
  const formatJson = (jsonStr?: string) => {
    if (!jsonStr) return '-'
    try {
      return JSON.stringify(JSON.parse(jsonStr), null, 2)
    } catch {
      return jsonStr
    }
  }

  /**
   * 渲染信息行
   */
  const renderInfoRow = (label: string, value: string | number | undefined, field?: string, copyable = false) => (
    <div className="grid grid-cols-3 gap-4 py-2">
      <div className="text-sm font-medium text-muted-foreground">{label}:</div>
      <div className="col-span-2 flex items-center justify-between">
        <span className="text-sm break-all">{value || '-'}</span>
        {copyable && value && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 ml-2"
            onClick={() => copyToClipboard(String(value), field || label)}
          >
            {copiedField === (field || label) ? (
              <Check className="h-3 w-3 text-green-600" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
          </Button>
        )}
      </div>
    </div>
  )

  if (!operLog) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>操作日志详情</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh]">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4">基本信息</h3>
              <div className="space-y-1">
                {renderInfoRow('日志ID', operLog.id)}
                {renderInfoRow('操作模块', operLog.title)}
                <div className="grid grid-cols-3 gap-4 py-2">
                  <div className="text-sm font-medium text-muted-foreground">业务类型:</div>
                  <div className="col-span-2">
                    <Badge variant="outline">
                      {getBusinessTypeLabel(operLog.businessType)}
                    </Badge>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2">
                  <div className="text-sm font-medium text-muted-foreground">操作状态:</div>
                  <div className="col-span-2">
                    <Badge className={getOperStatusColor(operLog.status)}>
                      {getOperStatusLabel(operLog.status)}
                    </Badge>
                  </div>
                </div>
                {renderInfoRow('操作人员', operLog.operName)}
                {renderInfoRow('部门名称', operLog.deptName)}
                {renderInfoRow('操作时间', formatTime(operLog.operTime))}
                {renderInfoRow('耗时', operLog.costTime ? `${operLog.costTime}ms` : undefined)}
              </div>
            </div>

            <Separator />

            {/* 请求信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-4">请求信息</h3>
              <div className="space-y-1">
                {renderInfoRow('请求方法', operLog.method, 'method', true)}
                {renderInfoRow('请求方式', operLog.requestMethod)}
                {renderInfoRow('请求URL', operLog.operUrl, 'operUrl', true)}
                {renderInfoRow('主机地址', operLog.operIp, 'operIp', true)}
                {renderInfoRow('操作地点', operLog.operLocation)}
              </div>
            </div>

            <Separator />

            {/* 请求参数 */}
            {operLog.operParam && (
              <>
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">请求参数</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(operLog.operParam!, 'operParam')}
                    >
                      {copiedField === 'operParam' ? (
                        <Check className="h-4 w-4 mr-2 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4 mr-2" />
                      )}
                      复制
                    </Button>
                  </div>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm whitespace-pre-wrap break-all">
                      {formatJson(operLog.operParam)}
                    </pre>
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* 返回参数 */}
            {operLog.jsonResult && (
              <>
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">返回参数</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(operLog.jsonResult!, 'jsonResult')}
                    >
                      {copiedField === 'jsonResult' ? (
                        <Check className="h-4 w-4 mr-2 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4 mr-2" />
                      )}
                      复制
                    </Button>
                  </div>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm whitespace-pre-wrap break-all">
                      {formatJson(operLog.jsonResult)}
                    </pre>
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* 错误信息 */}
            {operLog.errorMsg && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-red-600">错误信息</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(operLog.errorMsg!, 'errorMsg')}
                  >
                    {copiedField === 'errorMsg' ? (
                      <Check className="h-4 w-4 mr-2 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4 mr-2" />
                    )}
                    复制
                  </Button>
                </div>
                <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap break-all text-red-800">
                    {operLog.errorMsg}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default OperLogDetailDialog
