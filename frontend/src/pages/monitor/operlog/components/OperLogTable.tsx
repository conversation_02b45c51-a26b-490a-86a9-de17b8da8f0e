/**
 * 操作日志表格组件
 * 
 * 基于权限管理模块的成功架构模式
 */

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Button,
  Checkbox,
  Badge
} from '@/components/ui'
import { Eye, Trash2 } from 'lucide-react'
import { DataPagination } from '@/components/common/DataPagination'
import type { OperLog, PageResult } from '@/types/monitor'
import { getBusinessTypeLabel, getOperStatusLabel, getOperStatusColor } from '@/types/monitor'

/**
 * 表格属性接口
 */
interface OperLogTableProps {
  /** 数据 */
  data: PageResult<OperLog>
  /** 加载状态 */
  loading?: boolean
  /** 选中的ID列表 */
  selectedIds: number[]
  /** 选择变化回调 */
  onSelectionChange: (ids: number[]) => void
  /** 查看详情回调 */
  onView: (operLog: OperLog) => void
  /** 删除回调 */
  onDelete: (operLog: OperLog) => void
  /** 分页变化回调 */
  onPageChange?: (page: number, pageSize?: number) => void
}

/**
 * 操作日志表格组件
 */
const OperLogTable: React.FC<OperLogTableProps> = ({
  data,
  loading = false,
  selectedIds,
  onSelectionChange,
  onView,
  onDelete,
  onPageChange
}) => {
  /**
   * 处理全选
   */
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(data.records.map(item => item.id))
    } else {
      onSelectionChange([])
    }
  }

  /**
   * 处理单选
   */
  const handleSelectOne = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id])
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id))
    }
  }

  /**
   * 格式化时间
   */
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '-'
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  /**
   * 格式化耗时
   */
  const formatCostTime = (costTime?: number) => {
    if (!costTime) return '-'
    return `${costTime}ms`
  }

  /**
   * 截断文本
   */
  const truncateText = (text: string, maxLength: number = 30) => {
    if (!text) return '-'
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text
  }

  const isAllSelected = data.records.length > 0 && selectedIds.length === data.records.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < data.records.length

  return (
    <div className="space-y-4">
      {/* 表格 */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isIndeterminate}
                  onCheckedChange={handleSelectAll}
                  disabled={loading}
                />
              </TableHead>
              <TableHead>操作模块</TableHead>
              <TableHead>业务类型</TableHead>
              <TableHead>操作人员</TableHead>
              <TableHead>主机地址</TableHead>
              <TableHead>操作地点</TableHead>
              <TableHead>操作状态</TableHead>
              <TableHead>操作时间</TableHead>
              <TableHead>耗时</TableHead>
              <TableHead className="w-32">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={10} className="h-32 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span className="ml-2">加载中...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : data.records.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="h-32 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              data.records.map((operLog) => (
                <TableRow key={operLog.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedIds.includes(operLog.id)}
                      onCheckedChange={(checked) => handleSelectOne(operLog.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{operLog.title || '-'}</div>
                    <div className="text-sm text-muted-foreground">
                      {truncateText(operLog.method, 20)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getBusinessTypeLabel(operLog.businessType)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{operLog.operName || '-'}</div>
                    {operLog.deptName && (
                      <div className="text-sm text-muted-foreground">{operLog.deptName}</div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">{operLog.operIp || '-'}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{operLog.operLocation || '-'}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getOperStatusColor(operLog.status)}>
                      {getOperStatusLabel(operLog.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{formatTime(operLog.operTime)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm font-mono">{formatCostTime(operLog.costTime)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onView(operLog)}
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        title="查看详情"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(operLog)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="删除"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      <DataPagination
        current={data.pageNum}
        pageSize={data.pageSize}
        total={data.total}
        onPageChange={(page) => onPageChange?.(page, data.pageSize)}
        onPageSizeChange={(pageSize) => onPageChange?.(1, pageSize)}
      />
    </div>
  )
}

export default OperLogTable
