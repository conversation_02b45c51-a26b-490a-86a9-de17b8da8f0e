import { useState, useCallback, useEffect, useMemo } from 'react'
import { useSearchParams, useLocation } from 'react-router-dom'
import { getPresetTimeRange } from '@/utils/financial'
import { 
  type TimeRange, 
  type FinancialStatsCategory, 
  type FinancialPageConfig,
  TimeRangePreset
} from '@/types/financial'

/**
 * 财务页面状态接口
 */
export interface FinancialPageState {
  /** 时间范围 */
  timeRange: TimeRange
  /** 是否包含主播数据 */
  includeAnchor: boolean
  /** 当前活跃的标签页 */
  activeTab: FinancialStatsCategory
  /** 是否启用自动刷新 */
  autoRefresh: boolean
  /** 时间范围预设 */
  timeRangePreset: TimeRangePreset
}

/**
 * URL参数键名常量
 */
const URL_PARAMS = {
  START_TIME: 'startTime',
  END_TIME: 'endTime',
  INCLUDE_ANCHOR: 'includeAnchor',
  ACTIVE_TAB: 'tab',
  AUTO_REFRESH: 'autoRefresh',
  TIME_PRESET: 'preset'
} as const

/**
 * 默认页面配置
 */
const DEFAULT_CONFIG: FinancialPageConfig = {
  defaultTimeRange: TimeRangePreset.TODAY,
  defaultIncludeAnchor: true,
  autoRefreshInterval: 30000,
  enableAutoRefresh: false,
  pageSize: 20,
  showTrend: true
}

/**
 * 财务页面状态管理Hook
 * 提供页面状态管理和URL同步功能
 */
export const useFinancialPageState = (config: Partial<FinancialPageConfig> = {}) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const location = useLocation()
  
  const finalConfig = useMemo(() => ({
    ...DEFAULT_CONFIG,
    ...config
  }), [config])

  /**
   * 从URL参数解析状态
   */
  const parseStateFromURL = useCallback((): FinancialPageState => {
    const startTime = searchParams.get(URL_PARAMS.START_TIME)
    const endTime = searchParams.get(URL_PARAMS.END_TIME)
    const includeAnchor = searchParams.get(URL_PARAMS.INCLUDE_ANCHOR)
    const activeTab = searchParams.get(URL_PARAMS.ACTIVE_TAB)
    const autoRefresh = searchParams.get(URL_PARAMS.AUTO_REFRESH)
    const timePreset = searchParams.get(URL_PARAMS.TIME_PRESET)

    // 解析时间范围预设
    const preset = (timePreset as TimeRangePreset) || finalConfig.defaultTimeRange
    
    // 如果URL中有具体的时间参数，使用URL参数；否则使用预设
    let timeRange: TimeRange
    if (startTime && endTime) {
      timeRange = { startTime, endTime }
    } else {
      timeRange = getPresetTimeRange(preset)
    }

    return {
      timeRange,
      includeAnchor: includeAnchor === 'false' ? false : finalConfig.defaultIncludeAnchor,
      activeTab: (activeTab as FinancialStatsCategory) || 'total' as FinancialStatsCategory,
      autoRefresh: autoRefresh === 'true' ? true : finalConfig.enableAutoRefresh,
      timeRangePreset: preset
    }
  }, [searchParams, finalConfig])

  /**
   * 初始化状态
   */
  const [state, setState] = useState<FinancialPageState>(() => parseStateFromURL())

  /**
   * 将状态同步到URL
   */
  const syncStateToURL = useCallback((newState: FinancialPageState) => {
    const params = new URLSearchParams(searchParams)
    
    // 设置时间参数
    params.set(URL_PARAMS.START_TIME, newState.timeRange.startTime)
    params.set(URL_PARAMS.END_TIME, newState.timeRange.endTime)
    params.set(URL_PARAMS.TIME_PRESET, newState.timeRangePreset)
    
    // 设置其他参数
    params.set(URL_PARAMS.INCLUDE_ANCHOR, newState.includeAnchor.toString())
    params.set(URL_PARAMS.ACTIVE_TAB, newState.activeTab)
    params.set(URL_PARAMS.AUTO_REFRESH, newState.autoRefresh.toString())
    
    // 更新URL，但不触发页面刷新
    setSearchParams(params, { replace: true })
  }, [searchParams, setSearchParams])

  /**
   * 查找匹配的时间预设
   */
  const findMatchingPreset = useCallback((timeRange: TimeRange): TimeRangePreset | null => {
    const presets = [
      TimeRangePreset.TODAY,
      TimeRangePreset.YESTERDAY,
      TimeRangePreset.THIS_WEEK,
      TimeRangePreset.LAST_WEEK,
      TimeRangePreset.THIS_MONTH,
      TimeRangePreset.LAST_MONTH
    ]

    for (const preset of presets) {
      const presetRange = getPresetTimeRange(preset)
      if (presetRange.startTime === timeRange.startTime && 
          presetRange.endTime === timeRange.endTime) {
        return preset
      }
    }

    return null
  }, [])

  /**
   * 更新状态的通用方法
   */
  const updateState = useCallback((updates: Partial<FinancialPageState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates }
      
      // 如果时间范围发生变化，检查是否匹配某个预设
      if (updates.timeRange) {
        const matchedPreset = findMatchingPreset(updates.timeRange)
        if (matchedPreset) {
          newState.timeRangePreset = matchedPreset
        } else {
          newState.timeRangePreset = TimeRangePreset.CUSTOM
        }
      }
      
      return newState
    })
  }, [findMatchingPreset])

  /**
   * 设置时间范围
   */
  const setTimeRange = useCallback((timeRange: TimeRange) => {
    updateState({ timeRange })
  }, [updateState])

  /**
   * 设置时间范围预设
   */
  const setTimeRangePreset = useCallback((preset: TimeRangePreset) => {
    const timeRange = getPresetTimeRange(preset)
    updateState({ timeRange, timeRangePreset: preset })
  }, [updateState])

  /**
   * 设置是否包含主播数据
   */
  const setIncludeAnchor = useCallback((includeAnchor: boolean) => {
    updateState({ includeAnchor })
  }, [updateState])

  /**
   * 设置活跃标签页
   */
  const setActiveTab = useCallback((activeTab: FinancialStatsCategory) => {
    updateState({ activeTab })
  }, [updateState])

  /**
   * 设置自动刷新
   */
  const setAutoRefresh = useCallback((autoRefresh: boolean) => {
    updateState({ autoRefresh })
  }, [updateState])

  /**
   * 重置状态到默认值
   */
  const resetState = useCallback(() => {
    const defaultState: FinancialPageState = {
      timeRange: getPresetTimeRange(finalConfig.defaultTimeRange),
      includeAnchor: finalConfig.defaultIncludeAnchor,
      activeTab: 'total' as FinancialStatsCategory,
      autoRefresh: finalConfig.enableAutoRefresh,
      timeRangePreset: finalConfig.defaultTimeRange
    }
    
    setState(defaultState)
    syncStateToURL(defaultState)
  }, [finalConfig, syncStateToURL])

  /**
   * 获取当前状态的查询参数
   */
  const getQueryParams = useCallback(() => {
    return {
      startTime: state.timeRange.startTime,
      endTime: state.timeRange.endTime,
      includeAnchor: state.includeAnchor
    }
  }, [state])

  /**
   * 获取状态摘要信息
   */
  const getStateSummary = useCallback(() => {
    return {
      timeRangeLabel: getTimeRangeLabel(state.timeRangePreset, state.timeRange),
      includeAnchorLabel: state.includeAnchor ? '包含主播数据' : '不包含主播数据',
      activeTabLabel: getTabLabel(state.activeTab),
      autoRefreshLabel: state.autoRefresh ? '自动刷新已启用' : '自动刷新已禁用'
    }
  }, [state])

  /**
   * 监听状态变化，同步到URL
   */
  useEffect(() => {
    syncStateToURL(state)
  }, [state, syncStateToURL])

  /**
   * 监听URL变化，同步状态（仅在URL直接变化时，如浏览器前进后退）
   */
  useEffect(() => {
    const startTime = searchParams.get(URL_PARAMS.START_TIME)
    const endTime = searchParams.get(URL_PARAMS.END_TIME)
    const includeAnchor = searchParams.get(URL_PARAMS.INCLUDE_ANCHOR)
    const activeTab = searchParams.get(URL_PARAMS.ACTIVE_TAB)
    const autoRefresh = searchParams.get(URL_PARAMS.AUTO_REFRESH)
    const timePreset = searchParams.get(URL_PARAMS.TIME_PRESET)

    // 解析时间范围预设
    const preset = (timePreset as TimeRangePreset) || finalConfig.defaultTimeRange
    
    // 如果URL中有具体的时间参数，使用URL参数；否则使用预设
    let timeRange: TimeRange
    if (startTime && endTime) {
      timeRange = { startTime, endTime }
    } else {
      timeRange = getPresetTimeRange(preset)
    }

    const newState: FinancialPageState = {
      timeRange,
      includeAnchor: includeAnchor === 'false' ? false : finalConfig.defaultIncludeAnchor,
      activeTab: (activeTab as FinancialStatsCategory) || 'total' as FinancialStatsCategory,
      autoRefresh: autoRefresh === 'true' ? true : finalConfig.enableAutoRefresh,
      timeRangePreset: preset
    }

    // 使用深度比较避免不必要的更新
    const stateChanged = (
      newState.timeRange.startTime !== state.timeRange.startTime ||
      newState.timeRange.endTime !== state.timeRange.endTime ||
      newState.includeAnchor !== state.includeAnchor ||
      newState.activeTab !== state.activeTab ||
      newState.autoRefresh !== state.autoRefresh ||
      newState.timeRangePreset !== state.timeRangePreset
    )
    
    if (stateChanged) {
      setState(newState)
    }
  }, [location.search]) // 只依赖 location.search，避免循环

  return {
    // 状态
    state,
    
    // 状态更新方法
    setTimeRange,
    setTimeRangePreset,
    setIncludeAnchor,
    setActiveTab,
    setAutoRefresh,
    updateState,
    resetState,
    
    // 工具方法
    getQueryParams,
    getStateSummary,
    
    // 配置
    config: finalConfig
  }
}

/**
 * 获取时间范围标签
 */
function getTimeRangeLabel(preset: TimeRangePreset, timeRange: TimeRange): string {
  switch (preset) {
    case TimeRangePreset.TODAY:
      return '今天'
    case TimeRangePreset.YESTERDAY:
      return '昨天'
    case TimeRangePreset.THIS_WEEK:
      return '本周'
    case TimeRangePreset.LAST_WEEK:
      return '上周'
    case TimeRangePreset.THIS_MONTH:
      return '本月'
    case TimeRangePreset.LAST_MONTH:
      return '上月'
    case TimeRangePreset.CUSTOM:
      return `${timeRange.startTime.split(' ')[0]} 至 ${timeRange.endTime.split(' ')[0]}`
    default:
      return '自定义时间'
  }
}

/**
 * 获取标签页标签
 */
function getTabLabel(tab: FinancialStatsCategory): string {
  switch (tab) {
    case 'user':
      return '用户相关统计'
    case 'anchor':
      return '主播相关统计'
    case 'total':
      return '合计统计'
    case 'business':
      return '其他业务统计'
    default:
      return '未知分类'
  }
}

/**
 * 页面状态持久化Hook
 * 将页面状态保存到localStorage
 */
export const useFinancialPageStatePersistence = (
  key: string = 'financial_page_state'
) => {
  /**
   * 保存状态到localStorage
   */
  const saveState = useCallback((state: FinancialPageState) => {
    try {
      const stateToSave = {
        ...state,
        // 不保存时间范围，因为时间是动态的
        timeRange: undefined
      }
      localStorage.setItem(key, JSON.stringify(stateToSave))
    } catch (error) {
      console.warn('保存页面状态失败:', error)
    }
  }, [key])

  /**
   * 从localStorage加载状态
   */
  const loadState = useCallback((): Partial<FinancialPageState> | null => {
    try {
      const saved = localStorage.getItem(key)
      if (!saved) return null
      
      const parsed = JSON.parse(saved)
      return {
        includeAnchor: parsed.includeAnchor,
        activeTab: parsed.activeTab,
        autoRefresh: parsed.autoRefresh,
        timeRangePreset: parsed.timeRangePreset
      }
    } catch (error) {
      console.warn('加载页面状态失败:', error)
      return null
    }
  }, [key])

  /**
   * 清除保存的状态
   */
  const clearState = useCallback(() => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('清除页面状态失败:', error)
    }
  }, [key])

  return {
    saveState,
    loadState,
    clearState
  }
}