import { useState, useCallback, useEffect, useRef, useMemo } from 'react'
import { FinancialService } from '../services/financial.ts'
import type {
    FinancialStatsRequest,
    GroupedFinancialStatsResponse,
    FinancialDataState
} from '../types/financial.ts'
import { ErrorHandler, AppError } from '../utils/error.ts'

/**
 * 财务数据Hook选项
 */
interface UseFinancialStatsOptions {
    /** 是否手动触发 */
    manual?: boolean
    /** 自动刷新间隔（毫秒），0表示不自动刷新 */
    refreshInterval?: number
    /** 成功回调 */
    onSuccess?: (data: GroupedFinancialStatsResponse) => void
    /** 错误回调 */
    onError?: (error: AppError) => void
    /** 是否启用缓存 */
    enableCache?: boolean
    /** 缓存时间（毫秒），默认5分钟 */
    cacheTime?: number
}

/**
 * 缓存项接口
 */
interface CacheItem {
    data: GroupedFinancialStatsResponse
    timestamp: number
    key: string
}

/**
 * 财务统计数据Hook
 * 提供财务数据的获取、缓存、自动刷新等功能
 */
export const useFinancialStats = (
    request: FinancialStatsRequest,
    options: UseFinancialStatsOptions = {}
) => {
    const {
        manual = false,
        refreshInterval = 0,
        onSuccess,
        onError,
        enableCache = true,
        cacheTime = 5 * 60 * 1000, // 5分钟
    } = options

    const [state, setState] = useState<FinancialDataState>({
        data: null,
        loading: false,
        error: null,
        lastUpdated: null,
    })

    const refreshTimerRef = useRef<NodeJS.Timeout | null>(null)
    const cacheRef = useRef<Map<string, CacheItem>>(new Map())
    const abortControllerRef = useRef<AbortController | null>(null)

    // 使用 useMemo 稳定请求参数
    const stableRequest = useMemo(() => ({
        startTime: request.startTime,
        endTime: request.endTime,
        includeAnchor: request.includeAnchor
    }), [request.startTime, request.endTime, request.includeAnchor])

    /**
     * 生成缓存键
     */
    const getCacheKey = useCallback((req: FinancialStatsRequest): string => {
        return `financial_stats_${req.startTime}_${req.endTime}_${req.includeAnchor}`
    }, [])

    /**
     * 获取缓存数据
     */
    const getCachedData = useCallback((key: string): GroupedFinancialStatsResponse | null => {
        if (!enableCache) return null

        const cached = cacheRef.current.get(key)
        if (!cached) return null

        const now = Date.now()
        if (now - cached.timestamp > cacheTime) {
            cacheRef.current.delete(key)
            return null
        }

        return cached.data
    }, [enableCache, cacheTime])

    /**
     * 设置缓存数据
     */
    const setCachedData = useCallback((key: string, data: GroupedFinancialStatsResponse) => {
        if (!enableCache) return

        cacheRef.current.set(key, {
            data,
            timestamp: Date.now(),
            key,
        })

        // 清理过期缓存
        const now = Date.now()
        for (const [cacheKey, item] of cacheRef.current.entries()) {
            if (now - item.timestamp > cacheTime) {
                cacheRef.current.delete(cacheKey)
            }
        }
    }, [enableCache, cacheTime])

    /**
     * 验证请求参数
     */
    const validateRequest = useCallback((req: FinancialStatsRequest): void => {
        const validation = FinancialService.validateTimeRange(req.startTime, req.endTime)
        if (!validation.valid) {
            throw new Error(validation.error)
        }
    }, [])

    /**
     * 执行数据获取
     */
    const fetchData = useCallback(async (req: FinancialStatsRequest): Promise<GroupedFinancialStatsResponse> => {
        // 取消之前的请求
        if (abortControllerRef.current) {
            abortControllerRef.current.abort()
        }

        // 创建新的AbortController
        abortControllerRef.current = new AbortController()

        try {
            // 验证请求参数
            validateRequest(req)

            // 检查缓存
            const cacheKey = getCacheKey(req)
            const cachedData = getCachedData(cacheKey)
            if (cachedData) {
                console.log('🎯 使用缓存数据:', cacheKey)
                return cachedData
            }

            setState(prev => ({ ...prev, loading: true, error: null }))

            console.log('🚀 获取财务数据:', req)
            const data = await FinancialService.getFinancialStats(req)

            // 检查请求是否被取消
            if (abortControllerRef.current?.signal.aborted) {
                throw new Error('请求已取消')
            }

            // 缓存数据
            setCachedData(cacheKey, data)

            setState({
                data,
                loading: false,
                error: null,
                lastUpdated: new Date().toISOString(),
            })

            onSuccess?.(data)
            return data
        } catch (error) {
            // 如果请求被取消，不更新状态
            if (abortControllerRef.current?.signal.aborted) {
                return Promise.reject(error)
            }

            const appError = ErrorHandler.handle(error)
            setState(prev => ({
                ...prev,
                loading: false,
                error: appError,
            }))

            onError?.(appError)
            throw appError
        }
    }, [validateRequest, getCacheKey, getCachedData, setCachedData, onSuccess, onError])

    /**
     * 手动刷新数据
     */
    const refetch = useCallback(async (): Promise<GroupedFinancialStatsResponse | undefined> => {
        try {
            return await fetchData(stableRequest)
        } catch (error) {
            console.error('刷新财务数据失败:', error)
            return undefined
        }
    }, [fetchData, stableRequest])

    /**
     * 强制刷新（清除缓存）
     */
    const forceRefresh = useCallback(async (): Promise<GroupedFinancialStatsResponse | undefined> => {
        const cacheKey = getCacheKey(stableRequest)
        cacheRef.current.delete(cacheKey)
        return refetch()
    }, [getCacheKey, stableRequest, refetch])

    /**
     * 清除所有缓存
     */
    const clearCache = useCallback(() => {
        cacheRef.current.clear()
    }, [])

    /**
     * 设置自动刷新
     */
    const setupAutoRefresh = useCallback(() => {
        if (refreshInterval > 0) {
            refreshTimerRef.current = setInterval(() => {
                console.log('🔄 自动刷新财务数据')
                refetch()
            }, refreshInterval)
        }
    }, [refreshInterval, refetch])

    /**
     * 清除自动刷新
     */
    const clearAutoRefresh = useCallback(() => {
        if (refreshTimerRef.current) {
            clearInterval(refreshTimerRef.current)
            refreshTimerRef.current = null
        }
    }, [])

    /**
     * 取消当前请求
     */
    const cancel = useCallback(() => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort()
            abortControllerRef.current = null
        }
        setState(prev => ({ ...prev, loading: false }))
    }, [])

    /**
     * 重置状态
     */
    const reset = useCallback(() => {
        cancel()
        clearAutoRefresh()
        setState({
            data: null,
            loading: false,
            error: null,
            lastUpdated: null,
        })
    }, [cancel, clearAutoRefresh])

    // 自动执行初始请求
    useEffect(() => {
        if (!manual) {
            fetchData(stableRequest)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [manual, stableRequest.startTime, stableRequest.endTime, stableRequest.includeAnchor])

    // 设置自动刷新
    useEffect(() => {
        setupAutoRefresh()
        return clearAutoRefresh
    }, [setupAutoRefresh, clearAutoRefresh])

    // 清理资源
    useEffect(() => {
        return () => {
            cancel()
            clearAutoRefresh()
        }
    }, [cancel, clearAutoRefresh])

    return {
        ...state,
        refetch,
        forceRefresh,
        clearCache,
        cancel,
        reset,
    }
}

/**
 * 今日财务数据Hook
 * 专门用于获取今日财务数据的简化Hook
 */
export const useTodayFinancialStats = (
    includeAnchor: boolean = true,
    options: Omit<UseFinancialStatsOptions, 'manual'> = {}
) => {
    const request = FinancialService.buildTodayRequest(includeAnchor)

    return useFinancialStats(request, {
        ...options,
        manual: false,
    })
}

/**
 * 财务数据对比Hook
 * 用于对比两个时间段的财务数据
 */
export const useFinancialStatsComparison = (
    currentRequest: FinancialStatsRequest,
    previousRequest: FinancialStatsRequest,
    options: UseFinancialStatsOptions = {}
) => {
    const currentStats = useFinancialStats(currentRequest, options)
    const previousStats = useFinancialStats(previousRequest, options)

    /**
     * 计算变化趋势
     */
    const calculateTrend = useCallback((
        currentValue: number,
        previousValue: number
    ): {
        direction: 'up' | 'down' | 'stable'
        value: number
        percentage: number
    } => {
        const diff = currentValue - previousValue
        const percentage = previousValue === 0 ? 0 : (diff / previousValue) * 100

        return {
            direction: diff > 0 ? 'up' : diff < 0 ? 'down' : 'stable',
            value: diff,
            percentage: Math.abs(percentage),
        }
    }, [])

    /**
     * 获取对比数据
     */
    const getComparisonData = useCallback(() => {
        if (!currentStats.data || !previousStats.data) {
            return null
        }

        // 这里可以根据需要实现具体的对比逻辑
        // 例如对比总收入、用户数量等关键指标
        return {
            current: currentStats.data,
            previous: previousStats.data,
            trends: {
                // 示例：对比用户相关统计的第一项
                userStats: currentStats.data.userStats.length > 0 && previousStats.data.userStats.length > 0
                    ? calculateTrend(
                        currentStats.data.userStats[0].statValue,
                        previousStats.data.userStats[0].statValue
                    )
                    : null,
            },
        }
    }, [currentStats.data, previousStats.data, calculateTrend])

    return {
        current: currentStats,
        previous: previousStats,
        comparison: getComparisonData(),
        loading: currentStats.loading || previousStats.loading,
        error: currentStats.error || previousStats.error,
    }
}