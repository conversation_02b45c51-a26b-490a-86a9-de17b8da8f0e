import { useState, useCallback, useRef, useEffect } from 'react'
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>, AppError } from '../utils/error.ts'

/**
 * 请求状态接口
 */
interface RequestState<T> {
  data: T | null
  loading: boolean
  error: AppError | null
}

/**
 * 请求选项接口
 */
interface RequestOptions {
  manual?: boolean // 是否手动触发
  defaultParams?: any[] // 默认参数
  onSuccess?: (data: any, params: any[]) => void // 成功回调
  onError?: (error: AppError, params: any[]) => void // 错误回调
  retryCount?: number // 重试次数
  retryDelay?: number // 重试延迟（毫秒）
  debounceWait?: number // 防抖延迟（毫秒）
  throttleWait?: number // 节流延迟（毫秒）
}

/**
 * 请求Hook
 */
export const useRequest = <T = any, P extends any[] = any[]>(
  service: (...args: P) => Promise<T>,
  options: RequestOptions = {}
) => {
  const {
    manual = false,
    defaultParams = [] as unknown as P,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000,
    debounceWait,
    throttleWait,
  } = options

  const [state, setState] = useState<RequestState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const retryCountRef = useRef(0)
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const throttleTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastCallTimeRef = useRef(0)

  /**
   * 执行请求
   */
  const runAsync = useCallback(
    async (...params: P): Promise<T> => {
      setState(prev => ({ ...prev, loading: true, error: null }))

      try {
        const data = await service(...params)
        setState({ data, loading: false, error: null })
        retryCountRef.current = 0
        onSuccess?.(data, params)
        return data
      } catch (error) {
        const appError = ErrorHandler.handle(error)
        
        // 如果还有重试次数，进行重试
        if (retryCountRef.current < retryCount) {
          retryCountRef.current++
          console.log(`🔄 请求重试 ${retryCountRef.current}/${retryCount}`)
          
          return new Promise((resolve, reject) => {
            setTimeout(async () => {
              try {
                const result = await runAsync(...params)
                resolve(result)
              } catch (retryError) {
                reject(retryError)
              }
            }, retryDelay)
          })
        }

        setState({ data: null, loading: false, error: appError })
        onError?.(appError, params)
        throw appError
      }
    },
    [service, onSuccess, onError, retryCount, retryDelay]
  )

  /**
   * 防抖执行
   */
  const debouncedRun = useCallback(
    (...params: P) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      debounceTimerRef.current = setTimeout(() => {
        runAsync(...params)
      }, debounceWait)
    },
    [runAsync, debounceWait]
  )

  /**
   * 节流执行
   */
  const throttledRun = useCallback(
    (...params: P) => {
      const now = Date.now()
      
      if (now - lastCallTimeRef.current >= (throttleWait || 0)) {
        lastCallTimeRef.current = now
        runAsync(...params)
      } else if (!throttleTimerRef.current) {
        const remainingTime = (throttleWait || 0) - (now - lastCallTimeRef.current)
        throttleTimerRef.current = setTimeout(() => {
          throttleTimerRef.current = null
          lastCallTimeRef.current = Date.now()
          runAsync(...params)
        }, remainingTime)
      }
    },
    [runAsync, throttleWait]
  )

  /**
   * 执行请求（根据配置选择执行方式）
   */
  const run = useCallback(
    (...params: P) => {
      if (debounceWait) {
        debouncedRun(...params)
      } else if (throttleWait) {
        throttledRun(...params)
      } else {
        runAsync(...params)
      }
    },
    [runAsync, debouncedRun, throttledRun, debounceWait, throttleWait]
  )

  /**
   * 刷新（使用上次的参数重新请求）
   */
  const refresh = useCallback(() => {
    run(...(defaultParams as P))
  }, [run, defaultParams])

  /**
   * 取消请求
   */
  const cancel = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
      debounceTimerRef.current = null
    }
    if (throttleTimerRef.current) {
      clearTimeout(throttleTimerRef.current)
      throttleTimerRef.current = null
    }
    setState(prev => ({ ...prev, loading: false }))
  }, [])

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    cancel()
    setState({ data: null, loading: false, error: null })
    retryCountRef.current = 0
  }, [cancel])

  // 自动执行
  useEffect(() => {
    if (!manual) {
      run(...(defaultParams as P))
    }
  }, [manual, run, defaultParams])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
      if (throttleTimerRef.current) {
        clearTimeout(throttleTimerRef.current)
      }
    }
  }, [])

  return {
    ...state,
    run,
    runAsync,
    refresh,
    cancel,
    reset,
  }
}

/**
 * 分页请求Hook
 */
export const usePagination = <T = any>(
  service: (params: any) => Promise<{ records: T[]; total: number; current: number; size: number }>,
  options: RequestOptions & {
    defaultPageSize?: number
    defaultCurrent?: number
  } = {}
) => {
  const { defaultPageSize = 10, defaultCurrent = 1, ...restOptions } = options
  
  const [pagination, setPagination] = useState({
    current: defaultCurrent,
    pageSize: defaultPageSize,
    total: 0,
  })

  const { data, loading, error, run, runAsync, refresh } = useRequest(
    async (params: any = {}) => {
      const result = await service({
        page: pagination.current,
        size: pagination.pageSize,
        ...params,
      })
      
      setPagination(prev => ({
        ...prev,
        total: result.total,
        current: result.current,
      }))
      
      return result
    },
    {
      ...restOptions,
      manual: true,
    }
  )

  /**
   * 改变页码
   */
  const changePage = useCallback((page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }))
  }, [])

  /**
   * 改变页面大小
   */
  const changePageSize = useCallback((pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize,
    }))
  }, [])

  // 当分页参数变化时重新请求
  useEffect(() => {
    run()
  }, [pagination.current, pagination.pageSize, run])

  return {
    data: data?.records || [],
    loading,
    error,
    pagination,
    run,
    runAsync,
    refresh,
    changePage,
    changePageSize,
  }
}
