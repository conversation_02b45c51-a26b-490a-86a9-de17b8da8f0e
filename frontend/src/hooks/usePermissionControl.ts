/**
 * 权限控制Hook
 * 
 * 提供便捷的权限检查和控制方法
 */

import { useMemo } from 'react'
import { usePermission } from '@/stores/auth'

export interface PermissionControlOptions {
  /**
   * 需要的权限列表
   */
  permissions?: string[]
  
  /**
   * 需要的角色列表
   */
  roles?: string[]
  
  /**
   * 权限检查模式
   * - 'all': 需要拥有所有指定权限（默认）
   * - 'any': 拥有任一权限即可
   */
  permissionMode?: 'all' | 'any'
  
  /**
   * 角色检查模式
   * - 'all': 需要拥有所有指定角色
   * - 'any': 拥有任一角色即可（默认）
   */
  roleMode?: 'all' | 'any'
}

export interface PermissionControlResult {
  /**
   * 是否有权限
   */
  hasAccess: boolean
  
  /**
   * 是否为超级管理员
   */
  isSuperAdmin: boolean
  
  /**
   * 权限检查详情
   */
  permissionDetails: {
    hasRequiredPermissions: boolean
    hasRequiredRoles: boolean
    missingPermissions: string[]
    missingRoles: string[]
  }
  
  /**
   * 渲染权限控制的元素
   */
  renderWithPermission: (
    element: React.ReactNode,
    fallback?: React.ReactNode
  ) => React.ReactNode
  
  /**
   * 获取权限控制的样式类名
   */
  getPermissionClassName: (
    baseClassName?: string,
    noPermissionClassName?: string
  ) => string
  
  /**
   * 获取权限控制的属性
   */
  getPermissionProps: (
    baseProps?: Record<string, any>,
    noPermissionProps?: Record<string, any>
  ) => Record<string, any>
}

/**
 * 权限控制Hook
 */
export const usePermissionControl = (
  options: PermissionControlOptions = {}
): PermissionControlResult => {
  const {
    permissions = [],
    roles = [],
    permissionMode = 'all',
    roleMode = 'any'
  } = options

  const {
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasAnyRole,
    hasAllRoles,
    isSuperAdmin,
    permissions: userPermissions,
    roles: userRoles
  } = usePermission()

  // 权限检查结果
  const permissionResult = useMemo(() => {
    const superAdmin = isSuperAdmin()
    
    // 超级管理员拥有所有权限
    if (superAdmin) {
      return {
        hasAccess: true,
        isSuperAdmin: true,
        permissionDetails: {
          hasRequiredPermissions: true,
          hasRequiredRoles: true,
          missingPermissions: [],
          missingRoles: []
        }
      }
    }

    // 检查权限
    let hasRequiredPermissions = true
    let missingPermissions: string[] = []
    
    if (permissions.length > 0) {
      if (permissionMode === 'all') {
        hasRequiredPermissions = hasAllPermissions(permissions)
        if (!hasRequiredPermissions) {
          missingPermissions = permissions.filter(p => !hasPermission(p))
        }
      } else {
        hasRequiredPermissions = hasAnyPermission(permissions)
        if (!hasRequiredPermissions) {
          missingPermissions = permissions
        }
      }
    }

    // 检查角色
    let hasRequiredRoles = true
    let missingRoles: string[] = []
    
    if (roles.length > 0) {
      if (roleMode === 'all') {
        hasRequiredRoles = hasAllRoles(roles)
        if (!hasRequiredRoles) {
          missingRoles = roles.filter(r => !userRoles.includes(r))
        }
      } else {
        hasRequiredRoles = hasAnyRole(roles)
        if (!hasRequiredRoles) {
          missingRoles = roles
        }
      }
    }

    const hasAccess = hasRequiredPermissions && hasRequiredRoles

    return {
      hasAccess,
      isSuperAdmin: false,
      permissionDetails: {
        hasRequiredPermissions,
        hasRequiredRoles,
        missingPermissions,
        missingRoles
      }
    }
  }, [
    permissions,
    roles,
    permissionMode,
    roleMode,
    userPermissions,
    userRoles,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasAnyRole,
    hasAllRoles,
    isSuperAdmin
  ])

  // 渲染权限控制的元素
  const renderWithPermission = (
    element: React.ReactNode,
    fallback: React.ReactNode = null
  ): React.ReactNode => {
    return permissionResult.hasAccess ? element : fallback
  }

  // 获取权限控制的样式类名
  const getPermissionClassName = (
    baseClassName = '',
    noPermissionClassName = 'opacity-50 cursor-not-allowed'
  ): string => {
    if (permissionResult.hasAccess) {
      return baseClassName
    }
    return `${baseClassName} ${noPermissionClassName}`.trim()
  }

  // 获取权限控制的属性
  const getPermissionProps = (
    baseProps: Record<string, any> = {},
    noPermissionProps: Record<string, any> = { disabled: true }
  ): Record<string, any> => {
    if (permissionResult.hasAccess) {
      return baseProps
    }
    return { ...baseProps, ...noPermissionProps }
  }

  return {
    ...permissionResult,
    renderWithPermission,
    getPermissionClassName,
    getPermissionProps
  }
}

/**
 * 批量权限检查Hook
 */
export const useBatchPermissionControl = (
  permissionGroups: Record<string, PermissionControlOptions>
): Record<string, PermissionControlResult> => {
  // 获取所有权限控制结果 - 使用固定数量的Hook调用
  const permissionEntries = useMemo(() => Object.entries(permissionGroups), [permissionGroups])

  // 为了遵循Hook规则，我们需要为每个可能的权限组预先调用Hook
  // 这里我们假设最多支持10个权限组，实际项目中可以根据需要调整
  const result1 = usePermissionControl(permissionEntries[0]?.[1] || { permissions: [], roles: [] })
  const result2 = usePermissionControl(permissionEntries[1]?.[1] || { permissions: [], roles: [] })
  const result3 = usePermissionControl(permissionEntries[2]?.[1] || { permissions: [], roles: [] })
  const result4 = usePermissionControl(permissionEntries[3]?.[1] || { permissions: [], roles: [] })
  const result5 = usePermissionControl(permissionEntries[4]?.[1] || { permissions: [], roles: [] })

  const allResults = [result1, result2, result3, result4, result5]

  return useMemo(() => {
    const results: Record<string, PermissionControlResult> = {}

    permissionEntries.forEach(([key], index) => {
      if (index < allResults.length) {
        results[key] = allResults[index]
      }
    })

    return results
  }, [permissionEntries, allResults])
}

/**
 * 权限调试Hook（仅开发环境）
 */
export const usePermissionDebug = (label?: string) => {
  const { permissions, roles, isSuperAdmin } = usePermission()

  if (process.env.NODE_ENV === 'development') {
    console.group(`权限调试${label ? ` - ${label}` : ''}`)
    console.log('用户权限:', permissions)
    console.log('用户角色:', roles)
    console.log('是否超级管理员:', isSuperAdmin())
    console.groupEnd()
  }

  return {
    permissions,
    roles,
    isSuperAdmin: isSuperAdmin()
  }
}

export default usePermissionControl
