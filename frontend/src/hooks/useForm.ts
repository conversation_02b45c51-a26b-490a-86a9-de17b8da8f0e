/**
 * 自定义表单Hook
 * 
 * 提供表单状态管理、验证和提交功能
 * 作为React Hook Form的轻量级替代方案
 */

import { useState, useCallback } from 'react'

export interface ValidationRule<T = any> {
  required?: boolean | string
  minLength?: number | { value: number; message: string }
  maxLength?: number | { value: number; message: string }
  pattern?: RegExp | { value: RegExp; message: string }
  validate?: (value: T) => boolean | string
}

export interface FieldConfig<T = any> {
  defaultValue?: T
  rules?: ValidationRule<T>
}

export interface FormConfig<T extends Record<string, any>> {
  defaultValues?: Partial<T>
  fields?: {
    [K in keyof T]?: FieldConfig<T[K]>
  }
}

export interface FieldState {
  value: any
  error?: string
  touched: boolean
}

export interface FormState<T extends Record<string, any>> {
  values: T
  errors: Partial<Record<keyof T, string>>
  touched: Partial<Record<keyof T, boolean>>
  isValid: boolean
  isSubmitting: boolean
}

export interface UseFormReturn<T extends Record<string, any>> {
  // 状态
  formState: FormState<T>
  
  // 方法
  register: (name: keyof T, rules?: ValidationRule) => {
    name: string
    value: any
    onChange: (value: any) => void
    onBlur: () => void
    error?: string
  }
  setValue: (name: keyof T, value: any) => void
  setError: (name: keyof T, error: string) => void
  clearErrors: (names?: (keyof T)[]) => void
  reset: (values?: Partial<T>) => void
  handleSubmit: (onSubmit: (data: T) => void | Promise<void>) => (e?: React.FormEvent) => Promise<void>
  watch: (name?: keyof T) => any
}

/**
 * 验证单个字段
 */
const validateField = (value: any, rules?: ValidationRule): string | undefined => {
  if (!rules) return undefined

  // Required validation
  if (rules.required) {
    const isEmpty = value === undefined || value === null || value === ''
    if (isEmpty) {
      return typeof rules.required === 'string' ? rules.required : '此字段为必填项'
    }
  }

  // Skip other validations if value is empty and not required
  if (value === undefined || value === null || value === '') {
    return undefined
  }

  // MinLength validation
  if (rules.minLength) {
    const minLength = typeof rules.minLength === 'number' ? rules.minLength : rules.minLength.value
    const message = typeof rules.minLength === 'object' ? rules.minLength.message : `最少需要${minLength}个字符`
    
    if (String(value).length < minLength) {
      return message
    }
  }

  // MaxLength validation
  if (rules.maxLength) {
    const maxLength = typeof rules.maxLength === 'number' ? rules.maxLength : rules.maxLength.value
    const message = typeof rules.maxLength === 'object' ? rules.maxLength.message : `最多允许${maxLength}个字符`
    
    if (String(value).length > maxLength) {
      return message
    }
  }

  // Pattern validation
  if (rules.pattern) {
    const pattern = typeof rules.pattern === 'object' && 'value' in rules.pattern ? rules.pattern.value : rules.pattern
    const message = typeof rules.pattern === 'object' && 'message' in rules.pattern ? rules.pattern.message : '格式不正确'
    
    if (!pattern.test(String(value))) {
      return message
    }
  }

  // Custom validation
  if (rules.validate) {
    const result = rules.validate(value)
    if (typeof result === 'string') {
      return result
    }
    if (result === false) {
      return '验证失败'
    }
  }

  return undefined
}

/**
 * 自定义表单Hook
 */
export function useForm<T extends Record<string, any>>(
  config: FormConfig<T> = {}
): UseFormReturn<T> {
  const { defaultValues = {} as T, fields = {} } = config

  // 初始化表单状态
  const [values, setValues] = useState<T>(() => {
    const initialValues = { ...defaultValues } as T

    // 从字段配置中获取默认值
    Object.keys(fields || {}).forEach(key => {
      const fieldConfig = (fields || {} as any)[key as keyof T]
      if (fieldConfig?.defaultValue !== undefined && initialValues[key as keyof T] === undefined) {
        initialValues[key as keyof T] = fieldConfig.defaultValue
      }
    })
    
    return initialValues
  })

  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({})
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 计算表单是否有效
  const isValid = Object.keys(errors).length === 0

  // 注册字段
  const register = useCallback((name: keyof T, rules?: ValidationRule) => {
    return {
      name: String(name),
      value: values[name] || '',
      onChange: (value: any) => {
        setValues(prev => ({ ...prev, [name]: value }))
        
        // 实时验证
        const error = validateField(value, rules)
        setErrors(prev => {
          const newErrors = { ...prev }
          if (error) {
            newErrors[name] = error
          } else {
            delete newErrors[name]
          }
          return newErrors
        })
      },
      onBlur: () => {
        setTouched(prev => ({ ...prev, [name]: true }))
        
        // 失焦时验证
        const error = validateField(values[name], rules)
        if (error) {
          setErrors(prev => ({ ...prev, [name]: error }))
        }
      },
      error: errors[name]
    }
  }, [values, errors])

  // 设置字段值
  const setValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }))
  }, [])

  // 设置字段错误
  const setError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }))
  }, [])

  // 清除错误
  const clearErrors = useCallback((names?: (keyof T)[]) => {
    if (names) {
      setErrors(prev => {
        const newErrors = { ...prev }
        names.forEach(name => delete newErrors[name])
        return newErrors
      })
    } else {
      setErrors({})
    }
  }, [])

  // 重置表单
  const reset = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues || defaultValues
    setValues(resetValues as T)
    setErrors({})
    setTouched({})
    setIsSubmitting(false)
  }, [defaultValues])

  // 处理表单提交
  const handleSubmit = useCallback((onSubmit: (data: T) => void | Promise<void>) => {
    return async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault()
      }

      setIsSubmitting(true)

      try {
        // 验证所有字段
        const newErrors: Partial<Record<keyof T, string>> = {}
        
        Object.keys(fields || {}).forEach(key => {
          const fieldConfig = (fields || {} as any)[key as keyof T]
          const error = validateField(values[key as keyof T], fieldConfig?.rules)
          if (error) {
            newErrors[key as keyof T] = error
          }
        })

        setErrors(newErrors)

        // 如果有错误，不提交
        if (Object.keys(newErrors).length > 0) {
          return
        }

        // 提交表单
        await onSubmit(values)
      } finally {
        setIsSubmitting(false)
      }
    }
  }, [values, fields])

  // 监听字段值
  const watch = useCallback((name?: keyof T) => {
    if (name) {
      return values[name]
    }
    return values
  }, [values])

  return {
    formState: {
      values,
      errors,
      touched,
      isValid,
      isSubmitting
    },
    register,
    setValue,
    setError,
    clearErrors,
    reset,
    handleSubmit,
    watch
  }
}
