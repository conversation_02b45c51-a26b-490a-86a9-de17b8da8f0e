import { useCallback, useRef, useEffect } from 'react'
import { httpClient } from '@/services/request'
import { requestCache } from '@/utils/requestCache'
import type { RequestCacheOptions } from '@/utils/requestCache'

/**
 * 优化的请求Hook
 * 提供防抖、缓存、重试等功能
 */

interface UseOptimizedRequestOptions {
  // 防抖延迟（毫秒）
  debounceDelay?: number
  // 是否启用缓存
  enableCache?: boolean
  // 缓存选项
  cacheOptions?: RequestCacheOptions
  // 重试次数
  retryCount?: number
  // 重试延迟（毫秒）
  retryDelay?: number
  // 是否在组件卸载时取消请求
  cancelOnUnmount?: boolean
}

interface OptimizedRequestResult<T> {
  // 执行请求
  execute: (url: string, params?: any) => Promise<T>
  // 取消请求
  cancel: () => void
  // 清除缓存
  clearCache: (url?: string, params?: any) => void
  // 获取缓存统计
  getCacheStats: () => any
}

export function useOptimizedRequest<T = any>(
  options: UseOptimizedRequestOptions = {}
): OptimizedRequestResult<T> {
  const {
    debounceDelay = 300,
    enableCache = true,
    cacheOptions = {},
    retryCount = 2,
    retryDelay = 1000,
    cancelOnUnmount = true
  } = options

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const isMountedRef = useRef(true)

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
      if (cancelOnUnmount && abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [cancelOnUnmount])

  // 防抖执行函数
  const debouncedExecute = useCallback((
    fn: () => Promise<T>,
    delay: number
  ): Promise<T> => {
    return new Promise((resolve, reject) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      debounceTimerRef.current = setTimeout(async () => {
        if (!isMountedRef.current) {
          reject(new Error('Component unmounted'))
          return
        }

        try {
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay)
    })
  }, [])

  // 重试执行函数
  const executeWithRetry = useCallback(async (
    fn: () => Promise<T>,
    maxRetries: number,
    delay: number
  ): Promise<T> => {
    let lastError: Error

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          break
        }

        // 如果组件已卸载，停止重试
        if (!isMountedRef.current) {
          throw new Error('Component unmounted')
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)))
      }
    }

    throw lastError!
  }, [])

  // 执行请求
  const execute = useCallback(async (url: string, params?: any): Promise<T> => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的取消控制器
    abortControllerRef.current = new AbortController()

    const requestFn = async (): Promise<T> => {
      if (!isMountedRef.current) {
        throw new Error('Component unmounted')
      }

      const config = {
        params,
        signal: abortControllerRef.current?.signal,
        useCache: enableCache,
        cacheOptions
      }

      return httpClient.get<T>(url, config)
    }

    // 如果启用缓存，使用缓存请求
    if (enableCache) {
      const cachedRequestFn = () => requestCache.request(
        requestFn,
        url,
        params,
        cacheOptions
      )

      // 防抖 + 重试
      if (debounceDelay > 0) {
        return debouncedExecute(
          () => executeWithRetry(cachedRequestFn, retryCount, retryDelay),
          debounceDelay
        )
      } else {
        return executeWithRetry(cachedRequestFn, retryCount, retryDelay)
      }
    } else {
      // 防抖 + 重试
      if (debounceDelay > 0) {
        return debouncedExecute(
          () => executeWithRetry(requestFn, retryCount, retryDelay),
          debounceDelay
        )
      } else {
        return executeWithRetry(requestFn, retryCount, retryDelay)
      }
    }
  }, [
    enableCache,
    cacheOptions,
    debounceDelay,
    retryCount,
    retryDelay,
    debouncedExecute,
    executeWithRetry
  ])

  // 取消请求
  const cancel = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
  }, [])

  // 清除缓存
  const clearCache = useCallback((url?: string, params?: any) => {
    if (url) {
      const key = params ? `${url}:${JSON.stringify(params)}` : url
      requestCache.delete(key)
    } else {
      requestCache.clear()
    }
  }, [])

  // 获取缓存统计
  const getCacheStats = useCallback(() => {
    return requestCache.getStats()
  }, [])

  return {
    execute,
    cancel,
    clearCache,
    getCacheStats
  }
}
