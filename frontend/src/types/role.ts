/**
 * 角色管理相关类型定义
 * 
 * 基于后端SysRole实体和相关DTO定义
 * 确保前后端类型一致性
 */

import { z } from 'zod'
import type { PageRequest, PageResult } from './api'

/**
 * 角色类型枚举
 */
export enum RoleType {
    /** 系统角色 */
    SYSTEM = 'SYSTEM',
    /** 自定义角色 */
    CUSTOM = 'CUSTOM'
}

/**
 * 数据权限范围枚举
 */
export enum DataScope {
    /** 全部数据权限 */
    ALL = 'ALL',
    /** 部门数据权限 */
    DEPT = 'DEPT',
    /** 部门及子部门数据权限 */
    DEPT_AND_SUB = 'DEPT_AND_SUB',
    /** 仅本人数据权限 */
    SELF = 'SELF',
    /** 自定义数据权限 */
    CUSTOM = 'CUSTOM'
}

/**
 * 角色状态枚举
 */
export enum RoleStatus {
    /** 禁用 */
    DISABLED = 0,
    /** 启用 */
    ENABLED = 1
}

/**
 * 角色信息接口
 * 对应后端SysRole实体
 */
export interface Role {
    /** 角色ID */
    id: number
    /** 租户ID */
    tenantId: number
    /** 角色编码 */
    roleCode: string
    /** 角色名称 */
    roleName: string
    /** 角色类型 */
    roleType: RoleType
    /** 数据权限范围 */
    dataScope: DataScope
    /** 状态（0-禁用，1-启用） */
    status: RoleStatus
    /** 排序 */
    sortOrder: number
    /** 备注 */
    remark?: string
    /** 创建者ID */
    createBy?: number
    /** 创建时间 */
    createTime: string
    /** 更新者ID */
    updateBy?: number
    /** 更新时间 */
    updateTime: string
    /** 删除标记 */
    deleted: number
}

/**
 * 角色查询请求参数
 * 对应后端RoleQueryRequest
 */
export interface RoleQueryRequest extends PageRequest {
    /** 搜索关键词（同时搜索角色编码和角色名称） */
    keyword?: string
    /** 角色编码 */
    roleCode?: string
    /** 角色名称 */
    roleName?: string
    /** 角色类型 */
    roleType?: RoleType
    /** 数据权限范围 */
    dataScope?: DataScope
    /** 状态 */
    status?: RoleStatus
    /** 创建时间开始 */
    createTimeStart?: string
    /** 创建时间结束 */
    createTimeEnd?: string
}

/**
 * 角色创建请求参数
 * 对应后端RoleCreateRequest
 */
export interface RoleCreateRequest {
    /** 角色编码 */
    roleCode: string
    /** 角色名称 */
    roleName: string
    /** 角色类型 */
    roleType: RoleType
    /** 数据权限范围 */
    dataScope: DataScope
    /** 状态（0-禁用，1-启用） */
    status: RoleStatus
    /** 排序 */
    sortOrder?: number
    /** 权限ID列表 */
    permissionIds?: number[]
    /** 备注 */
    remark?: string
}

/**
 * 角色更新请求参数
 * 对应后端RoleUpdateRequest
 */
export interface RoleUpdateRequest {
    /** 角色ID */
    id: number
    /** 角色名称 */
    roleName?: string
    /** 数据权限范围 */
    dataScope?: DataScope
    /** 状态（0-禁用，1-启用） */
    status?: RoleStatus
    /** 排序 */
    sortOrder?: number
    /** 权限ID列表 */
    permissionIds?: number[]
    /** 备注 */
    remark?: string
}

/**
 * 角色权限分配请求参数
 */
export interface RolePermissionAssignRequest {
    /** 角色ID */
    roleId: number
    /** 权限ID列表 */
    permissionIds: number[]
}

/**
 * 角色状态更新请求参数
 */
export interface RoleStatusUpdateRequest {
    /** 角色ID */
    roleId: number
    /** 状态 */
    status: RoleStatus
}

/**
 * 批量角色状态更新请求参数
 */
export interface RoleBatchStatusUpdateRequest {
    /** 角色ID列表 */
    roleIds: number[]
    /** 状态 */
    status: RoleStatus
}

/**
 * 角色分页查询结果
 */
export type RolePageResult = PageResult<Role>

/**
 * 角色列表响应
 */
export type RoleListResponse = Role[]

/**
 * 角色详情响应
 */
export type RoleDetailResponse = Role

/**
 * 角色权限列表响应
 */
export interface RolePermissionResponse {
    /** 权限ID列表 */
    permissionIds: number[]
    /** 权限详情列表 */
    permissions: Array<{
        id: number
        permissionName: string
        permissionCode: string
        permissionType: number
        parentId?: number
    }>
}

/**
 * 角色用户统计响应
 */
export interface RoleUserCountResponse {
    /** 角色ID */
    roleId: number
    /** 用户数量 */
    userCount: number
}

/**
 * 角色验证响应
 */
export interface RoleValidationResponse {
    /** 是否存在 */
    exists: boolean
    /** 验证消息 */
    message?: string
}

// ==================== Zod 验证 Schema ====================

/**
 * 角色编码验证规则
 */
export const roleCodeSchema = z
    .string()
    .min(1, '角色编码不能为空')
    .max(50, '角色编码长度不能超过50个字符')
    .regex(/^[A-Z][A-Z0-9_]*$/, '角色编码只能包含大写字母、数字和下划线，且必须以大写字母开头')

/**
 * 角色名称验证规则
 */
export const roleNameSchema = z
    .string()
    .min(1, '角色名称不能为空')
    .max(100, '角色名称长度不能超过100个字符')

/**
 * 角色类型验证规则
 */
export const roleTypeSchema = z.nativeEnum(RoleType, {
    message: '角色类型只能是SYSTEM或CUSTOM'
})

/**
 * 数据权限范围验证规则
 */
export const dataScopeSchema = z.nativeEnum(DataScope, {
    message: '数据权限范围值不正确'
})

/**
 * 角色状态验证规则
 */
export const roleStatusSchema = z.nativeEnum(RoleStatus, {
    message: '状态值不正确'
})

/**
 * 排序验证规则
 */
export const sortOrderSchema = z
    .number()
    .min(0, '排序值不能小于0')
    .optional()
    .default(0)

/**
 * 备注验证规则
 */
export const remarkSchema = z
    .string()
    .max(500, '备注长度不能超过500个字符')
    .optional()

/**
 * 权限ID列表验证规则
 */
export const permissionIdsSchema = z
    .array(z.number().positive('权限ID必须为正整数'))
    .optional()

/**
 * 角色创建表单验证Schema
 */
export const roleCreateSchema = z.object({
    roleCode: roleCodeSchema,
    roleName: roleNameSchema,
    roleType: roleTypeSchema,
    dataScope: dataScopeSchema,
    status: roleStatusSchema,
    sortOrder: sortOrderSchema,
    permissionIds: permissionIdsSchema,
    remark: remarkSchema
})

/**
 * 角色更新表单验证Schema
 */
export const roleUpdateSchema = z.object({
    id: z.number().positive('角色ID必须为正整数'),
    roleName: roleNameSchema.optional(),
    dataScope: dataScopeSchema.optional(),
    status: roleStatusSchema.optional(),
    sortOrder: sortOrderSchema,
    permissionIds: permissionIdsSchema,
    remark: remarkSchema
})

/**
 * 角色查询表单验证Schema
 */
export const roleQuerySchema = z.object({
    page: z.number().min(1, '页码必须大于0').default(1),
    size: z.number().min(1, '每页大小必须大于0').max(100, '每页大小不能超过100').default(10),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional(),
    roleCode: z.string().optional(),
    roleName: z.string().optional(),
    roleType: roleTypeSchema.optional(),
    dataScope: dataScopeSchema.optional(),
    status: roleStatusSchema.optional(),
    createTimeStart: z.string().optional(),
    createTimeEnd: z.string().optional()
})

/**
 * 角色权限分配验证Schema
 */
export const rolePermissionAssignSchema = z.object({
    roleId: z.number().positive('角色ID必须为正整数'),
    permissionIds: z.array(z.number().positive('权限ID必须为正整数'))
})

/**
 * 角色状态更新验证Schema
 */
export const roleStatusUpdateSchema = z.object({
    roleId: z.number().positive('角色ID必须为正整数'),
    status: roleStatusSchema
})

/**
 * 批量角色状态更新验证Schema
 */
export const roleBatchStatusUpdateSchema = z.object({
    roleIds: z.array(z.number().positive('角色ID必须为正整数')).min(1, '至少选择一个角色'),
    status: roleStatusSchema
})

// ==================== 类型导出 ====================

/**
 * 角色创建表单类型
 */
export type RoleCreateForm = z.infer<typeof roleCreateSchema>

/**
 * 角色更新表单类型
 */
export type RoleUpdateForm = z.infer<typeof roleUpdateSchema>

/**
 * 角色查询表单类型
 */
export type RoleQueryForm = z.infer<typeof roleQuerySchema>

/**
 * 角色权限分配表单类型
 */
export type RolePermissionAssignForm = z.infer<typeof rolePermissionAssignSchema>

/**
 * 角色状态更新表单类型
 */
export type RoleStatusUpdateForm = z.infer<typeof roleStatusUpdateSchema>

/**
 * 批量角色状态更新表单类型
 */
export type RoleBatchStatusUpdateForm = z.infer<typeof roleBatchStatusUpdateSchema>

// ==================== 常量定义 ====================

/**
 * 角色类型选项
 */
export const ROLE_TYPE_OPTIONS = [
    { label: '系统角色', value: RoleType.SYSTEM },
    { label: '自定义角色', value: RoleType.CUSTOM }
] as const

/**
 * 数据权限范围选项
 */
export const DATA_SCOPE_OPTIONS = [
    { label: '全部数据权限', value: DataScope.ALL },
    { label: '部门数据权限', value: DataScope.DEPT },
    { label: '部门及子部门数据权限', value: DataScope.DEPT_AND_SUB },
    { label: '仅本人数据权限', value: DataScope.SELF },
    { label: '自定义数据权限', value: DataScope.CUSTOM }
] as const

/**
 * 角色状态选项
 */
export const ROLE_STATUS_OPTIONS = [
    { label: '启用', value: RoleStatus.ENABLED },
    { label: '禁用', value: RoleStatus.DISABLED }
] as const

/**
 * 角色状态标签映射
 */
export const ROLE_STATUS_LABELS = {
    [RoleStatus.ENABLED]: '启用',
    [RoleStatus.DISABLED]: '禁用'
} as const

/**
 * 角色类型标签映射
 */
export const ROLE_TYPE_LABELS = {
    [RoleType.SYSTEM]: '系统角色',
    [RoleType.CUSTOM]: '自定义角色'
} as const

/**
 * 数据权限范围标签映射
 */
export const DATA_SCOPE_LABELS = {
    [DataScope.ALL]: '全部数据权限',
    [DataScope.DEPT]: '部门数据权限',
    [DataScope.DEPT_AND_SUB]: '部门及子部门数据权限',
    [DataScope.SELF]: '仅本人数据权限',
    [DataScope.CUSTOM]: '自定义数据权限'
} as const