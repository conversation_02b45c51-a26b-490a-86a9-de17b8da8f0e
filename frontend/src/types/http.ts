/**
 * HTTP响应处理重构 - 新的类型定义
 * 
 * 这个文件定义了重构后的HTTP响应类型，支持直接返回业务数据
 * 同时保持向后兼容性
 */

/**
 * 原始API响应结构（保持向后兼容）
 */
export interface LegacyApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: string
}

/**
 * 重构后的错误对象
 * 包含完整的错误信息，便于错误处理
 */
export interface ApiError extends Error {
  code: number
  data?: any
  timestamp?: string
  originalResponse?: LegacyApiResponse
}

/**
 * 服务方法类型定义
 * 重构后直接返回业务数据类型T，而不是包装的ApiResponse<T>
 */
export type ServiceMethod<T = any, P extends any[] = any[]> = (...args: P) => Promise<T>

/**
 * 分页查询结果（重构后直接返回数据）
 */
export interface PaginatedResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 验证方法返回类型
 * 重构后直接返回boolean，而不是包装的响应
 */
export type ValidationResult = boolean

/**
 * 菜单验证服务类型
 */
export interface MenuValidationService {
  checkMenuName: (name: string, parentId: number, excludeId?: number) => Promise<ValidationResult>
  checkPath: (path: string, excludeId?: number) => Promise<ValidationResult>
}

/**
 * 通用CRUD服务类型
 */
export interface CrudService<T, CreateDto, UpdateDto> {
  list: (params?: any) => Promise<PaginatedResult<T>>
  getById: (id: number) => Promise<T>
  create: (data: CreateDto) => Promise<T>
  update: (id: number, data: UpdateDto) => Promise<T>
  delete: (id: number) => Promise<void>
}

/**
 * HTTP客户端接口（重构后）
 */
export interface HttpClient {
  get<T = any>(url: string, config?: any): Promise<T>
  post<T = any>(url: string, data?: any, config?: any): Promise<T>
  put<T = any>(url: string, data?: any, config?: any): Promise<T>
  delete<T = any>(url: string, config?: any): Promise<T>
  upload<T = any>(url: string, formData: FormData, config?: any): Promise<T>
}

/**
 * 请求配置选项
 */
export interface RequestOptions {
  skipAuth?: boolean
  skipErrorHandler?: boolean
  retryCount?: number
  timeout?: number
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
  handle(error: any): ApiError
  isApiError(error: any): error is ApiError
  getErrorMessage(error: any): string
}

/**
 * 响应拦截器配置
 */
export interface ResponseInterceptorConfig {
  autoUnwrap?: boolean // 是否自动解包响应数据
  throwOnError?: boolean // 是否在业务错误时抛出异常
  logResponse?: boolean // 是否记录响应日志
}

/**
 * 迁移辅助类型
 * 用于在迁移期间标识哪些方法已经重构
 */
export interface MigrationStatus {
  isRefactored: boolean
  version: 'legacy' | 'refactored'
  notes?: string
}

/**
 * 类型守卫：检查是否为旧版API响应
 */
export function isLegacyApiResponse<T>(response: any): response is LegacyApiResponse<T> {
  return (
    typeof response === 'object' &&
    response !== null &&
    'code' in response &&
    'message' in response &&
    'data' in response &&
    'success' in response
  )
}

/**
 * 类型守卫：检查是否为API错误
 */
export function isApiError(error: any): error is ApiError {
  return (
    error instanceof Error &&
    'code' in error &&
    typeof error.code === 'number'
  )
}

/**
 * 响应数据解包工具
 * 用于处理可能的旧版响应格式
 */
export function unwrapResponse<T>(response: T | LegacyApiResponse<T>): T {
  if (isLegacyApiResponse(response)) {
    return response.data
  }
  return response
}

/**
 * 创建API错误对象
 */
export function createApiError(
  message: string,
  code: number = 500,
  data?: any,
  originalResponse?: LegacyApiResponse
): ApiError {
  const error = new Error(message) as ApiError
  error.code = code
  error.data = data
  error.originalResponse = originalResponse
  error.timestamp = new Date().toISOString()
  return error
}

/**
 * 重构状态标记
 * 用于标识各个模块的重构进度
 */
export const REFACTOR_STATUS = {
  HTTP_CLIENT: { isRefactored: true, version: 'refactored' as const },
  MENU_SERVICE: { isRefactored: true, version: 'refactored' as const },
  USER_SERVICE: { isRefactored: false, version: 'legacy' as const },
  ROLE_SERVICE: { isRefactored: false, version: 'legacy' as const },
} as const

/**
 * 导出重构后的主要类型
 */
export type {
  LegacyApiResponse as ApiResponse, // 保持向后兼容的别名
}
