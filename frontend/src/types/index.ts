export * from './api'
export * from './role'
export * from './user'
export * from './dept'
export * from './tenant'
export * from './financial'

/**
 * 通用状态枚举
 */
export enum Status {
  DISABLED = 0,
  ENABLED = 1
}

/**
 * 性别枚举
 */
export enum Gender {
  UNKNOWN = 0,
  MALE = 1,
  FEMALE = 2
}

/**
 * 菜单类型枚举
 */
export enum MenuType {
  DIRECTORY = 'M',
  MENU = 'C',
  BUTTON = 'F'
}

/**
 * 权限类型枚举
 */
export enum PermissionType {
  MENU = 1,
  BUTTON = 2,
  API = 3
}

/**
 * 路由元信息
 */
export interface RouteMeta {
  title: string
  icon?: string
  hidden?: boolean
  keepAlive?: boolean
  permissions?: string[]
  roles?: string[]
}

/**
 * 应用路由
 */
export interface AppRoute {
  path: string
  name: string
  component?: React.ComponentType<any>
  redirect?: string
  meta?: RouteMeta
  children?: AppRoute[]
}

/**
 * 表格列配置
 */
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  render?: (value: any, record: T, index: number) => React.ReactNode
}

/**
 * 表单字段配置
 */
export interface FormField<T = any> {
  name: string
  label: string
  type: 'input' | 'password' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'datetime'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: T }>
  rules?: Array<(value: T) => boolean | string>
}
