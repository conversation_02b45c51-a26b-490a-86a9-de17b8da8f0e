/**
 * 部门管理相关类型定义
 */

import type { PageRequest } from './api'

/**
 * 部门信息
 */
export interface Dept {
  id: number
  parentId: number
  deptCode: string
  deptName: string
  leaderId?: number
  leaderName?: string
  phone?: string
  email?: string
  status: number
  sortOrder: number
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  deleted: number
  tenantId?: number
  children?: Dept[]
}

/**
 * 部门查询请求
 */
export interface DeptQueryRequest extends PageRequest {
  keyword?: string // 通用搜索关键词
  parentId?: number
  deptCode?: string
  deptName?: string
  leaderId?: number
  phone?: string
  email?: string
  status?: number
  startTime?: string
  endTime?: string
}

/**
 * 部门创建请求
 */
export interface DeptCreateRequest {
  parentId: number
  deptCode: string
  deptName: string
  leaderId?: number
  phone?: string
  email?: string
  status: number
  sortOrder: number
  remark?: string
}

/**
 * 部门更新请求
 */
export interface DeptUpdateRequest {
  id: number
  parentId?: number
  deptName: string
  leaderId?: number
  phone?: string
  email?: string
  status: number
  sortOrder: number
  remark?: string
}

/**
 * 部门状态枚举
 */
export enum DeptStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1   // 启用
}

/**
 * 部门状态选项
 */
export const DEPT_STATUS_OPTIONS = [
  { label: '启用', value: DeptStatus.ENABLED },
  { label: '禁用', value: DeptStatus.DISABLED }
]

/**
 * 获取部门状态标签
 */
export const getDeptStatusLabel = (status: number): string => {
  const option = DEPT_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || '未知'
}

/**
 * 部门树节点接口
 */
export interface DeptTreeNode extends Dept {
  children: DeptTreeNode[]
  level: number
  expanded?: boolean
  loading?: boolean
}

/**
 * 部门移动请求
 */
export interface DeptMoveRequest {
  deptId: number
  newParentId: number
}

/**
 * 用户部门分配请求
 */
export interface UserDeptAssignRequest {
  userId: number
  deptIds: number[]
  primaryDeptId: number
}

/**
 * 部门统计信息
 */
export interface DeptStats {
  total: number
  enabled: number
  disabled: number
  rootDepts: number
}

/**
 * 部门选择器选项
 */
export interface DeptSelectOption {
  value: number
  label: string
  disabled?: boolean
  children?: DeptSelectOption[]
}

/**
 * 构建部门树
 */
export const buildDeptTree = (depts: Dept[]): DeptTreeNode[] => {
  const deptMap = new Map<number, DeptTreeNode>()
  const rootDepts: DeptTreeNode[] = []

  // 创建部门节点映射
  depts.forEach(dept => {
    const node: DeptTreeNode = {
      ...dept,
      children: [],
      level: 0,
      expanded: false
    }
    deptMap.set(dept.id, node)
  })

  // 构建树形结构
  depts.forEach(dept => {
    const node = deptMap.get(dept.id)!
    if (dept.parentId === 0) {
      // 根部门
      rootDepts.push(node)
    } else {
      // 子部门
      const parent = deptMap.get(dept.parentId)
      if (parent) {
        parent.children.push(node)
        node.level = parent.level + 1
      } else {
        // 如果父部门不在当前数据中（比如搜索结果），将其作为根部门显示
        rootDepts.push(node)
      }
    }
  })

  // 按排序字段排序
  const sortDepts = (depts: DeptTreeNode[]) => {
    depts.sort((a, b) => {
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder
      }
      return a.createTime.localeCompare(b.createTime)
    })
    depts.forEach(dept => {
      if (dept.children.length > 0) {
        sortDepts(dept.children)
      }
    })
  }

  sortDepts(rootDepts)
  return rootDepts
}

/**
 * 扁平化部门树
 */
export const flattenDeptTree = (tree: DeptTreeNode[]): DeptTreeNode[] => {
  const result: DeptTreeNode[] = []
  
  const traverse = (nodes: DeptTreeNode[]) => {
    nodes.forEach(node => {
      result.push(node)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(tree)
  return result
}

/**
 * 查找部门路径
 */
export const findDeptPath = (tree: DeptTreeNode[], targetId: number): DeptTreeNode[] => {
  const path: DeptTreeNode[] = []
  
  const findPath = (nodes: DeptTreeNode[], target: number): boolean => {
    for (const node of nodes) {
      path.push(node)
      
      if (node.id === target) {
        return true
      }
      
      if (node.children && node.children.length > 0) {
        if (findPath(node.children, target)) {
          return true
        }
      }
      
      path.pop()
    }
    return false
  }
  
  findPath(tree, targetId)
  return path
}

/**
 * 构建部门选择器选项
 */
export const buildDeptSelectOptions = (depts: Dept[], excludeId?: number): DeptSelectOption[] => {
  const tree = buildDeptTree(depts.filter(dept => dept.id !== excludeId))
  
  const buildOptions = (nodes: DeptTreeNode[], level = 0): DeptSelectOption[] => {
    return nodes.map(node => ({
      value: node.id,
      label: '　'.repeat(level) + node.deptName,
      disabled: node.status === DeptStatus.DISABLED,
      children: node.children.length > 0 ? buildOptions(node.children, level + 1) : undefined
    }))
  }
  
  return buildOptions(tree)
}
