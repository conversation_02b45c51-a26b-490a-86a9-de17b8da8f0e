/**
 * 财务数据相关类型定义
 * 对应后端财务统计功能的数据结构
 */

/**
 * 时间范围接口
 */
export interface TimeRange {
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
}

/**
 * 财务统计查询请求参数
 * 对应后端 FinancialStatsRequest
 */
export interface FinancialStatsRequest extends TimeRange {
  /** 是否包含主播数据 */
  includeAnchor: boolean
}

/**
 * 财务统计数据项
 * 对应后端 FinancialStatsResponse
 */
export interface FinancialStatsItem {
  /** 统计分类 */
  category: string
  /** 统计项名称 */
  statName: string
  /** 统计值 */
  statValue: number
  /** 单位 */
  unit: string
}

/**
 * 分组财务统计响应数据
 * 对应后端 GroupedFinancialStatsResponse
 */
export interface GroupedFinancialStatsResponse {
  /** 用户相关统计 */
  userStats: FinancialStatsItem[]
  /** 主播相关统计 */
  anchorStats: FinancialStatsItem[]
  /** 合计统计 */
  totalStats: FinancialStatsItem[]
  /** 其他业务统计 */
  businessStats: FinancialStatsItem[]
}

/**
 * 财务统计分类枚举
 */
export enum FinancialStatsCategory {
  /** 用户相关统计 */
  USER = 'user',
  /** 主播相关统计 */
  ANCHOR = 'anchor',
  /** 合计统计 */
  TOTAL = 'total',
  /** 其他业务统计 */
  BUSINESS = 'business'
}

/**
 * 时间范围预设选项
 */
export enum TimeRangePreset {
  /** 今天 */
  TODAY = 'today',
  /** 昨天 */
  YESTERDAY = 'yesterday',
  /** 本周 */
  THIS_WEEK = 'thisWeek',
  /** 上周 */
  LAST_WEEK = 'lastWeek',
  /** 本月 */
  THIS_MONTH = 'thisMonth',
  /** 上月 */
  LAST_MONTH = 'lastMonth',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 时间范围选择器选项
 */
export interface TimeRangeOption {
  /** 选项标签 */
  label: string
  /** 选项值 */
  value: TimeRangePreset
  /** 时间范围 */
  range: TimeRange
}

/**
 * 财务数据加载状态
 */
export interface FinancialDataState {
  /** 数据 */
  data: GroupedFinancialStatsResponse | null
  /** 加载中 */
  loading: boolean
  /** 错误信息 */
  error: Error | null
  /** 最后更新时间 */
  lastUpdated: string | null
}

/**
 * 财务数据查询参数
 */
export interface FinancialQueryParams extends FinancialStatsRequest {
  /** 自动刷新间隔（毫秒） */
  refreshInterval?: number
  /** 是否启用缓存 */
  enableCache?: boolean
}

/**
 * 统计卡片数据
 */
export interface StatsCardData {
  /** 标题 */
  title: string
  /** 值 */
  value: number | string
  /** 单位 */
  unit?: string
  /** 格式化后的值 */
  formattedValue?: string
  /** 图标 */
  icon?: string
  /** 颜色主题 */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 趋势 */
  trend?: {
    /** 趋势方向 */
    direction: 'up' | 'down' | 'stable'
    /** 变化值 */
    value: number
    /** 变化百分比 */
    percentage: number
  }
}

/**
 * 财务数据表格列配置
 */
export interface FinancialTableColumn {
  /** 列键 */
  key: string
  /** 列标题 */
  title: string
  /** 数据索引 */
  dataIndex: keyof FinancialStatsItem
  /** 列宽 */
  width?: number
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义渲染函数 */
  render?: (value: any, record: FinancialStatsItem, index: number) => React.ReactNode
}

/**
 * 财务数据导出选项
 */
export interface FinancialExportOptions {
  /** 导出格式 */
  format: 'excel' | 'csv' | 'pdf'
  /** 包含的分类 */
  categories: FinancialStatsCategory[]
  /** 文件名 */
  filename?: string
  /** 是否包含图表 */
  includeCharts?: boolean
}

/**
 * 财务数据权限
 */
export interface FinancialPermissions {
  /** 查看权限 */
  view: boolean
  /** 导出权限 */
  export: boolean
  /** 刷新权限 */
  refresh: boolean
  /** 历史数据查看权限 */
  viewHistory: boolean
}

/**
 * 财务页面配置
 */
export interface FinancialPageConfig {
  /** 默认时间范围 */
  defaultTimeRange: TimeRangePreset
  /** 默认是否包含主播数据 */
  defaultIncludeAnchor: boolean
  /** 自动刷新间隔（毫秒） */
  autoRefreshInterval: number
  /** 是否启用自动刷新 */
  enableAutoRefresh: boolean
  /** 每页显示数量 */
  pageSize: number
  /** 是否显示趋势 */
  showTrend: boolean
}