/**
 * 监控管理模块类型定义
 * 
 * 基于权限管理模块的成功架构模式
 */

/**
 * 操作日志接口
 */
export interface OperLog {
  /** 日志ID */
  id: number
  /** 租户ID */
  tenantId: number
  /** 操作模块 */
  title: string
  /** 业务类型 */
  businessType: number
  /** 请求方法 */
  method: string
  /** 请求方式 */
  requestMethod: string
  /** 操作类别 */
  operatorType: number
  /** 操作人员 */
  operName: string
  /** 部门名称 */
  deptName?: string
  /** 请求URL */
  operUrl: string
  /** 主机地址 */
  operIp: string
  /** 操作地点 */
  operLocation?: string
  /** 请求参数 */
  operParam?: string
  /** 返回参数 */
  jsonResult?: string
  /** 操作状态 */
  status: number
  /** 错误消息 */
  errorMsg?: string
  /** 操作时间 */
  operTime: string
  /** 消耗时间（毫秒） */
  costTime?: number
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createBy: number
  /** 更新人 */
  updateBy: number
  /** 删除标志 */
  deleted: number
  /** 版本号 */
  version: number
}

/**
 * 登录日志接口
 */
export interface LoginLog {
  /** 日志ID */
  id: number
  /** 租户ID */
  tenantId: number
  /** 用户账号 */
  userName: string
  /** 登录IP地址 */
  ipaddr: string
  /** 登录地点 */
  loginLocation?: string
  /** 浏览器类型 */
  browser?: string
  /** 操作系统 */
  os?: string
  /** 登录状态 */
  status: number
  /** 提示消息 */
  msg?: string
  /** 访问时间 */
  loginTime: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createBy: number
  /** 更新人 */
  updateBy: number
  /** 删除标志 */
  deleted: number
  /** 版本号 */
  version: number
}

/**
 * 操作日志查询请求
 */
export interface OperLogQueryRequest {
  /** 页码 */
  pageNum: number
  /** 页面大小 */
  pageSize: number
  /** 操作模块 */
  title?: string
  /** 业务类型 */
  businessType?: number
  /** 操作人员 */
  operName?: string
  /** 操作状态 */
  status?: number
  /** 操作时间开始 */
  operTimeStart?: string
  /** 操作时间结束 */
  operTimeEnd?: string
}

/**
 * 登录日志查询请求
 */
export interface LoginLogQueryRequest {
  /** 页码 */
  pageNum: number
  /** 页面大小 */
  pageSize: number
  /** 用户账号 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
  /** 登录状态 */
  status?: number
  /** 登录时间开始 */
  loginTimeStart?: string
  /** 登录时间结束 */
  loginTimeEnd?: string
}

/**
 * 系统信息接口
 */
export interface SystemInfo {
  /** JVM名称 */
  jvmName: string
  /** JVM版本 */
  jvmVersion: string
  /** JVM厂商 */
  jvmVendor: string
  /** 启动时间 */
  startTime: number
  /** 运行时间 */
  uptime: number
  /** 操作系统名称 */
  osName: string
  /** 操作系统版本 */
  osVersion: string
  /** 操作系统架构 */
  osArch: string
  /** 可用处理器数量 */
  availableProcessors: number
  /** 堆内存已使用 */
  heapMemoryUsed: number
  /** 堆内存最大值 */
  heapMemoryMax: number
  /** 非堆内存已使用 */
  nonHeapMemoryUsed: number
  /** 非堆内存最大值 */
  nonHeapMemoryMax: number
}

/**
 * 系统统计信息接口
 */
export interface SystemStats {
  /** 今日操作数量 */
  todayOperCount: number
  /** 今日登录数量 */
  todayLoginCount: number
  /** 错误操作数量 */
  errorOperCount: number
  /** 失败登录数量 */
  failedLoginCount: number
  /** 按业务类型统计操作 */
  operByBusinessType: Array<{ businessType: number; count: number }>
  /** 按状态统计登录 */
  loginByStatus: Array<{ status: number; count: number }>
}

/**
 * 最近日志接口
 */
export interface RecentLogs {
  /** 最近操作日志 */
  recentOperLogs: OperLog[]
  /** 最近登录日志 */
  recentLoginLogs: LoginLog[]
}

/**
 * 分页结果接口
 */
export interface PageResult<T> {
  /** 数据列表 */
  records: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  pageNum: number
  /** 每页大小 */
  pageSize: number
  /** 总页数 */
  totalPages?: number
  /** 是否有下一页 */
  hasNext?: boolean
  /** 是否有上一页 */
  hasPrevious?: boolean
}

/**
 * 业务类型枚举
 */
export enum BusinessType {
  /** 其它 */
  OTHER = 0,
  /** 新增 */
  INSERT = 1,
  /** 修改 */
  UPDATE = 2,
  /** 删除 */
  DELETE = 3,
  /** 授权 */
  GRANT = 4,
  /** 导出 */
  EXPORT = 5,
  /** 导入 */
  IMPORT = 6,
  /** 强退 */
  FORCE = 7,
  /** 生成代码 */
  GENCODE = 8,
  /** 清空数据 */
  CLEAN = 9
}

/**
 * 操作状态枚举
 */
export enum OperStatus {
  /** 正常 */
  SUCCESS = 0,
  /** 异常 */
  FAIL = 1
}

/**
 * 登录状态枚举
 */
export enum LoginStatus {
  /** 成功 */
  SUCCESS = 0,
  /** 失败 */
  FAIL = 1
}

/**
 * 业务类型标签映射
 */
export const BUSINESS_TYPE_LABELS: Record<BusinessType, string> = {
  [BusinessType.OTHER]: '其它',
  [BusinessType.INSERT]: '新增',
  [BusinessType.UPDATE]: '修改',
  [BusinessType.DELETE]: '删除',
  [BusinessType.GRANT]: '授权',
  [BusinessType.EXPORT]: '导出',
  [BusinessType.IMPORT]: '导入',
  [BusinessType.FORCE]: '强退',
  [BusinessType.GENCODE]: '生成代码',
  [BusinessType.CLEAN]: '清空数据'
}

/**
 * 操作状态标签映射
 */
export const OPER_STATUS_LABELS: Record<OperStatus, string> = {
  [OperStatus.SUCCESS]: '成功',
  [OperStatus.FAIL]: '失败'
}

/**
 * 登录状态标签映射
 */
export const LOGIN_STATUS_LABELS: Record<LoginStatus, string> = {
  [LoginStatus.SUCCESS]: '成功',
  [LoginStatus.FAIL]: '失败'
}

/**
 * 获取业务类型标签
 */
export const getBusinessTypeLabel = (businessType: BusinessType): string => {
  return BUSINESS_TYPE_LABELS[businessType] || '未知'
}

/**
 * 获取操作状态标签
 */
export const getOperStatusLabel = (status: OperStatus): string => {
  return OPER_STATUS_LABELS[status] || '未知'

}

/**
 * 获取登录状态标签
 */
export const getLoginStatusLabel = (status: LoginStatus): string => {
  return LOGIN_STATUS_LABELS[status] || '未知'
}

/**
 * 获取操作状态颜色
 */
export const getOperStatusColor = (status: OperStatus): string => {
  switch (status) {
    case OperStatus.SUCCESS:
      return 'bg-green-100 text-green-800 border-green-200'
    case OperStatus.FAIL:
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 获取登录状态颜色
 */
export const getLoginStatusColor = (status: LoginStatus): string => {
  switch (status) {
    case LoginStatus.SUCCESS:
      return 'bg-green-100 text-green-800 border-green-200'
    case LoginStatus.FAIL:
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}
