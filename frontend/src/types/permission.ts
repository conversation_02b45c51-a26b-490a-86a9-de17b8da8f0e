/**
 * 权限管理相关类型定义
 * 
 * 统一重构版本 - 完整的权限管理类型系统
 */

/**
 * 权限类型枚举
 */
export enum PermissionType {
  /** 菜单权限 */
  MENU = 'MENU',
  /** 按钮权限 */
  BUTTON = 'BUTTON',
  /** API权限 */
  API = 'API',
  /** 数据权限 */
  DATA = 'DATA'
}

/**
 * 权限状态枚举
 */
export enum PermissionStatus {
  /** 启用 */
  ENABLED = 1,
  /** 禁用 */
  DISABLED = 0
}

/**
 * 权限实体接口
 */
export interface Permission {
  /** 权限ID */
  id: number
  /** 权限编码 */
  permissionCode: string
  /** 权限名称 */
  permissionName: string
  /** 权限类型 */
  permissionType: PermissionType
  /** 资源路径 */
  resourcePath?: string
  /** HTTP方法 */
  method?: string
  /** 父权限ID */
  parentId?: number
  /** 排序顺序 */
  sortOrder: number
  /** 状态 */
  status: PermissionStatus
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 创建者 */
  createBy?: string
  /** 更新者 */
  updateBy?: string
  /** 租户ID */
  tenantId?: number
  /** 子权限列表（树形结构） */
  children?: Permission[]
}

/**
 * 权限查询请求参数
 */
export interface PermissionQueryRequest {
  /** 页码 */
  pageNum?: number
  /** 页面大小 */
  pageSize?: number
  /** 权限编码 */
  permissionCode?: string
  /** 权限名称 */
  permissionName?: string
  /** 权限类型 */
  permissionType?: PermissionType
  /** 状态 */
  status?: PermissionStatus
  /** 父权限ID */
  parentId?: number
  /** 创建时间范围 - 开始 */
  createTimeStart?: string
  /** 创建时间范围 - 结束 */
  createTimeEnd?: string
}

/**
 * 权限创建请求参数
 */
export interface PermissionCreateRequest {
  /** 权限编码 */
  permissionCode: string
  /** 权限名称 */
  permissionName: string
  /** 权限类型 */
  permissionType: PermissionType
  /** 资源路径 */
  resourcePath?: string
  /** HTTP方法 */
  method?: string
  /** 父权限ID */
  parentId?: number
  /** 排序顺序 */
  sortOrder: number
  /** 状态 */
  status: PermissionStatus
  /** 备注 */
  remark?: string
}

/**
 * 权限更新请求参数
 */
export interface PermissionUpdateRequest extends PermissionCreateRequest {
  /** 权限ID */
  id: number
}

/**
 * 权限状态标签映射
 */
export const PERMISSION_STATUS_LABELS: Record<PermissionStatus, string> = {
  [PermissionStatus.ENABLED]: '启用',
  [PermissionStatus.DISABLED]: '禁用'
}

/**
 * 权限类型标签映射
 */
export const PERMISSION_TYPE_LABELS: Record<PermissionType, string> = {
  [PermissionType.MENU]: '菜单权限',
  [PermissionType.BUTTON]: '按钮权限',
  [PermissionType.API]: 'API权限',
  [PermissionType.DATA]: '数据权限'
}

/**
 * HTTP方法选项
 */
export const HTTP_METHODS = [
  'GET',
  'POST',
  'PUT',
  'DELETE',
  'PATCH',
  'HEAD',
  'OPTIONS'
] as const

/**
 * 获取权限状态标签
 */
export const getPermissionStatusLabel = (status: PermissionStatus): string => {
  return PERMISSION_STATUS_LABELS[status] || '未知'
}

/**
 * 获取权限类型标签
 */
export const getPermissionTypeLabel = (type: PermissionType): string => {
  return PERMISSION_TYPE_LABELS[type] || '未知'
}

/**
 * 权限状态颜色映射
 */
export const getPermissionStatusColor = (status: PermissionStatus): string => {
  switch (status) {
    case PermissionStatus.ENABLED:
      return 'bg-green-100 text-green-800 border-green-200'
    case PermissionStatus.DISABLED:
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 权限类型颜色映射
 */
export const getPermissionTypeColor = (type: PermissionType): string => {
  switch (type) {
    case PermissionType.MENU:
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case PermissionType.BUTTON:
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case PermissionType.API:
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case PermissionType.DATA:
      return 'bg-teal-100 text-teal-800 border-teal-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 权限树节点接口
 */
export interface PermissionTreeNode extends Permission {
  /** 节点层级 */
  level: number
  /** 是否展开 */
  expanded?: boolean
  /** 是否为叶子节点 */
  isLeaf?: boolean
  /** 节点路径 */
  path?: string[]
}

/**
 * 权限分配接口
 */
export interface PermissionAssignment {
  /** 角色ID */
  roleId: number
  /** 权限ID列表 */
  permissionIds: number[]
}

/**
 * 权限检查结果接口
 */
export interface PermissionCheckResult {
  /** 是否有权限 */
  hasPermission: boolean
  /** 权限详情 */
  permission?: Permission
  /** 错误信息 */
  message?: string
}

/**
 * 分页结果接口
 */
export interface PageResult<T> {
  /** 数据列表 */
  records: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  pageNum: number
  /** 每页大小 */
  pageSize: number
  /** 总页数 */
  totalPages?: number
  /** 是否有下一页 */
  hasNext?: boolean
  /** 是否有上一页 */
  hasPrevious?: boolean
}
