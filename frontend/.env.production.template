# jCloud前端生产环境配置模板
# 复制此文件为 .env.production.local 并根据实际部署环境修改配置

# ==================== 基础配置 ====================
# 应用标题
VITE_APP_TITLE=jCloud权限管理系统

# API基础URL - 根据实际部署环境修改
# 如果前后端部署在同一域名下，使用相对路径
VITE_API_BASE_URL=/api
# 如果后端部署在不同域名，使用完整URL
# VITE_API_BASE_URL=https://api.yourdomain.com/api

# 应用版本
VITE_APP_VERSION=1.0.0

# ==================== 功能开关 ====================
# 是否启用Mock数据（生产环境建议关闭）
VITE_USE_MOCK=false

# 是否启用开发工具（生产环境建议关闭）
VITE_ENABLE_DEVTOOLS=false

# 是否启用错误监控
VITE_ENABLE_ERROR_MONITOR=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 是否启用PWA
VITE_ENABLE_PWA=false

# ==================== 日志配置 ====================
# 日志级别 (error, warn, info, debug)
# 生产环境建议使用 error 或 warn
VITE_LOG_LEVEL=warn

# ==================== CDN配置 ====================
# CDN基础URL（如果使用CDN加速静态资源）
VITE_CDN_BASE_URL=

# ==================== 文件上传配置 ====================
# 文件上传大小限制（MB）
VITE_UPLOAD_MAX_SIZE=10

# 支持的文件类型
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# ==================== 分页配置 ====================
# 分页默认大小
VITE_DEFAULT_PAGE_SIZE=20

# ==================== 存储配置 ====================
# Token存储键名
VITE_TOKEN_KEY=jcloud_token

# 用户信息存储键名
VITE_USER_INFO_KEY=jcloud_user_info

# 主题存储键名
VITE_THEME_KEY=jcloud_theme

# 语言存储键名
VITE_LOCALE_KEY=jcloud_locale

# ==================== 国际化配置 ====================
# 默认语言
VITE_DEFAULT_LOCALE=zh-CN

# 是否启用国际化
VITE_ENABLE_I18N=false

# ==================== 路由配置 ====================
# 路由模式 (hash | history)
# history模式需要服务器支持，hash模式兼容性更好
VITE_ROUTER_MODE=history

# 路由基础路径
VITE_ROUTER_BASE=/

# 是否启用路由缓存
VITE_ENABLE_ROUTE_CACHE=true

# 缓存最大数量
VITE_CACHE_MAX_COUNT=10

# ==================== 网络请求配置 ====================
# 请求超时时间（毫秒）
VITE_REQUEST_TIMEOUT=10000

# 重试次数
VITE_REQUEST_RETRY_COUNT=3

# 重试延迟（毫秒）
VITE_REQUEST_RETRY_DELAY=1000

# ==================== 安全配置 ====================
# 是否启用HTTPS（如果部署环境支持）
# VITE_ENABLE_HTTPS=true

# CSP配置（如果需要）
# VITE_CSP_POLICY=default-src 'self'

# ==================== 监控配置 ====================
# 错误监控服务URL（如Sentry等）
# VITE_ERROR_MONITOR_URL=

# 性能监控服务URL
# VITE_PERFORMANCE_MONITOR_URL=

# ==================== 第三方服务配置 ====================
# 地图服务API Key（如果使用）
# VITE_MAP_API_KEY=

# 统计服务ID（如Google Analytics等）
# VITE_ANALYTICS_ID=
