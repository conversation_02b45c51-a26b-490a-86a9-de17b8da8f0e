# jCloud项目Docker Compose配置文件
version: '3.8'

# 网络配置
networks:
  jcloud-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
  backend-logs:
    driver: local
  backend-uploads:
    driver: local

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0.33
    container_name: jcloud-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-jcloud123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-jcloud}
      MYSQL_USER: ${MYSQL_USER:-jcloud}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-jcloud123456}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./backend/jcloud-admin/src/main/resources/sql:/docker-entrypoint-initdb.d:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=1000
      --innodb_buffer_pool_size=256M
      --innodb_log_file_size=128M
      --innodb_flush_log_at_trx_commit=2
      --innodb_flush_method=O_DIRECT
      --slow_query_log=1
      --slow_query_log_file=/var/lib/mysql/slow.log
      --long_query_time=2
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7.2-alpine
    container_name: jcloud-redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD:-jcloud123456}
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 10s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: jcloud/backend:1.0.0
    container_name: jcloud-backend
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # 数据库配置
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE:-jcloud}
      DB_USERNAME: ${MYSQL_USER:-jcloud}
      DB_PASSWORD: ${MYSQL_PASSWORD:-jcloud123456}
      DB_SSL: false
      
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-jcloud123456}
      REDIS_DATABASE: 10
      
      # 应用配置
      SERVER_PORT: 8081
      SPRING_PROFILES_ACTIVE: prod
      LOG_PATH: /app/logs
      FILE_UPLOAD_PATH: /app/uploads
      
      # JVM配置
      JAVA_OPTS: >
        -Xms512m -Xmx1g
        -XX:+UseG1GC
        -XX:+UseStringDeduplication
        -XX:MaxGCPauseMillis=200
        -Djava.security.egd=file:/dev/./urandom
        -Dfile.encoding=UTF-8
        -Duser.timezone=Asia/Shanghai
      
      # 时区
      TZ: Asia/Shanghai
    ports:
      - "${BACKEND_PORT:-8081}:8081"
    volumes:
      - backend-logs:/app/logs
      - backend-uploads:/app/uploads
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/api/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: jcloud/frontend:1.0.0
    container_name: jcloud-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${FRONTEND_PORT:-80}:80"
    networks:
      jcloud-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx反向代理（可选，用于生产环境）
  nginx:
    image: nginx:1.25-alpine
    container_name: jcloud-nginx
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${NGINX_PORT:-8080}:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    networks:
      jcloud-network:
        ipv4_address: ***********
    profiles:
      - production
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3
