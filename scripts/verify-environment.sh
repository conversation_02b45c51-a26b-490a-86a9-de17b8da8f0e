#!/bin/bash

# jCloud项目环境验证脚本
# 用于验证Rocky Linux环境安装是否成功
# 
# 作者: jCloud Team
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
REQUIRED_JAVA_VERSION="21"
REQUIRED_NODE_VERSION="18"
REQUIRED_PORTS=(5174 8081 3306 6379 80)

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((PASSED_CHECKS++))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}              jCloud项目环境验证脚本${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 检查系统信息
check_system_info() {
    echo -e "${BLUE}=== 系统信息检查 ===${NC}"
    ((TOTAL_CHECKS++))
    
    if grep -q "Rocky Linux release 9" /etc/redhat-release 2>/dev/null; then
        log_success "操作系统: $(cat /etc/redhat-release)"
    else
        log_error "操作系统: 不是Rocky Linux 9"
    fi
    
    ((TOTAL_CHECKS++))
    if [ "$(uname -m)" = "x86_64" ]; then
        log_success "系统架构: $(uname -m)"
    else
        log_error "系统架构: $(uname -m) (需要x86_64)"
    fi
    
    echo ""
}

# 检查Java环境
check_java() {
    echo -e "${BLUE}=== Java环境检查 ===${NC}"
    ((TOTAL_CHECKS++))
    
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
        if [ "$java_version" = "$REQUIRED_JAVA_VERSION" ]; then
            log_success "Java版本: $(java -version 2>&1 | head -1)"
        else
            log_error "Java版本: $java_version (需要$REQUIRED_JAVA_VERSION)"
        fi
    else
        log_error "Java: 未安装"
    fi
    
    ((TOTAL_CHECKS++))
    if [ -n "$JAVA_HOME" ] && [ -d "$JAVA_HOME" ]; then
        log_success "JAVA_HOME: $JAVA_HOME"
    else
        log_error "JAVA_HOME: 未设置或路径不存在"
    fi
    
    echo ""
}

# 检查Maven环境
check_maven() {
    echo -e "${BLUE}=== Maven环境检查 ===${NC}"
    ((TOTAL_CHECKS++))
    
    if command -v mvn &> /dev/null; then
        local maven_version=$(mvn -version | head -1 | awk '{print $3}')
        log_success "Maven版本: $maven_version"
    else
        log_error "Maven: 未安装"
    fi
    
    echo ""
}

# 检查Node.js环境
check_nodejs() {
    echo -e "${BLUE}=== Node.js环境检查 ===${NC}"
    ((TOTAL_CHECKS++))
    
    if command -v node &> /dev/null; then
        local node_version=$(node -v | sed 's/v//' | awk -F '.' '{print $1}')
        if [ "$node_version" -ge "$REQUIRED_NODE_VERSION" ]; then
            log_success "Node.js版本: $(node -v)"
        else
            log_error "Node.js版本: $(node -v) (需要v$REQUIRED_NODE_VERSION+)"
        fi
    else
        log_error "Node.js: 未安装"
    fi
    
    ((TOTAL_CHECKS++))
    if command -v npm &> /dev/null; then
        log_success "npm版本: $(npm -v)"
    else
        log_error "npm: 未安装"
    fi
    
    ((TOTAL_CHECKS++))
    if command -v pnpm &> /dev/null; then
        log_success "pnpm版本: $(pnpm -v)"
    else
        log_error "pnpm: 未安装"
    fi
    
    echo ""
}

# 检查服务状态
check_services() {
    echo -e "${BLUE}=== 服务状态检查 ===${NC}"
    
    local services=("nginx" "mysqld" "redis" "firewalld")
    
    for service in "${services[@]}"; do
        ((TOTAL_CHECKS++))
        if systemctl is-active --quiet "$service"; then
            log_success "$service: 运行中"
        else
            log_error "$service: 未运行"
        fi
        
        ((TOTAL_CHECKS++))
        if systemctl is-enabled --quiet "$service"; then
            log_success "$service: 已设置开机自启"
        else
            log_error "$service: 未设置开机自启"
        fi
    done
    
    echo ""
}

# 检查端口监听
check_ports() {
    echo -e "${BLUE}=== 端口监听检查 ===${NC}"
    
    for port in "${REQUIRED_PORTS[@]}"; do
        ((TOTAL_CHECKS++))
        if netstat -tlnp | grep -q ":$port "; then
            local process=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | head -1)
            log_success "端口 $port: 已监听 ($process)"
        else
            log_error "端口 $port: 未监听"
        fi
    done
    
    echo ""
}

# 检查防火墙规则
check_firewall() {
    echo -e "${BLUE}=== 防火墙规则检查 ===${NC}"
    
    if systemctl is-active --quiet firewalld; then
        for port in "${REQUIRED_PORTS[@]}"; do
            ((TOTAL_CHECKS++))
            if firewall-cmd --list-ports | grep -q "$port/tcp"; then
                log_success "防火墙端口 $port/tcp: 已开放"
            else
                log_error "防火墙端口 $port/tcp: 未开放"
            fi
        done
    else
        log_warning "防火墙服务未运行，无法检查规则"
    fi
    
    echo ""
}

# 检查数据库连接
check_database() {
    echo -e "${BLUE}=== 数据库连接检查 ===${NC}"
    
    # 检查MySQL连接
    ((TOTAL_CHECKS++))
    if systemctl is-active --quiet mysqld; then
        if mysql -e "SELECT 1;" &> /dev/null; then
            log_success "MySQL: 连接正常"
        else
            log_error "MySQL: 连接失败"
        fi
    else
        log_error "MySQL: 服务未运行"
    fi
    
    # 检查Redis连接
    ((TOTAL_CHECKS++))
    if systemctl is-active --quiet redis; then
        if redis-cli ping | grep -q "PONG"; then
            log_success "Redis: 连接正常"
        else
            log_error "Redis: 连接失败"
        fi
    else
        log_error "Redis: 服务未运行"
    fi
    
    echo ""
}

# 检查系统资源
check_system_resources() {
    echo -e "${BLUE}=== 系统资源检查 ===${NC}"
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    local available_mem=$(free -m | awk 'NR==2{print $7}')
    
    ((TOTAL_CHECKS++))
    if [ "$total_mem" -ge 2048 ]; then
        log_success "总内存: ${total_mem}MB (可用: ${available_mem}MB)"
    else
        log_error "总内存: ${total_mem}MB (建议至少2GB)"
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    
    ((TOTAL_CHECKS++))
    if [ "$disk_usage" -lt 80 ]; then
        log_success "磁盘使用率: ${disk_usage}%"
    else
        log_error "磁盘使用率: ${disk_usage}% (建议低于80%)"
    fi
    
    # 检查CPU核心数
    local cpu_cores=$(nproc)
    
    ((TOTAL_CHECKS++))
    if [ "$cpu_cores" -ge 2 ]; then
        log_success "CPU核心数: $cpu_cores"
    else
        log_error "CPU核心数: $cpu_cores (建议至少2核)"
    fi
    
    echo ""
}

# 检查网络连接
check_network() {
    echo -e "${BLUE}=== 网络连接检查 ===${NC}"
    
    ((TOTAL_CHECKS++))
    if ping -c 1 8.8.8.8 &> /dev/null; then
        log_success "外网连接: 正常"
    else
        log_error "外网连接: 失败"
    fi
    
    ((TOTAL_CHECKS++))
    if curl -s --connect-timeout 5 https://www.baidu.com > /dev/null; then
        log_success "HTTPS连接: 正常"
    else
        log_error "HTTPS连接: 失败"
    fi
    
    echo ""
}

# 显示验证结果
show_results() {
    echo -e "${CYAN}=== 验证结果汇总 ===${NC}"
    echo ""
    echo -e "${GREEN}通过检查: $PASSED_CHECKS${NC}"
    echo -e "${RED}失败检查: $FAILED_CHECKS${NC}"
    echo -e "${BLUE}总计检查: $TOTAL_CHECKS${NC}"
    echo ""
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    if [ "$success_rate" -ge 90 ]; then
        echo -e "${GREEN}✓ 环境验证通过率: $success_rate% - 环境配置良好！${NC}"
    elif [ "$success_rate" -ge 70 ]; then
        echo -e "${YELLOW}! 环境验证通过率: $success_rate% - 存在一些问题，建议修复${NC}"
    else
        echo -e "${RED}✗ 环境验证通过率: $success_rate% - 存在严重问题，需要重新安装${NC}"
    fi
    
    echo ""
}

# 显示修复建议
show_recommendations() {
    if [ "$FAILED_CHECKS" -gt 0 ]; then
        echo -e "${YELLOW}=== 修复建议 ===${NC}"
        echo ""
        echo "如果验证失败，请尝试以下操作："
        echo ""
        echo "1. 重新运行安装脚本:"
        echo "   sudo ./scripts/install-rocky-linux.sh"
        echo ""
        echo "2. 手动启动服务:"
        echo "   sudo systemctl start nginx mysqld redis firewalld"
        echo "   sudo systemctl enable nginx mysqld redis firewalld"
        echo ""
        echo "3. 检查防火墙规则:"
        echo "   sudo firewall-cmd --list-all"
        echo ""
        echo "4. 查看服务日志:"
        echo "   sudo journalctl -u nginx -f"
        echo "   sudo journalctl -u mysqld -f"
        echo "   sudo journalctl -u redis -f"
        echo ""
        echo "5. 运行系统优化脚本:"
        echo "   sudo ./scripts/optimize-rocky-linux.sh"
        echo ""
    fi
}

# 主函数
main() {
    show_header
    
    # 执行所有检查
    check_system_info
    check_java
    check_maven
    check_nodejs
    check_services
    check_ports
    check_firewall
    check_database
    check_system_resources
    check_network
    
    # 显示结果
    show_results
    show_recommendations
    
    # 返回适当的退出码
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
