#!/bin/bash

# jCloud验证码接口404错误修复脚本
# 专门用于修复传统部署环境中的API路径重复问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVER_HOST="data.voltskins.top"
BACKEND_PORT="8081"
NGINX_CONFIG_PATH="/etc/nginx/nginx.conf"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 诊断当前问题
diagnose_issue() {
    log_info "诊断验证码接口问题..."
    
    echo "=== 测试后端直连 ==="
    if curl -s "http://127.0.0.1:${BACKEND_PORT}/api/auth/captcha/config" > /dev/null; then
        log_success "后端服务正常 (http://127.0.0.1:${BACKEND_PORT}/api/auth/captcha/config)"
    else
        log_error "后端服务异常"
        return 1
    fi
    
    echo "=== 测试Nginx代理 ==="
    local response=$(curl -s -w "%{http_code}" "http://${SERVER_HOST}/api/auth/captcha/config" -o /dev/null)
    if [ "$response" = "200" ]; then
        log_success "Nginx代理正常"
        return 0
    else
        log_error "Nginx代理异常，状态码: $response"
        log_warning "这通常是由于API路径重复导致的404错误"
        return 1
    fi
}

# 备份当前Nginx配置
backup_nginx_config() {
    log_info "备份当前Nginx配置..."
    
    if [ -f "$NGINX_CONFIG_PATH" ]; then
        local backup_file="${NGINX_CONFIG_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
        sudo cp "$NGINX_CONFIG_PATH" "$backup_file"
        log_success "已备份到: $backup_file"
    else
        log_warning "Nginx配置文件不存在: $NGINX_CONFIG_PATH"
    fi
}

# 应用修复配置
apply_fix() {
    log_info "应用验证码接口修复..."
    
    # 检查修复配置文件是否存在
    if [ ! -f "frontend/nginx-prod.conf" ]; then
        log_error "修复配置文件不存在: frontend/nginx-prod.conf"
        log_info "请确保在项目根目录执行此脚本"
        return 1
    fi
    
    # 备份并应用新配置
    backup_nginx_config
    
    # 复制新配置
    sudo cp frontend/nginx-prod.conf "$NGINX_CONFIG_PATH"
    log_success "已应用修复配置"
    
    # 测试配置
    if sudo nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        return 1
    fi
}

# 重启Nginx服务
restart_nginx() {
    log_info "重启Nginx服务..."
    
    if sudo systemctl reload nginx; then
        log_success "Nginx配置重载成功"
    else
        log_warning "配置重载失败，尝试重启..."
        if sudo systemctl restart nginx; then
            log_success "Nginx服务重启成功"
        else
            log_error "Nginx服务重启失败"
            return 1
        fi
    fi
    
    # 检查服务状态
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未正常运行"
        return 1
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    # 等待服务启动
    sleep 2
    
    echo "=== 测试验证码接口 ==="
    local response=$(curl -s -w "%{http_code}" "http://${SERVER_HOST}/api/auth/captcha/generate" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        log_success "✅ 验证码接口修复成功！"
        
        # 显示测试结果
        echo ""
        log_info "测试结果："
        echo "  ✅ 验证码生成: http://${SERVER_HOST}/api/auth/captcha/generate"
        echo "  ✅ 验证码配置: http://${SERVER_HOST}/api/auth/captcha/config"
        echo "  ✅ 前端页面: http://${SERVER_HOST}"
        
        return 0
    else
        log_error "❌ 验证码接口仍然异常，状态码: $response"
        return 1
    fi
}

# 显示详细诊断信息
show_detailed_diagnosis() {
    log_info "显示详细诊断信息..."
    
    echo "=== 问题分析 ==="
    echo "问题原因: API路径重复导致404错误"
    echo "- 后端context-path: /api"
    echo "- 前端请求路径: /api/auth/captcha/generate"
    echo "- Nginx代理前: http://data.voltskins.top/api/auth/captcha/generate"
    echo "- Nginx代理后: http://127.0.0.1:8081/api/auth/captcha/generate"
    echo "- 后端实际接收: /api/api/auth/captcha/generate (404)"
    echo ""
    
    echo "=== 修复方案 ==="
    echo "修改Nginx配置中的proxy_pass:"
    echo "- 修复前: proxy_pass http://backend;"
    echo "- 修复后: proxy_pass http://backend/api/;"
    echo ""
    
    echo "=== 当前状态 ==="
    echo "后端服务: $(curl -s -w "%{http_code}" "http://127.0.0.1:${BACKEND_PORT}/api/auth/captcha/config" -o /dev/null)"
    echo "Nginx代理: $(curl -s -w "%{http_code}" "http://${SERVER_HOST}/api/auth/captcha/config" -o /dev/null)"
    echo "Nginx状态: $(sudo systemctl is-active nginx)"
}

# 主函数
main() {
    log_info "🔧 开始修复jCloud验证码接口404错误..."
    
    # 检查当前目录
    if [ ! -f "frontend/nginx-prod.conf" ]; then
        log_error "请在jCloud项目根目录执行此脚本"
        exit 1
    fi
    
    # 诊断问题
    if diagnose_issue; then
        log_success "验证码接口已正常，无需修复"
        exit 0
    fi
    
    # 显示问题分析
    show_detailed_diagnosis
    
    # 询问是否继续修复
    echo ""
    read -p "是否继续修复？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消修复"
        exit 0
    fi
    
    # 执行修复步骤
    if apply_fix && restart_nginx && verify_fix; then
        log_success "🎉 验证码接口404错误修复完成！"
        echo ""
        log_info "现在可以正常使用验证码功能了"
        log_info "前端登录页面: http://${SERVER_HOST}"
    else
        log_error "❌ 修复失败，请检查错误信息"
        show_detailed_diagnosis
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
