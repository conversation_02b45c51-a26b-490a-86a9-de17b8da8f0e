# jCloud项目 Rocky Linux 9.4 一键环境安装脚本

## 🎯 脚本功能概述

为jCloud项目创建了完整的Rocky Linux 9.4环境安装解决方案，包含4个核心脚本文件：

### 📁 脚本文件列表

1. **`install-rocky-linux.sh`** - 主安装脚本（587行）
2. **`optimize-rocky-linux.sh`** - 系统优化脚本（300行）
3. **`verify-environment.sh`** - 环境验证脚本（300行）
4. **`README-Rocky-Linux-Setup.md`** - 详细使用文档

## 🔧 主要功能特性

### ✅ 自动安装组件
- **Java 21 JDK** - OpenJDK 21 + 环境变量配置
- **Maven 3.9+** - 从Apache官方下载安装
- **Node.js 18+** - 通过NodeSource仓库安装
- **pnpm** - 全局安装最新版本
- **Nginx** - 系统包管理器安装
- **MySQL 8.0+** - 包含数据库和用户创建
- **Redis 7.0+** - 包含密码和网络配置

### ✅ 系统配置
- 创建`jcloud`系统用户
- 配置防火墙规则（开放5174、8081、3306、6379、80端口）
- 设置所有服务开机自启
- 创建必要的目录结构
- 配置环境变量

### ✅ 安全配置
- MySQL root密码设置
- Redis密码配置
- 防火墙规则配置
- 系统用户权限设置

## 🚀 使用方法

### 1. 上传脚本到Rocky Linux服务器

```bash
# 将脚本文件上传到服务器
scp scripts/*.sh user@your-server:/tmp/
scp scripts/README-Rocky-Linux-Setup.md user@your-server:/tmp/
```

### 2. 设置执行权限

```bash
# 在Rocky Linux服务器上执行
chmod +x /tmp/*.sh
```

### 3. 执行安装

```bash
# 一键安装环境
sudo /tmp/install-rocky-linux.sh

# 系统优化（可选）
sudo /tmp/optimize-rocky-linux.sh

# 验证安装
sudo /tmp/verify-environment.sh
```

## 📊 安装后的环境配置

### 🔗 服务端口配置
```
前端服务: 5174
后端API: 8081
Nginx: 80
MySQL: 3306
Redis: 6379
```

### 🗄️ 数据库配置
```yaml
MySQL:
  Host: localhost
  Port: 3306
  Root Password: jcloud123456
  Databases: cs2_skin_platform, vimbox
  User: voltskins
  Password: ShbAeEVw7RNh8arDzjN4eZhsh@

Redis:
  Host: localhost
  Port: 6379
  Password: DAANFtJj3n5PtbM8zDkPwh5PG
  Database: 10
```

### 📁 目录结构
```
/opt/jcloud/          # jCloud安装目录
├── logs/             # 应用日志
├── config/           # 配置文件
└── data/             # 数据文件

/var/log/jcloud-install.log    # 安装日志
/var/log/jcloud-optimize.log   # 优化日志
```

## 🔍 验证检查项目

验证脚本会检查30+项目：

### 系统检查
- ✅ Rocky Linux 9.4版本
- ✅ x86_64架构
- ✅ 内存≥2GB
- ✅ 磁盘使用率<80%
- ✅ CPU核心数≥2

### 软件检查
- ✅ Java 21安装和JAVA_HOME
- ✅ Maven 3.9+版本
- ✅ Node.js 18+和npm
- ✅ pnpm安装

### 服务检查
- ✅ Nginx运行状态和开机自启
- ✅ MySQL运行状态和连接测试
- ✅ Redis运行状态和连接测试
- ✅ 防火墙服务状态

### 网络检查
- ✅ 所有必需端口监听状态
- ✅ 防火墙端口开放规则
- ✅ 外网连接和HTTPS访问

## ⚡ 性能优化特性

### 内核参数优化
- 网络性能调优（TCP BBR、连接数限制）
- 文件系统优化（文件句柄数、inotify限制）
- 虚拟内存优化（swappiness、dirty ratio）
- 安全参数强化

### 应用服务优化
- **MySQL**: 连接池、缓冲区、查询缓存、慢查询日志
- **Redis**: 内存策略、持久化、网络优化
- **Nginx**: 工作进程、连接数、Gzip压缩、缓冲区
- **系统限制**: 文件句柄数、进程数限制提升

### 监控工具
- htop、iotop、nethogs、sysstat

## 🛡️ 错误处理和日志

### 错误处理机制
- `set -e` 遇错即停
- 详细的错误日志记录
- 彩色输出便于识别问题
- 幂等性设计，可重复执行

### 日志系统
- 安装过程完整日志记录
- 服务状态实时监控
- 错误信息详细输出
- 日志轮转配置

## 📋 部署后续步骤

环境安装完成后，需要：

1. **构建后端项目**
   ```bash
   cd /path/to/jcloud/backend
   mvn clean package -Pprod
   ```

2. **构建前端项目**
   ```bash
   cd /path/to/jcloud/frontend
   pnpm install && pnpm build
   ```

3. **配置Nginx代理**
   ```bash
   sudo cp frontend/nginx-prod.conf /etc/nginx/nginx.conf
   sudo systemctl reload nginx
   ```

4. **启动后端服务**
   ```bash
   java -jar target/jcloud-admin-*.jar --spring.profiles.active=prod
   ```

## 🎯 脚本优势

### ✅ 完整性
- 涵盖所有必需组件
- 包含系统优化配置
- 提供验证和故障排除

### ✅ 可靠性
- 幂等性设计
- 错误处理机制
- 详细日志记录

### ✅ 易用性
- 一键安装执行
- 彩色输出界面
- 详细的使用文档

### ✅ 专业性
- 针对生产环境优化
- 安全配置考虑
- 性能调优包含

## 📞 技术支持

如遇问题，请：
1. 查看 `/var/log/jcloud-install.log` 安装日志
2. 运行 `verify-environment.sh` 检查环境
3. 参考 `README-Rocky-Linux-Setup.md` 故障排除部分

---

**注意**: 脚本专为Rocky Linux 9.4 x86_64设计，确保在正确的系统环境下使用。
