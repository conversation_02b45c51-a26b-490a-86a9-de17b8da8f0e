#!/bin/bash

# jCloud项目Rocky Linux 9.4系统优化脚本
# 用于优化系统性能和安全配置
# 
# 作者: jCloud Team
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
LOG_FILE="/var/log/jcloud-optimize.log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# 优化系统内核参数
optimize_kernel_parameters() {
    log_info "优化系统内核参数..."
    
    # 备份原始配置
    cp /etc/sysctl.conf /etc/sysctl.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 创建jCloud优化配置
    cat > /etc/sysctl.d/99-jcloud.conf << 'EOF'
# jCloud系统优化配置

# 网络优化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.core.netdev_max_backlog = 5000
net.core.somaxconn = 65535
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_tw_reuse = 1
net.ipv4.ip_local_port_range = 1024 65535

# 文件系统优化
fs.file-max = 1000000
fs.inotify.max_user_watches = 524288
fs.inotify.max_user_instances = 512

# 虚拟内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1

# 安全优化
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
EOF
    
    # 应用配置
    sysctl -p /etc/sysctl.d/99-jcloud.conf >> "$LOG_FILE" 2>&1
    
    log_success "内核参数优化完成"
}

# 优化系统限制
optimize_system_limits() {
    log_info "优化系统限制..."
    
    # 备份原始配置
    cp /etc/security/limits.conf /etc/security/limits.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 添加jCloud优化配置
    cat >> /etc/security/limits.conf << 'EOF'

# jCloud系统限制优化
* soft nofile 65535
* hard nofile 65535
* soft nproc 65535
* hard nproc 65535
jcloud soft nofile 65535
jcloud hard nofile 65535
jcloud soft nproc 65535
jcloud hard nproc 65535
root soft nofile 65535
root hard nofile 65535
EOF
    
    # 配置systemd限制
    mkdir -p /etc/systemd/system.conf.d
    cat > /etc/systemd/system.conf.d/jcloud.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65535
DefaultLimitNPROC=65535
EOF
    
    # 重载systemd配置
    systemctl daemon-reload
    
    log_success "系统限制优化完成"
}

# 优化MySQL配置
optimize_mysql() {
    log_info "优化MySQL配置..."
    
    if ! systemctl is-active --quiet mysqld; then
        log_warning "MySQL服务未运行，跳过优化"
        return 0
    fi
    
    # 备份原始配置
    cp /etc/my.cnf.d/mysql-server.cnf /etc/my.cnf.d/mysql-server.cnf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 创建优化配置
    cat > /etc/my.cnf.d/jcloud-mysql.cnf << 'EOF'
[mysqld]
# jCloud MySQL优化配置

# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 200
max_connect_errors = 1000
wait_timeout = 28800
interactive_timeout = 28800

# 缓冲区配置
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 临时表配置
tmp_table_size = 64M
max_heap_table_size = 64M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
EOF
    
    # 创建日志目录
    mkdir -p /var/log/mysql
    chown mysql:mysql /var/log/mysql
    
    # 重启MySQL服务
    systemctl restart mysqld
    
    log_success "MySQL配置优化完成"
}

# 优化Redis配置
optimize_redis() {
    log_info "优化Redis配置..."
    
    if ! systemctl is-active --quiet redis; then
        log_warning "Redis服务未运行，跳过优化"
        return 0
    fi
    
    # 备份原始配置
    cp /etc/redis/redis.conf /etc/redis/redis.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 优化Redis配置
    cat >> /etc/redis/redis.conf << 'EOF'

# jCloud Redis优化配置
maxmemory 256mb
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 300

# 持久化优化
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# 网络优化
tcp-backlog 511
EOF
    
    # 重启Redis服务
    systemctl restart redis
    
    log_success "Redis配置优化完成"
}

# 优化Nginx配置
optimize_nginx() {
    log_info "优化Nginx配置..."
    
    if ! systemctl is-active --quiet nginx; then
        log_warning "Nginx服务未运行，跳过优化"
        return 0
    fi
    
    # 创建优化配置目录
    mkdir -p /etc/nginx/conf.d
    
    # 创建性能优化配置
    cat > /etc/nginx/conf.d/performance.conf << 'EOF'
# jCloud Nginx性能优化配置

# 工作进程优化
worker_processes auto;
worker_rlimit_nofile 65535;

# 事件优化
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

# HTTP优化
http {
    # 基础优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    
    # 缓冲区优化
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
EOF
    
    # 测试配置
    nginx -t >> "$LOG_FILE" 2>&1
    
    # 重载配置
    systemctl reload nginx
    
    log_success "Nginx配置优化完成"
}

# 配置日志轮转
configure_log_rotation() {
    log_info "配置日志轮转..."
    
    # jCloud应用日志轮转
    cat > /etc/logrotate.d/jcloud << 'EOF'
/opt/jcloud/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 jcloud jcloud
    postrotate
        /bin/kill -USR1 `cat /opt/jcloud/jcloud.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
EOF
    
    # MySQL慢查询日志轮转
    cat > /etc/logrotate.d/mysql-slow << 'EOF'
/var/log/mysql/slow.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 mysql mysql
    postrotate
        /bin/kill -USR1 `cat /var/run/mysqld/mysqld.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
EOF
    
    log_success "日志轮转配置完成"
}

# 安装监控工具
install_monitoring_tools() {
    log_info "安装系统监控工具..."
    
    # 安装htop, iotop, nethogs等监控工具
    dnf install -y htop iotop nethogs sysstat >> "$LOG_FILE" 2>&1
    
    # 启用sysstat服务
    systemctl enable sysstat
    systemctl start sysstat
    
    log_success "监控工具安装完成"
}

# 主函数
main() {
    echo -e "${BLUE}=== jCloud Rocky Linux系统优化脚本 ===${NC}"
    echo ""
    
    # 检查root权限
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本: sudo $0"
        exit 1
    fi
    
    # 创建日志文件
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    # 执行优化
    optimize_kernel_parameters
    optimize_system_limits
    optimize_mysql
    optimize_redis
    optimize_nginx
    configure_log_rotation
    install_monitoring_tools
    
    echo ""
    log_success "🎉 系统优化完成！建议重启系统以使所有配置生效。"
    echo ""
    echo -e "${YELLOW}优化内容包括:${NC}"
    echo "  ✓ 内核参数优化"
    echo "  ✓ 系统限制优化"
    echo "  ✓ MySQL性能优化"
    echo "  ✓ Redis性能优化"
    echo "  ✓ Nginx性能优化"
    echo "  ✓ 日志轮转配置"
    echo "  ✓ 监控工具安装"
    echo ""
    echo -e "${YELLOW}重启命令: ${NC}reboot"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
