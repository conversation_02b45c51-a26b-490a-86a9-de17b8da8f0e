#!/bin/bash

# jCloud生产环境部署脚本
# 用于部署到data.voltskins.top服务器

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVER_HOST="data.voltskins.top"
BACKEND_PORT="8081"
FRONTEND_PORT="80"
NGINX_CONFIG_PATH="/etc/nginx/nginx.conf"
FRONTEND_ROOT="/usr/share/nginx/html"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_backend_service() {
    log_info "检查后端服务状态..."
    
    if curl -s "http://127.0.0.1:${BACKEND_PORT}/api/actuator/health" > /dev/null; then
        log_success "后端服务运行正常"
        return 0
    else
        log_error "后端服务未运行或不可访问"
        return 1
    fi
}

# 检查验证码接口
check_captcha_api() {
    log_info "检查验证码接口..."
    
    local response=$(curl -s -w "%{http_code}" "http://127.0.0.1:${BACKEND_PORT}/api/auth/captcha/generate" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        log_success "验证码接口正常"
        return 0
    else
        log_error "验证码接口返回状态码: $response"
        return 1
    fi
}

# 更新Nginx配置
update_nginx_config() {
    log_info "更新Nginx配置..."
    
    # 备份当前配置
    if [ -f "$NGINX_CONFIG_PATH" ]; then
        sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已备份当前Nginx配置"
    fi
    
    # 复制新配置
    if [ -f "frontend/nginx-prod.conf" ]; then
        sudo cp frontend/nginx-prod.conf "$NGINX_CONFIG_PATH"
        log_success "已更新Nginx配置"
    else
        log_error "nginx-prod.conf文件不存在"
        return 1
    fi
    
    # 测试配置
    if sudo nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        return 1
    fi
}

# 重启Nginx服务
restart_nginx() {
    log_info "重启Nginx服务..."
    
    if sudo systemctl restart nginx; then
        log_success "Nginx服务重启成功"
    else
        log_error "Nginx服务重启失败"
        return 1
    fi
    
    # 检查服务状态
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未正常运行"
        return 1
    fi
}

# 测试API代理
test_api_proxy() {
    log_info "测试API代理..."
    
    # 等待服务启动
    sleep 2
    
    # 测试验证码接口
    local response=$(curl -s -w "%{http_code}" "http://${SERVER_HOST}/api/auth/captcha/generate" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        log_success "API代理测试通过"
        return 0
    else
        log_error "API代理测试失败，状态码: $response"
        return 1
    fi
}

# 显示诊断信息
show_diagnostic_info() {
    log_info "显示诊断信息..."
    
    echo "=== 服务状态 ==="
    echo "后端服务: http://127.0.0.1:${BACKEND_PORT}"
    curl -s "http://127.0.0.1:${BACKEND_PORT}/api/auth/captcha/config" | head -c 100
    echo ""
    
    echo "=== Nginx状态 ==="
    sudo systemctl status nginx --no-pager -l
    
    echo "=== 端口监听 ==="
    sudo netstat -tlnp | grep -E ":80|:8081"
    
    echo "=== 测试URL ==="
    echo "前端: http://${SERVER_HOST}"
    echo "验证码API: http://${SERVER_HOST}/api/auth/captcha/generate"
    echo "后端健康检查: http://${SERVER_HOST}/api/health"
}

# 主函数
main() {
    log_info "开始jCloud生产环境部署..."
    
    # 检查当前目录
    if [ ! -f "frontend/nginx-prod.conf" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    if check_backend_service; then
        log_success "后端服务检查通过"
    else
        log_warning "后端服务检查失败，请先启动后端服务"
        exit 1
    fi
    
    if check_captcha_api; then
        log_success "验证码接口检查通过"
    else
        log_warning "验证码接口检查失败"
    fi
    
    if update_nginx_config; then
        log_success "Nginx配置更新成功"
    else
        log_error "Nginx配置更新失败"
        exit 1
    fi
    
    if restart_nginx; then
        log_success "Nginx服务重启成功"
    else
        log_error "Nginx服务重启失败"
        exit 1
    fi
    
    if test_api_proxy; then
        log_success "API代理测试通过"
    else
        log_error "API代理测试失败"
        show_diagnostic_info
        exit 1
    fi
    
    log_success "🎉 jCloud生产环境部署完成！"
    log_info ""
    log_info "访问地址:"
    log_info "  前端: http://${SERVER_HOST}"
    log_info "  API: http://${SERVER_HOST}/api"
    log_info ""
    log_info "如果仍有问题，请检查:"
    log_info "  1. 后端服务是否在8081端口正常运行"
    log_info "  2. 防火墙是否开放80和8081端口"
    log_info "  3. Nginx错误日志: sudo tail -f /var/log/nginx/error.log"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
