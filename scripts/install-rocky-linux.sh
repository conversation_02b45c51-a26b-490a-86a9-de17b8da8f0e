#!/bin/bash

# jCloud项目Rocky Linux 9.4环境一键安装脚本
# 支持传统部署架构（非Docker容器化）
# 
# 作者: jCloud Team
# 版本: 1.0.0
# 系统: Rocky Linux 9.4 x86_64

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_VERSION="1.0.0"
LOG_FILE="/var/log/jcloud-install.log"
INSTALL_USER="jcloud"
INSTALL_DIR="/opt/jcloud"
JAVA_VERSION="21"
NODE_VERSION="18"
MYSQL_ROOT_PASSWORD="jcloud123456"
REDIS_PASSWORD="jcloud123456"

# 端口配置
FRONTEND_PORT="5174"
BACKEND_PORT="8081"
MYSQL_PORT="3306"
REDIS_PORT="6379"
NGINX_PORT="80"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1" | tee -a "$LOG_FILE"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示脚本头部信息
show_header() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}           jCloud项目 Rocky Linux 9.4 环境安装脚本${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${BLUE}版本:${NC} $SCRIPT_VERSION"
    echo -e "${BLUE}系统:${NC} Rocky Linux 9.4 x86_64"
    echo -e "${BLUE}架构:${NC} 传统部署（非Docker容器化）"
    echo -e "${BLUE}日志:${NC} $LOG_FILE"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if ! grep -q "Rocky Linux release 9" /etc/redhat-release 2>/dev/null; then
        log_warning "当前系统不是Rocky Linux 9，脚本可能需要调整"
    else
        log_success "系统检查通过: $(cat /etc/redhat-release)"
    fi
    
    # 检查架构
    if [ "$(uname -m)" != "x86_64" ]; then
        log_error "不支持的系统架构: $(uname -m)，需要x86_64"
        exit 1
    fi
    
    # 检查root权限
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本: sudo $0"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 8.8.8.8 &> /dev/null; then
        log_error "网络连接检查失败，请检查网络设置"
        exit 1
    fi
    
    log_success "系统要求检查完成"
}

# 初始化日志和目录
initialize_environment() {
    log_step "初始化安装环境..."
    
    # 创建日志文件
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"
    
    # 记录安装开始时间
    echo "=== jCloud安装开始: $(date) ===" >> "$LOG_FILE"
    
    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    
    log_success "环境初始化完成"
}

# 更新系统和安装基础工具
update_system() {
    log_step "更新系统和安装基础工具..."
    
    # 更新系统
    log_info "更新系统包..."
    dnf update -y >> "$LOG_FILE" 2>&1
    
    # 安装EPEL源
    log_info "安装EPEL源..."
    dnf install -y epel-release >> "$LOG_FILE" 2>&1
    
    # 安装基础工具
    log_info "安装基础工具..."
    dnf install -y \
        wget \
        curl \
        git \
        vim \
        unzip \
        tar \
        which \
        lsof \
        net-tools \
        firewalld \
        systemd \
        >> "$LOG_FILE" 2>&1
    
    log_success "系统更新和基础工具安装完成"
}

# 创建jcloud用户
create_jcloud_user() {
    log_step "创建jcloud用户..."
    
    if id "$INSTALL_USER" &>/dev/null; then
        log_info "用户 $INSTALL_USER 已存在"
    else
        useradd -m -s /bin/bash "$INSTALL_USER"
        log_success "用户 $INSTALL_USER 创建成功"
    fi
    
    # 设置目录权限
    chown -R "$INSTALL_USER:$INSTALL_USER" "$INSTALL_DIR"
    
    log_success "用户配置完成"
}

# 安装Java 21 JDK
install_java() {
    log_step "安装Java $JAVA_VERSION JDK..."
    
    # 检查是否已安装
    if java -version 2>&1 | grep -q "openjdk version \"$JAVA_VERSION"; then
        log_info "Java $JAVA_VERSION 已安装"
        return 0
    fi
    
    # 安装OpenJDK 21
    log_info "安装OpenJDK $JAVA_VERSION..."
    dnf install -y java-$JAVA_VERSION-openjdk java-$JAVA_VERSION-openjdk-devel >> "$LOG_FILE" 2>&1
    
    # 设置JAVA_HOME
    JAVA_HOME="/usr/lib/jvm/java-$JAVA_VERSION-openjdk"
    echo "export JAVA_HOME=$JAVA_HOME" >> /etc/profile.d/java.sh
    echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> /etc/profile.d/java.sh
    chmod +x /etc/profile.d/java.sh
    
    # 验证安装
    source /etc/profile.d/java.sh
    if java -version 2>&1 | grep -q "openjdk version \"$JAVA_VERSION"; then
        log_success "Java $JAVA_VERSION 安装成功"
        java -version 2>&1 | head -1 | tee -a "$LOG_FILE"
    else
        log_error "Java $JAVA_VERSION 安装失败"
        exit 1
    fi
}

# 安装Maven
install_maven() {
    log_step "安装Maven 3.9+..."
    
    # 检查是否已安装
    if command -v mvn &> /dev/null; then
        local current_version=$(mvn -version | head -1 | awk '{print $3}')
        log_info "Maven已安装，版本: $current_version"
        return 0
    fi
    
    # 下载并安装Maven
    local maven_version="3.9.6"
    local maven_url="https://archive.apache.org/dist/maven/maven-3/$maven_version/binaries/apache-maven-$maven_version-bin.tar.gz"
    
    log_info "下载Maven $maven_version..."
    cd /opt
    wget -q "$maven_url" -O "apache-maven-$maven_version-bin.tar.gz"
    tar -xzf "apache-maven-$maven_version-bin.tar.gz"
    ln -sf "apache-maven-$maven_version" maven
    rm -f "apache-maven-$maven_version-bin.tar.gz"
    
    # 设置环境变量
    echo "export MAVEN_HOME=/opt/maven" >> /etc/profile.d/maven.sh
    echo "export PATH=\$MAVEN_HOME/bin:\$PATH" >> /etc/profile.d/maven.sh
    chmod +x /etc/profile.d/maven.sh
    
    # 验证安装
    source /etc/profile.d/maven.sh
    if command -v mvn &> /dev/null; then
        log_success "Maven安装成功"
        mvn -version | head -1 | tee -a "$LOG_FILE"
    else
        log_error "Maven安装失败"
        exit 1
    fi
}

# 安装Node.js和pnpm
install_nodejs() {
    log_step "安装Node.js $NODE_VERSION+ 和 pnpm..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        local current_version=$(node -v | sed 's/v//')
        log_info "Node.js已安装，版本: $current_version"
    else
        # 安装NodeSource仓库
        log_info "添加NodeSource仓库..."
        curl -fsSL https://rpm.nodesource.com/setup_$NODE_VERSION.x | bash - >> "$LOG_FILE" 2>&1
        
        # 安装Node.js
        log_info "安装Node.js..."
        dnf install -y nodejs >> "$LOG_FILE" 2>&1
        
        # 验证Node.js安装
        if command -v node &> /dev/null; then
            log_success "Node.js安装成功"
            node -v | tee -a "$LOG_FILE"
        else
            log_error "Node.js安装失败"
            exit 1
        fi
    fi
    
    # 安装pnpm
    if command -v pnpm &> /dev/null; then
        log_info "pnpm已安装，版本: $(pnpm -v)"
    else
        log_info "安装pnpm..."
        npm install -g pnpm >> "$LOG_FILE" 2>&1
        
        if command -v pnpm &> /dev/null; then
            log_success "pnpm安装成功"
            pnpm -v | tee -a "$LOG_FILE"
        else
            log_error "pnpm安装失败"
            exit 1
        fi
    fi
}

# 安装Nginx
install_nginx() {
    log_step "安装Nginx..."

    # 检查是否已安装
    if command -v nginx &> /dev/null; then
        log_info "Nginx已安装，版本: $(nginx -v 2>&1 | awk '{print $3}')"
        return 0
    fi

    # 安装Nginx
    log_info "安装Nginx..."
    dnf install -y nginx >> "$LOG_FILE" 2>&1

    # 启用并启动Nginx
    systemctl enable nginx >> "$LOG_FILE" 2>&1
    systemctl start nginx >> "$LOG_FILE" 2>&1

    # 验证安装
    if systemctl is-active --quiet nginx; then
        log_success "Nginx安装并启动成功"
        nginx -v 2>&1 | tee -a "$LOG_FILE"
    else
        log_error "Nginx安装失败"
        exit 1
    fi
}

# 安装MySQL 8.0+
install_mysql() {
    log_step "安装MySQL 8.0+..."

    # 检查是否已安装
    if systemctl is-active --quiet mysqld; then
        log_info "MySQL已安装并运行"
        return 0
    fi

    # 安装MySQL
    log_info "安装MySQL服务器..."
    dnf install -y mysql-server mysql >> "$LOG_FILE" 2>&1

    # 启用并启动MySQL
    systemctl enable mysqld >> "$LOG_FILE" 2>&1
    systemctl start mysqld >> "$LOG_FILE" 2>&1

    # 等待MySQL启动
    sleep 5

    # 设置root密码
    log_info "配置MySQL root密码..."
    mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';" >> "$LOG_FILE" 2>&1 || true

    # 创建jCloud数据库和用户
    log_info "创建jCloud数据库..."
    mysql -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF >> "$LOG_FILE" 2>&1
CREATE DATABASE IF NOT EXISTS cs2_skin_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS vimbox CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'voltskins'@'%' IDENTIFIED BY 'ShbAeEVw7RNh8arDzjN4eZhsh@';
GRANT ALL PRIVILEGES ON cs2_skin_platform.* TO 'voltskins'@'%';
GRANT ALL PRIVILEGES ON vimbox.* TO 'voltskins'@'%';
FLUSH PRIVILEGES;
EOF

    # 验证安装
    if systemctl is-active --quiet mysqld; then
        log_success "MySQL安装并配置成功"
        mysql --version | tee -a "$LOG_FILE"
    else
        log_error "MySQL安装失败"
        exit 1
    fi
}

# 安装Redis 7.0+
install_redis() {
    log_step "安装Redis 7.0+..."

    # 检查是否已安装
    if systemctl is-active --quiet redis; then
        log_info "Redis已安装并运行"
        return 0
    fi

    # 安装Redis
    log_info "安装Redis服务器..."
    dnf install -y redis >> "$LOG_FILE" 2>&1

    # 配置Redis
    log_info "配置Redis..."
    sed -i "s/^# requirepass foobared/requirepass DAANFtJj3n5PtbM8zDkPwh5PG/" /etc/redis/redis.conf
    sed -i "s/^bind 127.0.0.1/bind 0.0.0.0/" /etc/redis/redis.conf

    # 启用并启动Redis
    systemctl enable redis >> "$LOG_FILE" 2>&1
    systemctl start redis >> "$LOG_FILE" 2>&1

    # 验证安装
    if systemctl is-active --quiet redis; then
        log_success "Redis安装并配置成功"
        redis-server --version | tee -a "$LOG_FILE"
    else
        log_error "Redis安装失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙规则..."

    # 启动防火墙服务
    systemctl enable firewalld >> "$LOG_FILE" 2>&1
    systemctl start firewalld >> "$LOG_FILE" 2>&1

    # 开放必要端口
    log_info "开放端口: $FRONTEND_PORT, $BACKEND_PORT, $MYSQL_PORT, $REDIS_PORT, $NGINX_PORT..."

    firewall-cmd --permanent --add-port="$FRONTEND_PORT/tcp" >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-port="$BACKEND_PORT/tcp" >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-port="$MYSQL_PORT/tcp" >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-port="$REDIS_PORT/tcp" >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-port="$NGINX_PORT/tcp" >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-service=http >> "$LOG_FILE" 2>&1
    firewall-cmd --permanent --add-service=https >> "$LOG_FILE" 2>&1

    # 重载防火墙规则
    firewall-cmd --reload >> "$LOG_FILE" 2>&1

    log_success "防火墙配置完成"
}

# 验证安装
verify_installation() {
    log_step "验证安装结果..."

    echo ""
    echo -e "${CYAN}=== 安装验证结果 ===${NC}"

    # Java验证
    if command -v java &> /dev/null; then
        echo -e "${GREEN}✓ Java:${NC} $(java -version 2>&1 | head -1)"
    else
        echo -e "${RED}✗ Java: 未安装${NC}"
    fi

    # Maven验证
    if command -v mvn &> /dev/null; then
        echo -e "${GREEN}✓ Maven:${NC} $(mvn -version | head -1 | awk '{print $3}')"
    else
        echo -e "${RED}✗ Maven: 未安装${NC}"
    fi

    # Node.js验证
    if command -v node &> /dev/null; then
        echo -e "${GREEN}✓ Node.js:${NC} $(node -v)"
    else
        echo -e "${RED}✗ Node.js: 未安装${NC}"
    fi

    # pnpm验证
    if command -v pnpm &> /dev/null; then
        echo -e "${GREEN}✓ pnpm:${NC} $(pnpm -v)"
    else
        echo -e "${RED}✗ pnpm: 未安装${NC}"
    fi

    # Nginx验证
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✓ Nginx:${NC} 运行中 ($(nginx -v 2>&1 | awk '{print $3}'))"
    else
        echo -e "${RED}✗ Nginx: 未运行${NC}"
    fi

    # MySQL验证
    if systemctl is-active --quiet mysqld; then
        echo -e "${GREEN}✓ MySQL:${NC} 运行中 ($(mysql --version | awk '{print $5}' | sed 's/,//'))"
    else
        echo -e "${RED}✗ MySQL: 未运行${NC}"
    fi

    # Redis验证
    if systemctl is-active --quiet redis; then
        echo -e "${GREEN}✓ Redis:${NC} 运行中 ($(redis-server --version | awk '{print $3}'))"
    else
        echo -e "${RED}✗ Redis: 未运行${NC}"
    fi

    echo ""
}

# 显示部署指导
show_deployment_guide() {
    log_step "显示部署指导..."

    echo ""
    echo -e "${CYAN}=== jCloud项目部署指导 ===${NC}"
    echo ""
    echo -e "${YELLOW}1. 环境信息:${NC}"
    echo "   - 安装目录: $INSTALL_DIR"
    echo "   - 运行用户: $INSTALL_USER"
    echo "   - 日志文件: $LOG_FILE"
    echo ""
    echo -e "${YELLOW}2. 服务端口:${NC}"
    echo "   - 前端服务: $FRONTEND_PORT"
    echo "   - 后端API: $BACKEND_PORT"
    echo "   - Nginx: $NGINX_PORT"
    echo "   - MySQL: $MYSQL_PORT"
    echo "   - Redis: $REDIS_PORT"
    echo ""
    echo -e "${YELLOW}3. 数据库信息:${NC}"
    echo "   - MySQL Root密码: $MYSQL_ROOT_PASSWORD"
    echo "   - 业务数据库: cs2_skin_platform, vimbox"
    echo "   - 业务用户: voltskins"
    echo "   - Redis密码: DAANFtJj3n5PtbM8zDkPwh5PG"
    echo ""
    echo -e "${YELLOW}4. 下一步操作:${NC}"
    echo "   a) 上传jCloud项目代码到服务器"
    echo "   b) 构建后端项目:"
    echo "      cd /path/to/jcloud/backend"
    echo "      mvn clean package -Pprod"
    echo ""
    echo "   c) 构建前端项目:"
    echo "      cd /path/to/jcloud/frontend"
    echo "      pnpm install"
    echo "      pnpm build"
    echo ""
    echo "   d) 部署后端服务:"
    echo "      java -jar target/jcloud-admin-*.jar --spring.profiles.active=prod"
    echo ""
    echo "   e) 配置Nginx代理:"
    echo "      cp frontend/nginx-prod.conf /etc/nginx/nginx.conf"
    echo "      systemctl reload nginx"
    echo ""
    echo -e "${YELLOW}5. 验证部署:${NC}"
    echo "   - 后端健康检查: http://localhost:$BACKEND_PORT/api/actuator/health"
    echo "   - 前端页面: http://localhost:$NGINX_PORT"
    echo "   - API接口: http://localhost:$NGINX_PORT/api/auth/captcha/generate"
    echo ""
    echo -e "${GREEN}环境安装完成！请按照上述指导进行项目部署。${NC}"
    echo ""
}

# 清理临时文件
cleanup() {
    log_step "清理临时文件..."

    # 清理下载的临时文件
    rm -f /tmp/jcloud-install-*

    log_success "清理完成"
}

# 主函数
main() {
    # 显示脚本头部
    show_header

    # 检查系统要求
    check_system_requirements

    # 初始化环境
    initialize_environment

    # 执行安装步骤
    log_info "开始安装jCloud运行环境..."
    echo ""

    update_system
    create_jcloud_user
    install_java
    install_maven
    install_nodejs
    install_nginx
    install_mysql
    install_redis
    configure_firewall

    # 验证安装
    verify_installation

    # 显示部署指导
    show_deployment_guide

    # 清理临时文件
    cleanup

    # 记录安装完成
    echo "=== jCloud安装完成: $(date) ===" >> "$LOG_FILE"

    log_success "🎉 jCloud环境安装完成！"
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志: $LOG_FILE"; exit 1' ERR

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
