# jCloud项目 Rocky Linux 9.4 环境安装指南

本指南提供了在Rocky Linux 9.4 x86_64系统上安装和配置jCloud项目运行环境的完整解决方案。

## 📋 脚本概览

| 脚本名称 | 功能描述 | 执行时间 |
|---------|---------|---------|
| `install-rocky-linux.sh` | 一键安装所有必需组件 | 15-30分钟 |
| `optimize-rocky-linux.sh` | 系统性能优化配置 | 5-10分钟 |
| `verify-environment.sh` | 环境安装验证检查 | 2-5分钟 |

## 🚀 快速开始

### 1. 系统要求

- **操作系统**: Rocky Linux 9.4 x86_64
- **内存**: 至少 2GB RAM
- **磁盘**: 至少 20GB 可用空间
- **网络**: 稳定的互联网连接
- **权限**: root 或 sudo 权限

### 2. 安装步骤

#### 步骤1: 下载脚本

```bash
# 克隆项目或下载脚本文件
git clone <jcloud-repo-url>
cd jcloud/scripts

# 或者直接下载脚本文件到服务器
```

#### 步骤2: 执行一键安装

```bash
# 给脚本执行权限
chmod +x *.sh

# 执行一键安装（需要root权限）
sudo ./install-rocky-linux.sh
```

#### 步骤3: 系统优化（可选）

```bash
# 执行系统优化
sudo ./optimize-rocky-linux.sh

# 重启系统以使优化生效
sudo reboot
```

#### 步骤4: 验证安装

```bash
# 验证环境安装
sudo ./verify-environment.sh
```

## 📦 安装的组件

### 核心组件

| 组件 | 版本 | 端口 | 用途 |
|------|------|------|------|
| **Java JDK** | 21 | - | 后端运行环境 |
| **Maven** | 3.9+ | - | Java项目构建 |
| **Node.js** | 18+ | - | 前端构建环境 |
| **pnpm** | 最新 | - | 前端包管理 |
| **Nginx** | 最新 | 80, 5174 | Web服务器和反向代理 |
| **MySQL** | 8.0+ | 3306 | 主数据库 |
| **Redis** | 7.0+ | 6379 | 缓存和会话存储 |

### 系统配置

- **防火墙**: 自动配置必要端口
- **用户**: 创建 `jcloud` 系统用户
- **目录**: 创建 `/opt/jcloud` 安装目录
- **服务**: 所有服务设置为开机自启

## 🔧 配置信息

### 数据库配置

```yaml
# MySQL配置
Host: localhost
Port: 3306
Root Password: jcloud123456
Databases: cs2_skin_platform, vimbox
User: voltskins
Password: ShbAeEVw7RNh8arDzjN4eZhsh@
```

### Redis配置

```yaml
# Redis配置
Host: localhost
Port: 6379
Password: DAANFtJj3n5PtbM8zDkPwh5PG
Database: 10
```

### 端口配置

```yaml
# 服务端口
Frontend: 5174
Backend API: 8081
Nginx: 80
MySQL: 3306
Redis: 6379
```

## 📁 目录结构

```
/opt/jcloud/          # jCloud安装目录
├── logs/             # 应用日志目录
├── config/           # 配置文件目录
└── data/             # 数据文件目录

/var/log/             # 系统日志
├── jcloud-install.log    # 安装日志
├── jcloud-optimize.log   # 优化日志
└── nginx/            # Nginx日志目录
```

## 🔍 验证检查项

验证脚本会检查以下项目：

### ✅ 系统检查
- 操作系统版本
- 系统架构
- 系统资源（内存、磁盘、CPU）

### ✅ 软件检查
- Java 21 安装和配置
- Maven 安装和版本
- Node.js 和 pnpm 安装
- 环境变量配置

### ✅ 服务检查
- Nginx 运行状态
- MySQL 运行状态和连接
- Redis 运行状态和连接
- 防火墙配置

### ✅ 网络检查
- 端口监听状态
- 防火墙规则
- 网络连接

## 🛠️ 故障排除

### 常见问题

#### 1. 安装失败

```bash
# 查看安装日志
sudo tail -f /var/log/jcloud-install.log

# 重新运行安装
sudo ./install-rocky-linux.sh
```

#### 2. 服务启动失败

```bash
# 检查服务状态
sudo systemctl status nginx mysqld redis

# 查看服务日志
sudo journalctl -u nginx -f
sudo journalctl -u mysqld -f
sudo journalctl -u redis -f

# 重启服务
sudo systemctl restart nginx mysqld redis
```

#### 3. 端口冲突

```bash
# 检查端口占用
sudo netstat -tlnp | grep -E ":80|:3306|:6379|:8081|:5174"

# 停止冲突服务
sudo systemctl stop <conflicting-service>
```

#### 4. 防火墙问题

```bash
# 检查防火墙状态
sudo firewall-cmd --list-all

# 手动开放端口
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --reload
```

### 日志文件位置

```bash
# 安装日志
/var/log/jcloud-install.log

# 优化日志
/var/log/jcloud-optimize.log

# 系统服务日志
sudo journalctl -u nginx
sudo journalctl -u mysqld
sudo journalctl -u redis
```

## 📈 性能优化

系统优化脚本包含以下优化：

### 内核参数优化
- 网络性能优化
- 文件系统优化
- 虚拟内存优化
- 安全参数优化

### 应用优化
- MySQL 性能调优
- Redis 配置优化
- Nginx 性能优化
- 系统限制调整

### 监控工具
- htop - 进程监控
- iotop - IO监控
- nethogs - 网络监控
- sysstat - 系统统计

## 🚀 部署jCloud项目

环境安装完成后，按照以下步骤部署jCloud项目：

### 1. 构建后端

```bash
cd /path/to/jcloud/backend
mvn clean package -Pprod
```

### 2. 构建前端

```bash
cd /path/to/jcloud/frontend
pnpm install
pnpm build
```

### 3. 部署后端

```bash
# 启动后端服务
java -jar target/jcloud-admin-*.jar --spring.profiles.active=prod
```

### 4. 配置Nginx

```bash
# 使用项目提供的Nginx配置
sudo cp frontend/nginx-prod.conf /etc/nginx/nginx.conf
sudo nginx -t
sudo systemctl reload nginx
```

### 5. 验证部署

```bash
# 检查后端健康状态
curl http://localhost:8081/api/actuator/health

# 检查前端页面
curl http://localhost/

# 检查API代理
curl http://localhost/api/auth/captcha/generate
```

## 📞 技术支持

如果在安装过程中遇到问题，请：

1. 查看相关日志文件
2. 运行验证脚本检查环境
3. 参考故障排除部分
4. 联系技术支持团队

---

**注意**: 本安装脚本专为Rocky Linux 9.4设计，其他Linux发行版可能需要调整。
