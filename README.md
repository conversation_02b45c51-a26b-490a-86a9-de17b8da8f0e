# jCloud企业级权限管理系统

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.1-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![sa-token](https://img.shields.io/badge/sa--token-1.37.0-blue.svg)](https://sa-token.cc/)
[![MyBatis-Flex](https://img.shields.io/badge/MyBatis--Flex-1.8.0-orange.svg)](https://mybatis-flex.com/)
[![Java](https://img.shields.io/badge/Java-21-red.svg)](https://openjdk.java.net/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目简介

jCloud是一个基于Spring Boot 3 + sa-token + MyBatis-Flex构建的现代化企业级权限管理系统，采用RBAC权限模型，支持多租户架构和高并发场景。

## ✨ 特性

- 🚀 **现代化技术栈**：Spring Boot 3 + Java 21 + sa-token + MyBatis-Flex
- 🏢 **多租户架构**：完整的多租户数据隔离和权限管理
- 🔐 **RBAC权限模型**：用户-角色-权限的经典权限管理模式
- ⚡ **高性能**：经过压力测试，支持高并发访问（TPS 36+）
- 🛡️ **安全可靠**：BCrypt密码加密，sa-token权限验证
- 📱 **API优先**：完整的RESTful API设计
- 📚 **完整文档**：Knife4j API文档自动生成

## 🏗️ 系统架构

```
jCloud企业级权限管理系统
├── jcloud-common     # 公共模块（实体类、工具类、配置等）
├── jcloud-auth       # 认证模块（登录、权限验证等）
└── jcloud-admin      # 管理模块（用户、角色、权限管理）
```

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 3.2.1
- **权限**: sa-token 1.37.0
- **ORM**: MyBatis-Flex 1.8.0
- **数据库**: MySQL 8.0
- **连接池**: Druid
- **缓存**: Redis
- **文档**: Knife4j (Swagger)
- **构建**: Maven 3.9+

### 开发环境
- **JDK**: OpenJDK 21
- **IDE**: IntelliJ IDEA
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

## 🚀 快速开始

### 环境要求
- JDK 21+
- Maven 3.9+
- MySQL 8.0+
- Redis 6.0+

### 1. 克隆项目
```bash
git clone https://github.com/your-username/jcloud.git
cd jcloud
```

### 2. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE jcloud DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据库脚本
mysql -u root -p jcloud < backend/jcloud-admin/src/main/resources/sql/schema.sql
mysql -u root -p jcloud < backend/jcloud-admin/src/main/resources/sql/data.sql
```

### 3. 配置文件
修改 `backend/jcloud-admin/src/main/resources/application-dev.yml`：
```yaml
spring:
  datasource:
    url: **********************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
```

### 4. 启动应用
```bash
cd backend
mvn clean install
cd jcloud-admin
mvn spring-boot:run
```

### 5. 访问应用
- API文档: http://localhost:8080/api/doc.html
- 默认账号: admin / 123456

## 📚 API文档

启动应用后，访问 [http://localhost:8080/api/doc.html](http://localhost:8080/api/doc.html) 查看完整的API文档。

### 主要接口
- **认证管理**: `/api/auth/*` - 登录、登出、权限验证
- **用户管理**: `/api/system/user/*` - 用户CRUD操作
- **角色管理**: `/api/system/role/*` - 角色CRUD操作
- **权限管理**: `/api/system/permission/*` - 权限CRUD操作

## 🔐 权限设计

### RBAC模型
```
用户(User) ←→ 用户角色(UserRole) ←→ 角色(Role) ←→ 角色权限(RolePermission) ←→ 权限(Permission)
```

### 多租户隔离
- 每个租户拥有独立的数据空间
- 权限验证时自动加入租户ID过滤
- 支持租户级别的数据隔离

### 权限验证
使用sa-token进行权限验证：
```java
@SaCheckPermission("system:user:list")
@GetMapping("/page")
public Result<PageResult<SysUser>> pageUsers() {
    // 业务逻辑
}
```

## 📊 性能测试

系统经过完整的压力测试，性能表现优秀：

| 并发用户数 | 总请求数 | 成功率 | 平均响应时间 | TPS |
|-----------|----------|--------|-------------|-----|
| 5用户     | 265      | 100%   | 471.54ms    | 8.83|
| 10用户    | 538      | 100%   | 459.80ms    | 17.93|
| 20用户    | 1098     | 100%   | 451.70ms    | 36.60|

### 测试环境
- **硬件**: 标准开发环境
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **测试工具**: 自定义Python压力测试脚本

## 🗂️ 项目结构

```
jcloud/
├── backend/                    # 后端代码
│   ├── jcloud-common/         # 公共模块
│   │   ├── src/main/java/com/jcloud/common/
│   │   │   ├── entity/        # 实体类
│   │   │   ├── mapper/        # MyBatis Mapper
│   │   │   ├── util/          # 工具类
│   │   │   ├── config/        # 配置类
│   │   │   └── constant/      # 常量定义
│   │   └── pom.xml
│   ├── jcloud-auth/           # 认证模块
│   │   ├── src/main/java/com/jcloud/auth/
│   │   │   ├── controller/    # 认证控制器
│   │   │   ├── service/       # 认证服务
│   │   │   └── dto/           # 数据传输对象
│   │   └── pom.xml
│   ├── jcloud-admin/          # 管理模块
│   │   ├── src/main/java/com/jcloud/admin/
│   │   │   ├── controller/    # 管理控制器
│   │   │   ├── service/       # 业务服务
│   │   │   └── config/        # 配置类
│   │   ├── src/main/resources/
│   │   │   ├── sql/           # 数据库脚本
│   │   │   └── application.yml
│   │   └── pom.xml
│   └── pom.xml                # 父POM
├── .gitignore                 # Git忽略文件
└── README.md                  # 项目说明
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目地址: [https://github.com/your-username/jcloud](https://github.com/your-username/jcloud)
- 问题反馈: [Issues](https://github.com/your-username/jcloud/issues)

## 🙏 致谢

感谢以下开源项目的支持：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [sa-token](https://sa-token.cc/)
- [MyBatis-Flex](https://mybatis-flex.com/)
- [Knife4j](https://doc.xiaominfo.com/)

### 后端技术栈
- **框架**: Spring Boot 3.2+ (Java 21)
- **权限管理**: sa-token
- **数据访问**: MyBatis-Flex
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **工具库**: Hutool, Lombok
- **API文档**: Knife4j
- **定时任务**: XXL-JOB
- **构建工具**: Maven 3.9+

### 前端技术栈
- **框架**: React 18+
- **状态管理**: Zustand
- **样式框架**: Tailwind CSS
- **组件库**: Shadcn UI
- **HTTP客户端**: Axios
- **数据获取**: React Query
- **路由**: React Router v6
- **表单验证**: React Hook Form + Zod
- **构建工具**: Vite

## 项目结构
```
jCloud/
├── backend/                    # 后端项目
│   ├── jcloud-admin/          # 管理后台服务
│   ├── jcloud-auth/           # 认证授权服务
│   ├── jcloud-common/         # 公共模块
│   └── pom.xml               # 父级POM
├── frontend/                  # 前端项目
│   ├── admin-web/            # 管理后台前端
│   └── package.json
├── docs/                     # 项目文档
├── scripts/                  # 部署脚本
└── docker/                   # Docker配置
```

## 核心功能
- 🔐 用户认证与授权
- 👥 用户管理
- 🎭 角色管理
- 🔑 权限管理
- 📋 菜单管理
- 🏢 多租户支持
- 📊 操作日志
- ⚙️ 系统配置

## 快速开始

### 环境要求
- JDK 21+
- Node.js 18+
- MySQL 8.0+
- Redis 7.0+
- Maven 3.9+

### 后端启动
```bash
cd backend
mvn clean install
mvn spring-boot:run -pl jcloud-admin
```

### 前端启动
```bash
cd frontend/admin-web
npm install
npm run dev
```

## 开发规范
- 代码注释使用中文
- 遵循阿里巴巴Java开发手册
- 前端遵循React最佳实践
- 数据库表名使用下划线命名
- API接口遵循RESTful规范

## 许可证
MIT License
